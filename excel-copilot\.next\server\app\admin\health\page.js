(()=>{var e={};e.id=6758,e.ids=[6758],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},61024:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o}),r(29381),r(65675),r(12523);var s=r(23191),a=r(88716),l=r(37922),i=r.n(l),n=r(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o=["",{children:["admin",{children:["health",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29381)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\admin\\health\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\admin\\health\\page.tsx"],u="/admin/health/page",x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/health/page",pathname:"/admin/health",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},80685:(e,t,r)=>{Promise.resolve().then(r.bind(r,6513))},37202:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48998:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58038:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},91470:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},6513:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(10326),a=r(76557);let l=(0,a.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var i=r(58038);let n=(0,a.Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var d=r(28916);let o=(0,a.Z)("Plug",[["path",{d:"M12 22v-5",key:"1ega77"}],["path",{d:"M9 8V2",key:"14iosj"}],["path",{d:"M15 8V2",key:"18g5xt"}],["path",{d:"M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z",key:"osxo6l"}]]),c=(0,a.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var u=r(37202),x=r(91470),m=r(48998);let p=(0,a.Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),h=(0,a.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var f=r(17577),v=r(38443),y=r(91664),g=r(29752);r(60962);var j=r(48051),N=Symbol("radix.slottable");function b(e){return f.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===N}var w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=f.forwardRef((e,t)=>{let{children:r,...s}=e;if(f.isValidElement(r)){let e,a;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,i=function(e,t){let r={...t};for(let s in t){let a=e[s],l=t[s];/^on[A-Z]/.test(s)?a&&l?r[s]=(...e)=>{let t=l(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...l}:"className"===s&&(r[s]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==f.Fragment&&(i.ref=t?(0,j.F)(t,l):l),f.cloneElement(r,i)}return f.Children.count(r)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=f.forwardRef((e,r)=>{let{children:a,...l}=e,i=f.Children.toArray(a),n=i.find(b);if(n){let e=n.props.children,a=i.map(t=>t!==n?t:f.Children.count(e)>1?f.Children.only(null):f.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...l,ref:r,children:f.isValidElement(e)?f.cloneElement(e,void 0,a):null})}return(0,s.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),a=f.forwardRef((e,a)=>{let{asChild:l,...i}=e,n=l?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(n,{...i,ref:a})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),k="horizontal",C=["horizontal","vertical"],M=f.forwardRef((e,t)=>{let{decorative:r,orientation:a=k,...l}=e,i=C.includes(a)?a:k;return(0,s.jsx)(w.div,{"data-orientation":i,...r?{role:"none"}:{"aria-orientation":"vertical"===i?i:void 0,role:"separator"},...l,ref:t})});M.displayName="Separator";var P=r(51223);let Z=f.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...a},l)=>s.jsx(M,{ref:l,decorative:r,orientation:t,className:(0,P.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));Z.displayName=M.displayName;let q={database:l,auth:i.Z,ai:n,stripe:d.Z,mcp:o},R={healthy:"default",degraded:"secondary",unhealthy:"destructive",unknown:"outline"};function _(){let[e,t]=(0,f.useState)(null),[r,a]=(0,f.useState)(!0),[l,i]=(0,f.useState)(null),[n,d]=(0,f.useState)(!0),o=async()=>{try{a(!0);let e=await fetch("/api/health"),r=await e.json();t(r),i(new Date)}catch(e){console.error("Failed to fetch health data:",e)}finally{a(!1)}},j=e=>{switch(e){case"healthy":return s.jsx(c,{className:"h-4 w-4 text-green-500"});case"degraded":return s.jsx(u.Z,{className:"h-4 w-4 text-yellow-500"});case"unhealthy":return s.jsx(x.Z,{className:"h-4 w-4 text-red-500"});default:return s.jsx(m.Z,{className:"h-4 w-4 text-gray-500"})}},N=e=>{let t=q[e]||p;return s.jsx(t,{className:"h-4 w-4"})},b=e=>e<1e3?`${e}ms`:`${(e/1e3).toFixed(2)}s`,w=e=>e<100?"Excelente":e<500?"Bom":e<1e3?"Regular":e<3e3?"Lento":"Cr\xedtico";return(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold",children:"\uD83C\uDFE5 Health Dashboard"}),s.jsx("p",{className:"text-muted-foreground",children:"Monitoramento em tempo real da sa\xfade do sistema"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(y.Button,{variant:"outline",size:"sm",onClick:()=>d(!n),children:[n?"Pausar":"Retomar"," Auto-refresh"]}),(0,s.jsxs)(y.Button,{variant:"outline",size:"sm",onClick:o,disabled:r,children:[s.jsx(h,{className:`h-4 w-4 mr-2 ${r?"animate-spin":""}`}),"Atualizar"]})]})]}),e&&(0,s.jsxs)(g.Zb,{children:[(0,s.jsxs)(g.Ol,{children:[(0,s.jsxs)(g.ll,{className:"flex items-center gap-2",children:[j(e.overall),"Status Geral do Sistema"]}),(0,s.jsxs)(g.SZ,{children:["\xdaltima atualiza\xe7\xe3o: ",l?.toLocaleString()||"Nunca"]})]}),s.jsx(g.aY,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.summary.healthy}),s.jsx("div",{className:"text-sm text-muted-foreground",children:"Saud\xe1veis"})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:e.summary.degraded}),s.jsx("div",{className:"text-sm text-muted-foreground",children:"Degradados"})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-red-600",children:e.summary.unhealthy}),s.jsx("div",{className:"text-sm text-muted-foreground",children:"N\xe3o Saud\xe1veis"})]}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold",children:b(e.responseTime)}),s.jsx("div",{className:"text-sm text-muted-foreground",children:"Tempo Total"})]})]})})]}),e&&s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.services.map(e=>(0,s.jsxs)(g.Zb,{children:[s.jsx(g.Ol,{className:"pb-3",children:(0,s.jsxs)(g.ll,{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[N(e.service),s.jsx("span",{className:"capitalize",children:e.service})]}),j(e.status)]})}),(0,s.jsxs)(g.aY,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Status:"}),s.jsx(v.C,{variant:R[e.status],children:e.status})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Tempo de Resposta:"}),s.jsx("span",{className:"text-sm font-mono",children:b(e.responseTime)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Performance:"}),s.jsx("span",{className:"text-sm",children:w(e.responseTime)})]}),e.details&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(Z,{}),(0,s.jsxs)("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs text-muted-foreground",children:"Detalhes:"}),e.details.message&&s.jsx("div",{className:"text-xs",children:e.details.message}),e.details.mode&&(0,s.jsxs)("div",{className:"text-xs",children:["Modo: ",e.details.mode]}),e.details.providers&&(0,s.jsxs)("div",{className:"text-xs",children:["Providers: ",e.details.providers.join(", ")]})]})]})]})]},e.service))}),r&&!e&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[s.jsx(h,{className:"h-8 w-8 animate-spin"}),s.jsx("span",{className:"ml-2",children:"Carregando dados de sa\xfade..."})]}),!r&&!e&&s.jsx(g.Zb,{children:s.jsx(g.aY,{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx(x.Z,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Erro ao carregar dados de sa\xfade"}),s.jsx("p",{className:"text-muted-foreground mb-4",children:"N\xe3o foi poss\xedvel conectar com os servi\xe7os de health check."}),s.jsx(y.Button,{onClick:o,children:"Tentar Novamente"})]})})}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[s.jsx("p",{children:"Dashboard de Health Checks • Excel Copilot v1.0.0"}),s.jsx("p",{children:"Atualiza\xe7\xe3o autom\xe1tica a cada 30 segundos"})]})]})}},38443:(e,t,r)=>{"use strict";r.d(t,{C:()=>n});var s=r(10326),a=r(79360);r(17577);var l=r(51223);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...r}){return s.jsx("div",{className:(0,l.cn)(i({variant:t}),e),...r})}},29752:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>u,Zb:()=>d,aY:()=>x,eW:()=>m,ll:()=>c});var s=r(10326),a=r(31722),l=r(17577),i=r(45365),n=r(51223);let d=(0,l.forwardRef)(({className:e,children:t,hoverable:r=!1,variant:l="default",noPadding:d=!1,animated:o=!1,...c},u)=>{let x=(0,n.cn)("rounded-xl border shadow-sm",{"p-6":!d,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":r&&!o,"border-border bg-card":"default"===l,"border-border/50 bg-transparent":"outline"===l,"bg-card/90 backdrop-blur-md border-border/50":"glass"===l,"bg-gradient-primary text-primary-foreground border-none":"gradient"===l},e);return o?s.jsx(a.E.div,{ref:u,className:x,...(0,i.Ph)("card"),whileHover:r?i.q.hover:void 0,whileTap:r?i.q.tap:void 0,...c,children:t}):s.jsx("div",{ref:u,className:x,...c,children:t})});d.displayName="Card";let o=(0,l.forwardRef)(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("mb-4 flex flex-col space-y-1.5",e),...t}));o.displayName="CardHeader";let c=(0,l.forwardRef)(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let u=(0,l.forwardRef)(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));u.displayName="CardDescription";let x=(0,l.forwardRef)(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("card-content",e),...t}));x.displayName="CardContent";let m=(0,l.forwardRef)(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center pt-4 mt-auto",e),...t}));m.displayName="CardFooter"},38238:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},29381:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var s=r(68570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\admin\health\page.tsx`),{__esModule:l,$$typeof:i}=a;a.default;let n=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\admin\health\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,9557,7410,86,7915,2972,4433,6841],()=>r(61024));module.exports=s})();