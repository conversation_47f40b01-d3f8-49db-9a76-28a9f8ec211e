(()=>{var e={};e.id=1100,e.ids=[1100],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},47901:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(39396),t(65675),t(12523);var a=t(23191),r=t(88716),i=t(37922),o=t.n(i),n=t(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let d=["",{children:["api-docs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,39396)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api-docs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],c=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api-docs\\page.tsx"],u="/api-docs/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/api-docs/page",pathname:"/api-docs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},36464:(e,s,t)=>{Promise.resolve().then(t.bind(t,83716))},87888:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},941:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},43810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},94893:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},88307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},83716:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(10326),r=t(17577),i=t(29752),o=t(38443),n=t(91664),l=t(99744),d=t(41190),c=t(27299),u=t(82015),m=t(88307),p=t(76557);let x=(0,p.Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var h=t(87888),f=t(941),g=t(39183);let v=(0,p.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var b=t(43810),y=t(94893),j=t(85999);let N=[{path:"/api/auth/login",method:"POST",summary:"Login de usu\xe1rio",description:"Autentica um usu\xe1rio e retorna token de acesso",tags:["Auth"],requiresAuth:!1,requestBody:{type:"application/json",example:JSON.stringify({email:"<EMAIL>",password:"password123"},null,2)},responses:[{status:200,description:"Login realizado com sucesso",example:JSON.stringify({success:!0,token:"jwt_token_here",user:{id:"1",email:"<EMAIL>"}},null,2)},{status:401,description:"Credenciais inv\xe1lidas",example:JSON.stringify({success:!1,error:"Credenciais inv\xe1lidas"},null,2)}]},{path:"/api/workbooks",method:"GET",summary:"Listar planilhas",description:"Lista todas as planilhas do usu\xe1rio autenticado",tags:["Workbooks"],requiresAuth:!0,parameters:[{name:"page",type:"number",required:!1,description:"N\xfamero da p\xe1gina (padr\xe3o: 1)"},{name:"limit",type:"number",required:!1,description:"Itens por p\xe1gina (padr\xe3o: 10)"}],responses:[{status:200,description:"Lista de planilhas retornada com sucesso",example:JSON.stringify({success:!0,data:[{id:"1",name:"Vendas Q1",createdAt:"2025-06-18T10:00:00Z"},{id:"2",name:"Relat\xf3rio Mensal",createdAt:"2025-06-17T15:30:00Z"}],pagination:{page:1,limit:10,total:2}},null,2)}]},{path:"/api/workbooks",method:"POST",summary:"Criar planilha",description:"Cria uma nova planilha para o usu\xe1rio autenticado",tags:["Workbooks"],requiresAuth:!0,requestBody:{type:"application/json",example:JSON.stringify({name:"Nova Planilha",description:"Descri\xe7\xe3o da planilha",template:"blank"},null,2)},responses:[{status:201,description:"Planilha criada com sucesso",example:JSON.stringify({success:!0,data:{id:"3",name:"Nova Planilha",description:"Descri\xe7\xe3o da planilha",createdAt:"2025-06-18T12:00:00Z"}},null,2)}]},{path:"/api/chat",method:"POST",summary:"Chat com IA",description:"Envia mensagem para processamento por IA e manipula\xe7\xe3o de planilha",tags:["AI"],requiresAuth:!0,requestBody:{type:"application/json",example:JSON.stringify({message:"Adicione uma coluna Total que some as colunas A e B",workbookId:"1",context:"spreadsheet"},null,2)},responses:[{status:200,description:"Comando processado com sucesso",example:JSON.stringify({success:!0,data:{response:"Coluna Total adicionada com sucesso!",operations:["add_column"],result:{columnAdded:"C",formula:"=A1+B1"}}},null,2)}]},{path:"/api/excel/analyze",method:"POST",summary:"Analisar dados",description:"Analisa dados da planilha para obter insights e estat\xedsticas",tags:["Excel"],requiresAuth:!0,requestBody:{type:"application/json",example:JSON.stringify({workbookId:"1",sheetName:"Sheet1",range:"A1:C10",analysisType:"statistics"},null,2)},responses:[{status:200,description:"An\xe1lise conclu\xedda com sucesso",example:JSON.stringify({success:!0,data:{statistics:{mean:45.6,median:42,mode:40,stdDev:12.3},insights:["Dados mostram tend\xeancia crescente","Outliers detectados na linha 7"],charts:[{type:"line",data:"..."}]}},null,2)}]},{path:"/api/metrics",method:"GET",summary:"M\xe9tricas do Sistema",description:"Obt\xe9m m\xe9tricas de desempenho da aplica\xe7\xe3o (apenas administradores)",tags:["Admin"],requiresAuth:!0,adminOnly:!0,responses:[{status:200,description:"M\xe9tricas retornadas com sucesso",example:JSON.stringify({success:!0,data:{activeUsers:1250,totalWorkbooks:5430,apiCalls:125e3,uptime:"99.9%"}},null,2)}]}],w={GET:"bg-green-100 text-green-800 border-green-200",POST:"bg-blue-100 text-blue-800 border-blue-200",PUT:"bg-yellow-100 text-yellow-800 border-yellow-200",PATCH:"bg-orange-100 text-orange-800 border-orange-200",DELETE:"bg-red-100 text-red-800 border-red-200"};function C(){let[e,s]=(0,r.useState)(""),[t,p]=(0,r.useState)("all"),[C,k]=(0,r.useState)(null),[q,P]=(0,r.useState)({}),A=["all",...Array.from(new Set(N.flatMap(e=>e.tags)))],T=N.filter(s=>{let a=s.path.toLowerCase().includes(e.toLowerCase())||s.summary.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase()),r="all"===t||s.tags.includes(t);return a&&r}),S=e=>{navigator.clipboard.writeText(e),j.toast.success("Copiado para a \xe1rea de transfer\xeancia!")},R=async e=>{q[`${e.method}-${e.path}`]||e.requestBody?.example;try{j.toast.loading("Testando endpoint...",{id:"test-endpoint"}),await new Promise(e=>setTimeout(e,1e3)),j.toast.success("Teste realizado com sucesso!",{id:"test-endpoint",description:"Verifique a resposta abaixo"})}catch(e){j.toast.error("Erro no teste do endpoint",{id:"test-endpoint"})}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Documenta\xe7\xe3o da API"}),a.jsx("p",{className:"text-xl text-muted-foreground mb-6",children:"Explore e teste todos os endpoints da API do Excel Copilot"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[a.jsx(m.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),a.jsx(d.I,{placeholder:"Pesquisar endpoints...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]}),a.jsx("div",{className:"flex gap-2 flex-wrap",children:A.map(e=>a.jsx(n.Button,{variant:t===e?"default":"outline",size:"sm",onClick:()=>p(e),children:"all"===e?"Todos":e},e))})]}),a.jsx(i.Zb,{className:"mb-6",children:a.jsx(i.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-2",children:"Base URL"}),a.jsx("code",{className:"text-sm bg-muted p-2 rounded block",children:"https://excel-copilot-eight.vercel.app"})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-2",children:"Autentica\xe7\xe3o"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Bearer Token via header Authorization"})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold mb-2",children:"Formato"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"JSON (application/json)"})]})]})})})]}),a.jsx("div",{className:"space-y-4",children:T.map((e,s)=>{let t=`${e.method}-${e.path}`,r=C===t;return(0,a.jsxs)(i.Zb,{className:"overflow-hidden",children:[a.jsx(i.Ol,{className:"cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>k(r?null:t),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx(o.C,{className:`${w[e.method]} border`,children:e.method}),(0,a.jsxs)("div",{children:[a.jsx(i.ll,{className:"text-lg",children:e.path}),a.jsx(i.SZ,{children:e.summary})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.requiresAuth&&a.jsx(x,{className:"h-4 w-4 text-amber-500"}),e.adminOnly&&a.jsx(h.Z,{className:"h-4 w-4 text-red-500"}),r?a.jsx(f.Z,{className:"h-4 w-4"}):a.jsx(g.Z,{className:"h-4 w-4"})]})]})}),r&&a.jsx(i.aY,{className:"border-t",children:(0,a.jsxs)(l.mQ,{defaultValue:"overview",className:"w-full",children:[(0,a.jsxs)(l.dr,{className:"grid w-full grid-cols-4",children:[a.jsx(l.SP,{value:"overview",children:"Vis\xe3o Geral"}),a.jsx(l.SP,{value:"parameters",children:"Par\xe2metros"}),a.jsx(l.SP,{value:"responses",children:"Respostas"}),a.jsx(l.SP,{value:"test",children:"Testar"})]}),(0,a.jsxs)(l.nU,{value:"overview",className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Descri\xe7\xe3o"}),a.jsx("p",{className:"text-muted-foreground",children:e.description})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Tags"}),a.jsx("div",{className:"flex gap-2",children:e.tags.map(e=>a.jsx(o.C,{variant:"outline",children:e},e))})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Autentica\xe7\xe3o"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.requiresAuth?(0,a.jsxs)(o.C,{variant:"destructive",children:[a.jsx(x,{className:"h-3 w-3 mr-1"}),"Requer autentica\xe7\xe3o"]}):(0,a.jsxs)(o.C,{variant:"secondary",children:[a.jsx(v,{className:"h-3 w-3 mr-1"}),"P\xfablico"]}),e.adminOnly&&(0,a.jsxs)(o.C,{variant:"destructive",children:[a.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"Apenas administradores"]})]})]})]}),(0,a.jsxs)(l.nU,{value:"parameters",className:"space-y-4",children:[e.parameters&&e.parameters.length>0?a.jsx("div",{className:"space-y-3",children:e.parameters.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[a.jsx("code",{className:"text-sm font-mono",children:e.name}),a.jsx(o.C,{variant:e.required?"destructive":"secondary",className:"text-xs",children:e.required?"Obrigat\xf3rio":"Opcional"}),a.jsx(o.C,{variant:"outline",className:"text-xs",children:e.type})]}),a.jsx("p",{className:"text-sm text-muted-foreground",children:e.description})]},s))}):a.jsx("p",{className:"text-muted-foreground",children:"Nenhum par\xe2metro necess\xe1rio"}),e.requestBody&&(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold mb-2",children:"Corpo da Requisi\xe7\xe3o"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("pre",{className:"bg-muted p-4 rounded text-sm overflow-x-auto",children:a.jsx("code",{children:e.requestBody.example})}),a.jsx(n.Button,{size:"sm",variant:"outline",className:"absolute top-2 right-2",onClick:()=>S(e.requestBody.example),children:a.jsx(b.Z,{className:"h-3 w-3"})})]})]})]}),a.jsx(l.nU,{value:"responses",className:"space-y-4",children:e.responses?.map((e,s)=>a.jsxs("div",{className:"border rounded p-4",children:[a.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[a.jsx(o.C,{variant:e.status<300?"default":"destructive",className:"text-sm",children:e.status}),a.jsx("span",{className:"font-medium",children:e.description})]}),a.jsxs("div",{className:"relative",children:[a.jsx("pre",{className:"bg-muted p-3 rounded text-sm overflow-x-auto",children:a.jsx("code",{children:e.example})}),a.jsx(n.Button,{size:"sm",variant:"outline",className:"absolute top-2 right-2",onClick:()=>S(e.example),children:a.jsx(b.Z,{className:"h-3 w-3"})})]})]},s))}),a.jsx(l.nU,{value:"test",className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"test-url",children:"URL do Endpoint"}),a.jsx(d.I,{id:"test-url",value:`https://excel-copilot-eight.vercel.app${e.path}`,readOnly:!0,className:"font-mono text-sm"})]}),e.requestBody&&(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"test-body",children:"Corpo da Requisi\xe7\xe3o (JSON)"}),a.jsx(u.Z,{id:"test-body",value:q[t]||e.requestBody.example,onChange:e=>P(s=>({...s,[t]:e.target.value})),className:"font-mono text-sm min-h-[200px]"})]}),(0,a.jsxs)(n.Button,{onClick:()=>R(e),className:"w-full",children:[a.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Testar Endpoint"]}),a.jsx("div",{className:"text-sm text-muted-foreground",children:(0,a.jsxs)("p",{children:["\uD83D\uDCA1 ",a.jsx("strong",{children:"Dica:"})," Para testar endpoints autenticados, certifique-se de estar logado na aplica\xe7\xe3o."]})})]})})]})})]},s)})}),0===T.length&&a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Nenhum endpoint encontrado com os filtros aplicados."})})]})}},38443:(e,s,t)=>{"use strict";t.d(s,{C:()=>n});var a=t(10326),r=t(79360);t(17577);var i=t(51223);let o=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...t}){return a.jsx("div",{className:(0,i.cn)(o({variant:s}),e),...t})}},29752:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>d,SZ:()=>u,Zb:()=>l,aY:()=>m,eW:()=>p,ll:()=>c});var a=t(10326),r=t(31722),i=t(17577),o=t(45365),n=t(51223);let l=(0,i.forwardRef)(({className:e,children:s,hoverable:t=!1,variant:i="default",noPadding:l=!1,animated:d=!1,...c},u)=>{let m=(0,n.cn)("rounded-xl border shadow-sm",{"p-6":!l,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":t&&!d,"border-border bg-card":"default"===i,"border-border/50 bg-transparent":"outline"===i,"bg-card/90 backdrop-blur-md border-border/50":"glass"===i,"bg-gradient-primary text-primary-foreground border-none":"gradient"===i},e);return d?a.jsx(r.E.div,{ref:u,className:m,...(0,o.Ph)("card"),whileHover:t?o.q.hover:void 0,whileTap:t?o.q.tap:void 0,...c,children:s}):a.jsx("div",{ref:u,className:m,...c,children:s})});l.displayName="Card";let d=(0,i.forwardRef)(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("mb-4 flex flex-col space-y-1.5",e),...s}));d.displayName="CardHeader";let c=(0,i.forwardRef)(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let u=(0,i.forwardRef)(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));u.displayName="CardDescription";let m=(0,i.forwardRef)(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("card-content",e),...s}));m.displayName="CardContent";let p=(0,i.forwardRef)(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("flex items-center pt-4 mt-auto",e),...s}));p.displayName="CardFooter"},62734:(e,s,t)=>{"use strict";t.d(s,{RM:()=>l,aF:()=>d});var a=t(17577),r=t.n(a),i=t(51223);let o={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},n={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function l(e="default",s="md",t=!1,a){return(0,i.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o[e],n[s],t&&"min-h-[80px] resize-vertical",a)}function d(e,s){return s?r().createElement("div",{className:s},e):e}},41190:(e,s,t)=>{"use strict";t.d(s,{I:()=>o,Z:()=>n});var a=t(10326),r=t(17577),i=t(62734);let o=r.forwardRef(({className:e,type:s,wrapperClassName:t,variant:r="default",fieldSize:o="md",inputSize:n,...l},d)=>{let c=a.jsx("input",{type:s,className:(0,i.RM)(r,n||o,!1,e),ref:d,...l});return(0,i.aF)(c,t)});o.displayName="Input";let n=o},27299:(e,s,t)=>{"use strict";t.d(s,{_:()=>c});var a=t(10326),r=t(17577),i=t(45226),o=r.forwardRef((e,s)=>(0,a.jsx)(i.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));o.displayName="Label";var n=t(79360),l=t(51223);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>a.jsx(o,{ref:t,className:(0,l.cn)(d(),e),...s}));c.displayName=o.displayName},99744:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>T,nU:()=>M,dr:()=>S,SP:()=>R});var a=t(10326),r=t(17577),i=t(82561),o=t(93095),n=t(15594),l=t(9815),d=t(45226),c=t(17124),u=t(52067),m=t(88957),p="Tabs",[x,h]=(0,o.b)(p,[n.Pc]),f=(0,n.Pc)(),[g,v]=x(p),b=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,onValueChange:i,defaultValue:o,orientation:n="horizontal",dir:l,activationMode:x="automatic",...h}=e,f=(0,c.gm)(l),[v,b]=(0,u.T)({prop:r,onChange:i,defaultProp:o??"",caller:p});return(0,a.jsx)(g,{scope:t,baseId:(0,m.M)(),value:v,onValueChange:b,orientation:n,dir:f,activationMode:x,children:(0,a.jsx)(d.WV.div,{dir:f,"data-orientation":n,...h,ref:s})})});b.displayName=p;var y="TabsList",j=r.forwardRef((e,s)=>{let{__scopeTabs:t,loop:r=!0,...i}=e,o=v(y,t),l=f(t);return(0,a.jsx)(n.fC,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:r,children:(0,a.jsx)(d.WV.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:s})})});j.displayName=y;var N="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,disabled:o=!1,...l}=e,c=v(N,t),u=f(t),m=q(c.baseId,r),p=P(c.baseId,r),x=r===c.value;return(0,a.jsx)(n.ck,{asChild:!0,...u,focusable:!o,active:x,children:(0,a.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":p,"data-state":x?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:m,...l,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;x||o||!e||c.onValueChange(r)})})})});w.displayName=N;var C="TabsContent",k=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,forceMount:o,children:n,...c}=e,u=v(C,t),m=q(u.baseId,i),p=P(u.baseId,i),x=i===u.value,h=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.z,{present:o||x,children:({present:t})=>(0,a.jsx)(d.WV.div,{"data-state":x?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:p,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&n})})});function q(e,s){return`${e}-trigger-${s}`}function P(e,s){return`${e}-content-${s}`}k.displayName=C;var A=t(51223);let T=b,S=r.forwardRef(({className:e,...s},t)=>a.jsx(j,{ref:t,className:(0,A.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));S.displayName=j.displayName;let R=r.forwardRef(({className:e,...s},t)=>a.jsx(w,{ref:t,className:(0,A.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));R.displayName=w.displayName;let M=r.forwardRef(({className:e,...s},t)=>a.jsx(k,{ref:t,className:(0,A.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));M.displayName=k.displayName},82015:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var a=t(10326),r=t(17577),i=t(62734);let o=r.forwardRef(({className:e,wrapperClassName:s,variant:t="default",fieldSize:r="md",textareaSize:o,...n},l)=>{let d=a.jsx("textarea",{className:(0,i.RM)(t,o||r,!0,e),ref:l,...n});return(0,i.aF)(d,s)});o.displayName="Textarea";let n=o},38238:(e,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"ReflectAdapter",{enumerable:!0,get:function(){return t}});class t{static get(e,s,t){let a=Reflect.get(e,s,t);return"function"==typeof a?a.bind(e):a}static set(e,s,t,a){return Reflect.set(e,s,t,a)}static has(e,s){return Reflect.has(e,s)}static deleteProperty(e,s){return Reflect.deleteProperty(e,s)}}},39396:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>o,__esModule:()=>i,default:()=>n});var a=t(68570);let r=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\api-docs\page.tsx`),{__esModule:i,$$typeof:o}=r;r.default;let n=(0,a.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\api-docs\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,9557,7410,86,7915,5999,2972,4433,6841],()=>t(47901));module.exports=a})();