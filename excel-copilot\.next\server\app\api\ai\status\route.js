"use strict";(()=>{var e={};e.id=8739,e.ids=[8739],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},20047:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>x,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>E,staticGenerationAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(49303),o=r(88716),i=r(60670),n=r(87070),u=r(52972);async function p(){try{let e=u.Vi.FEATURES.USE_MOCK_AI,t=u.Vi.VERTEX_AI.ENABLED,r=u.Vi.VERTEX_AI.PROJECT_ID;return n.NextResponse.json({status:"success",useMockAI:e,vertexEnabled:t,hasVertexConfig:!!r,aiProvider:"Google Vertex AI",aiModel:u.Vi.VERTEX_AI.MODEL_NAME||"gemini-1.5-pro",mockEnabled:e,timestamp:new Date().toISOString()})}catch(e){return console.error("Erro ao obter status da IA:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro desconhecido ao verificar o status da IA"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/ai/status/route",pathname:"/api/ai/status",filename:"route",bundlePath:"app/api/ai/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\ai\\status\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:c,staticGenerationAsyncStorage:l,serverHooks:E}=d,m="/api/ai/status/route";function x(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:l})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,7410,2972],()=>r(20047));module.exports=s})();