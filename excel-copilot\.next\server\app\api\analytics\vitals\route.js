"use strict";(()=>{var e={};e.id=9332,e.ids=[9332],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},92843:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>w,patchFetch:()=>b,requestAsyncStorage:()=>v,routeModule:()=>f,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{GET:()=>g,POST:()=>m,dynamic:()=>p,runtime:()=>d});var n=t(49303),o=t(88716),i=t(60670),s=t(87070),l=t(43895),c=t(63841);let u={error:(e,r,t)=>l.kg.error(e,r,t),warn:(e,r)=>l.kg.warn(e,r),performance:(e,r,t)=>l.kg.info(`Performance metric: ${e}`,{value:r,...t})},d="nodejs",p="force-dynamic";async function m(e){try{let r=await e.json();if(!r.name||"number"!=typeof r.value)return s.NextResponse.json({error:"Invalid metric data"},{status:400});u.performance(`web_vital_${r.name}`,r.value,{rating:r.rating,url:r.url,userAgent:r.userAgent,navigationType:r.navigationType});try{await c.prisma.webVital.create({data:{name:r.name,value:r.value,rating:r.rating,delta:r.delta,metricId:r.id,navigationType:r.navigationType,url:r.url,userAgent:r.userAgent,timestamp:new Date(r.timestamp)}})}catch(e){u.warn("Failed to save web vital to database",{error:e})}return"poor"===r.rating&&u.warn(`Poor Web Vital detected: ${r.name}`,{value:r.value,url:r.url,userAgent:r.userAgent}),s.NextResponse.json({success:!0})}catch(e){return u.error("Error processing web vital metric",e,{component:"web-vitals-api"}),s.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("days")||"7"),a=r.get("metric"),n=new Date;n.setDate(n.getDate()-t);let o={timestamp:{gte:n}};a&&(o.name=a);let i=await c.prisma.webVital.findMany({where:o,orderBy:{timestamp:"desc"},take:1e3}),l={total:i.length,byRating:{good:i.filter(e=>"good"===e.rating).length,needsImprovement:i.filter(e=>"needs-improvement"===e.rating).length,poor:i.filter(e=>"poor"===e.rating).length},byMetric:{}};for(let e of[...new Set(i.map(e=>e.name))]){let r=i.filter(r=>r.name===e),t=r.map(e=>e.value).sort((e,r)=>e-r);l.byMetric[e]={count:r.length,average:t.reduce((e,r)=>e+r,0)/t.length,p75:t[Math.floor(.75*t.length)]||0,p95:t[Math.floor(.95*t.length)]||0}}return s.NextResponse.json({success:!0,data:{period:`${t} days`,stats:l,recentMetrics:i.slice(0,50)}})}catch(e){return u.error("Error fetching web vital statistics",e,{component:"web-vitals-api"}),s.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/analytics/vitals/route",pathname:"/api/analytics/vitals",filename:"route",bundlePath:"app/api/analytics/vitals/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\analytics\\vitals\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:v,staticGenerationAsyncStorage:h,serverHooks:y}=f,w="/api/analytics/vitals/route";function b(){return(0,i.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},43895:(e,r,t)=>{let a;t.d(r,{kg:()=>u});var n=t(99557),o=t.n(n);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(a=>{r.includes(a)||(t[a]=e[a])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err}}};try{let e=c.production;a=o()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=o()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);a.warn(t,e)}else a.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:n,extractedMetadata:o}=s(r),i={...t||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};a.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:n,extractedMetadata:o}=s(r),i={...t||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};a.fatal(i,e)},createChild:e=>{let r=a.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:a}=s(t);r.warn(a,e)}else r.warn(l(t)||{},e)},error:(e,t,a)=>{let{normalizedError:n,extractedMetadata:o}=s(t),i={...a||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.error(i,e)},fatal:(e,t,a)=>{let{normalizedError:n,extractedMetadata:o}=s(t),i={...a||{},...o,...n&&{error:{message:n.message,stack:n.stack,name:n.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>s});var a=t(53524);let n={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},o={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],s=global.prisma||new a.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...o,activeConnections:Math.min(Math.floor(5*Math.random())+1,o.maxPoolSize),poolSize:o.poolSize}}async function c(){try{await s.$disconnect(),n.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){n.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{o.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),o.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&n.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{o.failedQueries++,o.connectionFailures++,o.lastConnectionFailure=new Date().toISOString(),n.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557],()=>t(92843));module.exports=a})();