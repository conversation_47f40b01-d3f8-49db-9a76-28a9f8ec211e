"use strict";(()=>{var e={};e.id=9990,e.ids=[9990],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},88595:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>I,patchFetch:()=>C,requestAsyncStorage:()=>d,routeModule:()=>_,serverHooks:()=>p,staticGenerationAsyncStorage:()=>c});var t={};r.r(t),r.d(t,{GET:()=>u,dynamic:()=>s});var E=r(49303),a=r(88716),i=r(60670),n=r(87070),T=r(52972);let s="force-dynamic";async function u(e){try{let o={NEXTAUTH_SECRET:!!T.Vi.NEXTAUTH_SECRET,NEXTAUTH_URL:T.Vi.NEXTAUTH_URL,GOOGLE_CLIENT_ID:!!T.Vi.API_KEYS.GOOGLE_CLIENT_ID,GOOGLE_CLIENT_SECRET:!!T.Vi.API_KEYS.GOOGLE_CLIENT_SECRET,GITHUB_CLIENT_ID:!!T.Vi.API_KEYS.GITHUB_CLIENT_ID,GITHUB_CLIENT_SECRET:!!T.Vi.API_KEYS.GITHUB_CLIENT_SECRET,DATABASE_URL:!!T.Vi.DATABASE_URL,NODE_ENV:T.Vi.NODE_ENV,IS_PRODUCTION:T.Vi.IS_PRODUCTION,IS_DEVELOPMENT:T.Vi.IS_DEVELOPMENT},r=o.GOOGLE_CLIENT_ID&&o.GOOGLE_CLIENT_SECRET,t=o.GITHUB_CLIENT_ID&&o.GITHUB_CLIENT_SECRET,E=o.NEXTAUTH_SECRET&&o.NEXTAUTH_URL&&o.DATABASE_URL,a=[];E||(o.NEXTAUTH_SECRET||a.push("NEXTAUTH_SECRET n\xe3o configurado"),o.NEXTAUTH_URL||a.push("NEXTAUTH_URL n\xe3o configurado"),o.DATABASE_URL||a.push("DATABASE_URL n\xe3o configurado")),r||a.push("Credenciais do Google n\xe3o configuradas (GOOGLE_CLIENT_ID/GOOGLE_CLIENT_SECRET)"),t||a.push("Credenciais do GitHub n\xe3o configuradas (GITHUB_CLIENT_ID/GITHUB_CLIENT_SECRET)");let i={timestamp:new Date().toISOString(),environment:T.Vi.NODE_ENV,authConfig:o,providersStatus:{google:r?"Configurado":"N\xe3o configurado",github:t?"Configurado":"N\xe3o configurado"},issues:a,ready:0===a.length,url:e.url,headers:Object.fromEntries(e.headers.entries())};return n.NextResponse.json(i,{status:200,headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return console.error("\uD83D\uDEA8 [AUTH-DEBUG] Erro ao verificar configura\xe7\xe3o:",e),n.NextResponse.json({error:"Erro ao verificar configura\xe7\xe3o de autentica\xe7\xe3o",details:e instanceof Error?e.message:"Erro desconhecido",timestamp:new Date().toISOString()},{status:500})}}let _=new E.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/debug/route",pathname:"/api/auth/debug",filename:"route",bundlePath:"app/api/auth/debug/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\debug\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:p}=_,I="/api/auth/debug/route";function C(){return(0,i.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:c})}}};var o=require("../../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8948,5972,7410,2972],()=>r(88595));module.exports=t})();