"use strict";(()=>{var e={};e.id=2481,e.ids=[2481],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},54736:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>p,dynamic:()=>c});var i=r(49303),o=r(88716),n=r(60670),a=r(87070),u=r(81628);let c="force-dynamic";async function p(e){try{let e=u.L.providers,t={timestamp:new Date().toISOString(),providersCount:e.length,providers:e.map(e=>({id:e.id,name:e.name,type:e.type,hasClientId:!!e.clientId,hasClientSecret:!!e.clientSecret,clientIdLength:e.clientId?.length||0,clientSecretLength:e.clientSecret?.length||0})),authOptions:{hasAdapter:!!u.L.adapter,hasSecret:!!u.L.secret,sessionStrategy:u.L.session?.strategy,hasCallbacks:!!u.L.callbacks,hasPages:!!u.L.pages,debug:u.L.debug},issues:[],recommendations:[]};e.forEach(e=>{e.clientId?e.clientId.length<10&&t.issues.push(`${e.id}: Client ID muito curto`):t.issues.push(`${e.id}: Client ID n\xe3o configurado`),e.clientSecret?e.clientSecret.length<10&&t.issues.push(`${e.id}: Client Secret muito curto`):t.issues.push(`${e.id}: Client Secret n\xe3o configurado`)}),u.L.secret||t.issues.push("NEXTAUTH_SECRET n\xe3o configurado"),u.L.adapter||t.issues.push("Adapter do banco de dados n\xe3o configurado");try{let r=process.env.AUTH_NEXTAUTH_URL||"https://excel-copilot-eight.vercel.app";t.providers.forEach((s,i)=>{let o=e[i];if(o){let e=`${r}/api/auth/signin/${o.id}`;t.providers[i].signinUrl=e}})}catch(e){t.issues.push(`Erro ao gerar URLs de signin: ${e instanceof Error?e.message:e}`)}return t.issues.length>0&&(t.recommendations.push("Verificar vari\xe1veis de ambiente no Vercel"),t.recommendations.push("Confirmar configura\xe7\xf5es OAuth nos provedores"),t.recommendations.push("Verificar se as URLs de callback est\xe3o corretas")),a.NextResponse.json(t,{status:200,headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return a.NextResponse.json({error:"Erro interno durante teste de login",message:e instanceof Error?e.message:"Erro desconhecido",stack:e instanceof Error?e.stack:void 0,timestamp:new Date().toISOString()},{status:500,headers:{"Content-Type":"application/json"}})}}let d=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/test-login/route",pathname:"/api/auth/test-login",filename:"route",bundlePath:"app/api/auth/test-login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-login\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:h,serverHooks:g}=d,m="/api/auth/test-login/route";function x(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,9557,7410,2972,1628],()=>r(54736));module.exports=s})();