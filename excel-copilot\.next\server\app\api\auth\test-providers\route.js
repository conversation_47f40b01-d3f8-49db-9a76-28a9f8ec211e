"use strict";(()=>{var e={};e.id=8561,e.ids=[8561],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},88485:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>g,patchFetch:()=>T,requestAsyncStorage:()=>p,routeModule:()=>h,serverHooks:()=>E,staticGenerationAsyncStorage:()=>d});var o={};s.r(o),s.d(o,{GET:()=>c,dynamic:()=>l});var r=s(49303),n=s(88716),i=s(60670),u=s(87070),a=s(52972);let l="force-dynamic";async function c(e){try{let e=a.Vi.API_KEYS.GOOGLE_CLIENT_ID,t=a.Vi.API_KEYS.GOOGLE_CLIENT_SECRET,s=a.Vi.API_KEYS.GITHUB_CLIENT_ID,o=a.Vi.API_KEYS.GITHUB_CLIENT_SECRET,r=a.Vi.NEXTAUTH_SECRET,n=a.Vi.NEXTAUTH_URL,i=e&&t&&e.length>10&&t.length>10,l=s&&o&&s.length>10&&o.length>10,c=r&&n&&r.length>10&&n.startsWith("https://"),h=e?.includes(".apps.googleusercontent.com"),p=s?.match(/^[a-f0-9]{20}$/i)||s?.startsWith("Iv1."),d={google:`${n}/api/auth/callback/google`,github:`${n}/api/auth/callback/github`},E={timestamp:new Date().toISOString(),environment:a.Vi.NODE_ENV,nextAuthUrl:n,google:{configured:i,clientIdPresent:!!e,clientIdLength:e?.length||0,clientIdFormat:h,clientSecretPresent:!!t,clientSecretLength:t?.length||0,expectedCallback:d.google,issues:[]},github:{configured:l,clientIdPresent:!!s,clientIdLength:s?.length||0,clientIdFormat:p,clientSecretPresent:!!o,clientSecretLength:o?.length||0,expectedCallback:d.github,issues:[]},nextAuth:{configured:c,secretPresent:!!r,secretLength:r?.length||0,urlPresent:!!n,urlFormat:n?.startsWith("https://"),issues:[]},overallStatus:"unknown",recommendations:[]};return i||(e?e.length<=10&&E.google.issues.push("GOOGLE_CLIENT_ID muito curto (poss\xedvel erro)"):E.google.issues.push("GOOGLE_CLIENT_ID n\xe3o definido"),t?t.length<=10&&E.google.issues.push("GOOGLE_CLIENT_SECRET muito curto (poss\xedvel erro)"):E.google.issues.push("GOOGLE_CLIENT_SECRET n\xe3o definido"),h||E.google.issues.push("GOOGLE_CLIENT_ID n\xe3o tem formato esperado (.apps.googleusercontent.com)")),l||(s?s.length<=10&&E.github.issues.push("GITHUB_CLIENT_ID muito curto (poss\xedvel erro)"):E.github.issues.push("GITHUB_CLIENT_ID n\xe3o definido"),o?o.length<=10&&E.github.issues.push("GITHUB_CLIENT_SECRET muito curto (poss\xedvel erro)"):E.github.issues.push("GITHUB_CLIENT_SECRET n\xe3o definido"),p||E.github.issues.push("GITHUB_CLIENT_ID n\xe3o tem formato esperado (20 chars hex ou Iv1.*)")),c||(r?r.length<=10&&E.nextAuth.issues.push("NEXTAUTH_SECRET muito curto (m\xednimo 32 caracteres recomendado)"):E.nextAuth.issues.push("NEXTAUTH_SECRET n\xe3o definido"),n?n.startsWith("https://")||E.nextAuth.issues.push("NEXTAUTH_URL deve come\xe7ar com https://"):E.nextAuth.issues.push("NEXTAUTH_URL n\xe3o definido")),i&&l&&c?E.overallStatus="success":c&&(i||l)?E.overallStatus="partial":E.overallStatus="error",i||(E.recommendations.push("Configurar Google OAuth no Google Cloud Console"),E.recommendations.push("Verificar GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET no Vercel")),l||(E.recommendations.push("Configurar GitHub OAuth App no GitHub"),E.recommendations.push("Verificar GITHUB_CLIENT_ID e GITHUB_CLIENT_SECRET no Vercel")),c||E.recommendations.push("Configurar NEXTAUTH_SECRET e NEXTAUTH_URL no Vercel"),u.NextResponse.json(E,{status:200,headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate"}})}catch(e){return console.error("\uD83D\uDEA8 [TEST-PROVIDERS] Erro durante teste:",e),u.NextResponse.json({error:"Erro interno durante teste de provedores",message:e instanceof Error?e.message:"Erro desconhecido",timestamp:new Date().toISOString()},{status:500,headers:{"Content-Type":"application/json"}})}}let h=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/test-providers/route",pathname:"/api/auth/test-providers",filename:"route",bundlePath:"app/api/auth/test-providers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\auth\\test-providers\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:E}=h,g="/api/auth/test-providers/route";function T(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[8948,5972,7410,2972],()=>s(88485));module.exports=o})();