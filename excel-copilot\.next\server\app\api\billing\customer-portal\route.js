"use strict";(()=>{var e={};e.id=7515,e.ids=[7515],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},92477:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>E,requestAsyncStorage:()=>v,routeModule:()=>f,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g});var n={};t.r(n),t.d(n,{POST:()=>m,dynamic:()=>p});var o=t(49303),a=t(88716),i=t(60670),s=t(87070),l=t(45609),c=t(43895),u=t(46029),d=t(63841);let p="force-dynamic";async function m(e){try{if(!u.Ag)return s.NextResponse.json({error:"Stripe n\xe3o est\xe1 configurado."},{status:503});let r=await (0,l.getServerSession)();if(!r?.user)return s.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let{returnUrl:t}=await e.json(),n=r.user.id;if(!await d.prisma.user.findUnique({where:{id:n}}))return s.NextResponse.json({error:"Usu\xe1rio n\xe3o encontrado."},{status:404});let o=(await d.prisma.subscription.findMany({where:{userId:n,OR:[{status:"active"},{status:"trialing"},{status:"past_due"}]},orderBy:{createdAt:"desc"},take:1}))[0];if(!o?.stripeCustomerId)return s.NextResponse.json({error:"Nenhuma assinatura ativa encontrada."},{status:400});let a=await u.Ag.billingPortal.sessions.create({customer:o.stripeCustomerId,return_url:t||`${e.headers.get("origin")}/dashboard`});return s.NextResponse.json({url:a.url})}catch(e){return c.kg.error("[CUSTOMER_PORTAL_ERROR]",e),s.NextResponse.json({error:"Erro ao acessar o portal do cliente. Por favor, tente novamente.",details:void 0},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/billing/customer-portal/route",pathname:"/api/billing/customer-portal",filename:"route",bundlePath:"app/api/billing/customer-portal/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\billing\\customer-portal\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:v,staticGenerationAsyncStorage:g,serverHooks:h}=f,x="/api/billing/customer-portal/route";function E(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},43895:(e,r,t)=>{let n;t.d(r,{kg:()=>u});var o=t(99557),a=t.n(o);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(n=>{r.includes(n)||(t[n]=e[n])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=c.production;n=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),n=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{n.trace(r||{},e)},debug:(e,r)=>{n.debug(r||{},e)},info:(e,r)=>{n.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);n.warn(t,e)}else n.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:o,extractedMetadata:a}=s(r),i={...t||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};n.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:o,extractedMetadata:a}=s(r),i={...t||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};n.fatal(i,e)},createChild:e=>{let r=n.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:n}=s(t);r.warn(n,e)}else r.warn(l(t)||{},e)},error:(e,t,n)=>{let{normalizedError:o,extractedMetadata:a}=s(t),i={...n||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.error(i,e)},fatal:(e,t,n)=>{let{normalizedError:o,extractedMetadata:a}=s(t),i={...n||{},...a,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},46029:(e,r,t)=>{t.d(r,{Ag:()=>c,Al:()=>u,Cf:()=>l,DI:()=>d,Xf:()=>a,cb:()=>i}),t(30468);var n=t(31059);let o={PRO_MONTHLY:"price_1RWHbARrKLXtzZkME498Zuab",PRO_ANNUAL:"price_1RWHckRrKLXtzZkMLLn1vFvh"},a={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},i={[a.FREE]:50,[a.PRO_MONTHLY]:500,[a.PRO_ANNUAL]:1e3},s=process.env.STRIPE_SECRET_KEY||"",l=process.env.STRIPE_WEBHOOK_SECRET||"",c=s?new n.Z(s,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function u(e){switch(e){case a.PRO_MONTHLY:return o.PRO_MONTHLY;case a.PRO_ANNUAL:return o.PRO_ANNUAL;default:return o.PRO_MONTHLY}}function d(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>s});var n=t(53524);let o={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],s=global.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function c(){try{await s.$disconnect(),o.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){o.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{a.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),a.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&o.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),o.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})},30468:()=>{var e,r="https://js.stripe.com",t="".concat(r,"/").concat("basil","/stripe.js"),n=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,o=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,a=function(){for(var e=document.querySelectorAll('script[src^="'.concat(r,'"]')),t=0;t<e.length;t++){var a,i=e[t];if(a=i.src,n.test(a)||o.test(a))return i}return null},i=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(t).concat(r);var o=document.head||document.body;if(!o)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return o.appendChild(n),n},s=null,l=null,c=null;Promise.resolve().then(function(){return e||(e=(null!==s?s:(s=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,n=a();n?n&&null!==c&&null!==l&&(n.removeEventListener("load",c),n.removeEventListener("error",l),null===(t=n.parentNode)||void 0===t||t.removeChild(n),n=i(null)):n=i(null),c=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},l=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",c),n.addEventListener("error",l)}catch(e){r(e);return}})).catch(function(e){return s=null,Promise.reject(e)})).catch(function(r){return e=null,Promise.reject(r)}))}).catch(function(e){console.warn(e)})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,5972,9557,330,5609,1059],()=>t(92477));module.exports=n})();