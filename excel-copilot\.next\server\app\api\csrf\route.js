"use strict";(()=>{var e={};e.id=8094,e.ids=[8094],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},71278:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>S,requestAsyncStorage:()=>m,routeModule:()=>N,serverHooks:()=>p,staticGenerationAsyncStorage:()=>T});var o,a={};t.r(a),t.d(a,{GET:()=>d,POST:()=>I,dynamic:()=>c,runtime:()=>_});var s=t(49303),i=t(88716),R=t(60670),n=t(87070),E=t(43895);!function(e){e.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",e.AUTHORIZATION_ERROR="AUTHORIZATION_ERROR",e.UNAUTHORIZED="UNAUTHORIZED",e.FORBIDDEN="FORBIDDEN",e.NOT_FOUND="NOT_FOUND",e.ALREADY_EXISTS="ALREADY_EXISTS",e.VALIDATION_ERROR="VALIDATION_ERROR",e.INVALID_INPUT="INVALID_INPUT",e.INVALID_REQUEST="INVALID_REQUEST",e.SERVICE_UNAVAILABLE="SERVICE_UNAVAILABLE",e.EXTERNAL_API_ERROR="EXTERNAL_API_ERROR",e.EXTERNAL_SERVICE="EXTERNAL_SERVICE",e.DATABASE_ERROR="DATABASE_ERROR",e.OPERATION_NOT_ALLOWED="OPERATION_NOT_ALLOWED",e.CONFLICT_ERROR="CONFLICT_ERROR",e.RATE_LIMIT_ERROR="RATE_LIMIT_ERROR",e.QUOTA_EXCEEDED="QUOTA_EXCEEDED",e.CONFIGURATION_ERROR="CONFIGURATION_ERROR",e.EXCEL_OPERATION_ERROR="EXCEL_OPERATION_ERROR",e.EXCEL_PARSE_ERROR="EXCEL_PARSE_ERROR",e.AI_REQUEST_ERROR="AI_REQUEST_ERROR",e.AI_RESPONSE_ERROR="AI_RESPONSE_ERROR",e.NETWORK_ERROR="NETWORK_ERROR",e.UNKNOWN_ERROR="UNKNOWN_ERROR",e.INTERNAL="INTERNAL",e.API_ERROR="API_ERROR"}(o||(o={}));let O={UNKNOWN_ERROR:500,EXTERNAL_API_ERROR:502,EXTERNAL_SERVICE:503,DATABASE_ERROR:500,AUTHENTICATION_ERROR:401,UNAUTHORIZED:401,AUTHORIZATION_ERROR:403,FORBIDDEN:403,NOT_FOUND:404,VALIDATION_ERROR:400,SERVICE_UNAVAILABLE:503,INVALID_INPUT:400,INVALID_REQUEST:400,OPERATION_NOT_ALLOWED:403,CONFLICT_ERROR:409,RATE_LIMIT_ERROR:429,QUOTA_EXCEEDED:403,CONFIGURATION_ERROR:500,ALREADY_EXISTS:409,API_ERROR:500,INTERNAL:500,EXCEL_OPERATION_ERROR:400,EXCEL_PARSE_ERROR:400,AI_REQUEST_ERROR:400,AI_RESPONSE_ERROR:502,NETWORK_ERROR:503},u={UNKNOWN_ERROR:"Ocorreu um erro inesperado. Por favor, tente novamente.",EXTERNAL_API_ERROR:"Ocorreu um erro ao comunicar com um servi\xe7o externo. Tente novamente mais tarde.",EXTERNAL_SERVICE:"Um servi\xe7o externo est\xe1 indispon\xedvel no momento.",DATABASE_ERROR:"Erro ao acessar os dados. Tente novamente mais tarde.",AUTHENTICATION_ERROR:"Erro de autentica\xe7\xe3o. Por favor, fa\xe7a login novamente.",UNAUTHORIZED:"Erro de autentica\xe7\xe3o. Por favor, fa\xe7a login novamente.",AUTHORIZATION_ERROR:"Voc\xea n\xe3o tem permiss\xe3o para acessar este recurso.",FORBIDDEN:"Voc\xea n\xe3o tem permiss\xe3o para acessar este recurso.",NOT_FOUND:"O recurso solicitado n\xe3o foi encontrado.",VALIDATION_ERROR:"Dados inv\xe1lidos. Por favor, verifique as informa\xe7\xf5es fornecidas.",SERVICE_UNAVAILABLE:"Servi\xe7o temporariamente indispon\xedvel. Por favor, tente novamente mais tarde.",INVALID_INPUT:"Entrada inv\xe1lida. Verifique os dados enviados.",INVALID_REQUEST:"Requisi\xe7\xe3o inv\xe1lida. Verifique os par\xe2metros enviados.",OPERATION_NOT_ALLOWED:"Opera\xe7\xe3o n\xe3o permitida. Verifique as permiss\xf5es e tente novamente.",CONFLICT_ERROR:"Conflito ao processar a solicita\xe7\xe3o. Verifique os dados e tente novamente.",RATE_LIMIT_ERROR:"Muitas solicita\xe7\xf5es. Por favor, tente novamente mais tarde.",QUOTA_EXCEEDED:"Limite de requisi\xe7\xf5es excedido. Por favor, tente novamente mais tarde.",CONFIGURATION_ERROR:"Erro de configura\xe7\xe3o do sistema. Entre em contato com o suporte.",ALREADY_EXISTS:"Recurso j\xe1 existe. Verifique os dados e tente novamente.",API_ERROR:"Ocorreu um erro ao processar sua solicita\xe7\xe3o na API.",INTERNAL:"Ocorreu um erro interno no servidor. Tente novamente mais tarde.",EXCEL_OPERATION_ERROR:"Erro ao executar opera\xe7\xe3o no Excel. Verifique os dados e tente novamente.",EXCEL_PARSE_ERROR:"Erro ao processar o arquivo Excel. Verifique se o formato \xe9 v\xe1lido.",AI_REQUEST_ERROR:"Erro ao processar requisi\xe7\xe3o para o servi\xe7o de IA.",AI_RESPONSE_ERROR:"Erro ao receber resposta do servi\xe7o de IA. Tente novamente mais tarde.",NETWORK_ERROR:"Erro de conex\xe3o de rede. Verifique sua internet e tente novamente."},l={EXTERNAL_API_ERROR:2e3,EXTERNAL_SERVICE:3e3,DATABASE_ERROR:5e3,RATE_LIMIT_ERROR:3e4,NETWORK_ERROR:1500,AI_RESPONSE_ERROR:3e3,SERVICE_UNAVAILABLE:5e3};class A extends Error{constructor(e,r="UNKNOWN_ERROR",t=500,o,a){super(e),this.name="AppError",this.type=r,this.status=t||O[r]||500,this.details=o,this.id=function(){let e=Date.now().toString(36),r=Math.random().toString(36).substring(2,8);return`err_${e}_${r}`}(),this.timestamp=new Date,this.originalError=a,Object.setPrototypeOf(this,A.prototype)}getUserFriendlyMessage(){return u[this.type]||u.UNKNOWN_ERROR}getHttpStatus(){return O[this.type]||500}isRetryable(){return Object.keys(l).includes(this.type)}getRetryTime(){return l[this.type]||0}toLogFormat(){return{id:this.id,type:this.type,message:this.message,userMessage:this.getUserFriendlyMessage(),statusCode:this.getHttpStatus(),stack:this.stack,timestamp:this.timestamp.toISOString(),technicalDetails:this.details,originalError:this.formatOriginalError()}}toApiResponse(e=!1){return{error:this.getUserFriendlyMessage(),type:this.type,status:this.getHttpStatus(),id:this.id}}formatOriginalError(){if(!this.originalError)return null;if(this.originalError instanceof Error)return{name:this.originalError.name,message:this.originalError.message,stack:this.originalError.stack};try{return JSON.stringify(this.originalError)}catch{return String(this.originalError)}}}process.env.SECURITY_CSRF_SECRET;let c="force-dynamic",_="nodejs";async function d(e){try{process.env.SECURITY_CSRF_SECRET,Date.now().toString();let e=await function(e,r){let t=Date.now(),o=Math.random().toString(36).substring(2,15),a=Math.random().toString(36).substring(2,10);return`csrf-${t}-${o}-${a}`}(0,0),r=n.NextResponse.json({csrfToken:e,expires:Date.now()+36e5});return r.cookies.set({name:"csrf_token",value:e,httpOnly:!0,secure:!0,sameSite:"lax",path:"/",maxAge:3600}),r}catch(e){return E.kg.error("Erro ao gerar token CSRF:",e),n.NextResponse.json({error:"Erro interno ao gerar token CSRF"},{status:500})}}async function I(e){try{return n.NextResponse.json({valid:!0,message:"O token CSRF foi validado com sucesso"})}catch(e){return E.kg.error("Erro ao validar token CSRF:",e),n.NextResponse.json({error:"Erro interno ao validar token CSRF"},{status:500})}}let N=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/csrf/route",pathname:"/api/csrf",filename:"route",bundlePath:"app/api/csrf/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\csrf\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:T,serverHooks:p}=N,f="/api/csrf/route";function S(){return(0,R.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:T})}},43895:(e,r,t)=>{let o;t.d(r,{kg:()=>O});var a=t(99557),s=t.n(a);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function R(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function n(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let E={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err}}};try{let e=E.production;o=s()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=s()({level:"info",formatters:{level:e=>({level:e})}})}let O={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=R(r);o.warn(t,e)}else o.warn(n(r)||{},e)},error:(e,r,t)=>{let{normalizedError:a,extractedMetadata:s}=R(r),i={...t||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:a,extractedMetadata:s}=R(r),i={...t||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.fatal(i,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=R(t);r.warn(o,e)}else r.warn(n(t)||{},e)},error:(e,t,o)=>{let{normalizedError:a,extractedMetadata:s}=R(t),i={...o||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.error(i,e)},fatal:(e,t,o)=>{let{normalizedError:a,extractedMetadata:s}=R(t),i={...o||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557],()=>t(71278));module.exports=o})();