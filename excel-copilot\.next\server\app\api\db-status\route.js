"use strict";(()=>{var e={};e.id=5869,e.ids=[5869],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},47822:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>E,requestAsyncStorage:()=>b,routeModule:()=>g,serverHooks:()=>x,staticGenerationAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{GET:()=>f,dynamic:()=>p,runtime:()=>m});var s=t(49303),o=t(88716),n=t(60670),i=t(53524),l=t(87070),u=t(43895);let c="phase-production-build"===process.env.NEXT_PHASE||"true"===process.env.NEXT_STATIC_EXPORT,d=c?null:new i.PrismaClient,p="force-dynamic",m="nodejs";async function f(e){if(c)return u.kg.info("Pulando verifica\xe7\xe3o de DB durante build est\xe1tico"),l.NextResponse.json({status:"skipped",message:"Verifica\xe7\xe3o de banco de dados ignorada durante build est\xe1tico"},{status:200});try{let e=(await d.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `).map(e=>e.table_name),r=["account","session","user","verificationtoken","subscription","workbook"].filter(r=>!e.includes(r.toLowerCase()));if(r.length>0)return l.NextResponse.json({status:"warning",message:`Banco de dados conectado, mas faltam tabelas: ${r.join(", ")}`,tables:e,missingTables:r},{status:200});return l.NextResponse.json({status:"ok",message:"Banco de dados conectado e tabelas verificadas",tables:e},{status:200})}catch(e){return console.error("Erro ao verificar status do banco de dados:",e),l.NextResponse.json({status:"error",message:"Falha ao conectar ao banco de dados",error:e instanceof Error?e.message:String(e)},{status:500})}finally{d&&await d.$disconnect()}}let g=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/db-status/route",pathname:"/api/db-status",filename:"route",bundlePath:"app/api/db-status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\db-status\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:b,staticGenerationAsyncStorage:v,serverHooks:x}=g,h="/api/db-status/route";function E(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:v})}},43895:(e,r,t)=>{let a;t.d(r,{kg:()=>c});var s=t(99557),o=t.n(s);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(a=>{r.includes(a)||(t[a]=e[a])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err}}};try{let e=u.production;a=o()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=o()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);a.warn(t,e)}else a.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:s,extractedMetadata:o}=i(r),n={...t||{},...o,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};a.error(n,e)},fatal:(e,r,t)=>{let{normalizedError:s,extractedMetadata:o}=i(r),n={...t||{},...o,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};a.fatal(n,e)},createChild:e=>{let r=a.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:a}=i(t);r.warn(a,e)}else r.warn(l(t)||{},e)},error:(e,t,a)=>{let{normalizedError:s,extractedMetadata:o}=i(t),n={...a||{},...o,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};r.error(n,e)},fatal:(e,t,a)=>{let{normalizedError:s,extractedMetadata:o}=i(t),n={...a||{},...o,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};r.fatal(n,e)}}},child:function(e){return this.createChild(e)}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557],()=>t(47822));module.exports=a})();