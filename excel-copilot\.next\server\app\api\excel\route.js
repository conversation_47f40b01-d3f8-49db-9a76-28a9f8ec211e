"use strict";(()=>{var e={};e.id=7740,e.ids=[7740],e.modules={53524:e=>{e.exports=require("@prisma/client")},57641:e=>{e.exports=require("exceljs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},46135:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>y,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>x,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{POST:()=>f,dynamic:()=>p});var a=t(49303),s=t(88716),n=t(60670),i=t(57641),l=t.n(i),u=t(87070),c=t(45609),d=t(43895),m=t(63841);let p="force-dynamic";async function f(e){try{let r=await (0,c.getServerSession)();if(!r?.user)return u.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let t=(await e.formData()).get("file");if(!t)return u.NextResponse.json({error:"Nenhum arquivo enviado."},{status:400});if(t.size>5242880)return u.NextResponse.json({error:"Arquivo muito grande. O tamanho m\xe1ximo permitido \xe9 5MB."},{status:400});let o="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===t.type||"application/vnd.ms-excel"===t.type||t.name.endsWith(".xlsx")||t.name.endsWith(".xls"),a="text/csv"===t.type||t.name.endsWith(".csv");if(!o&&!a)return u.NextResponse.json({error:"Formato de arquivo inv\xe1lido. Por favor, envie um arquivo Excel (.xlsx, .xls) ou CSV (.csv)."},{status:400});t.name.replace(/[^\w\s.-]/gi,"")!==t.name&&d.kg.warn(`Nome de arquivo potencialmente inseguro detectado: ${t.name}`);let s=await t.arrayBuffer(),n=Buffer.from(s),i=new(l()).Workbook;try{if(a){let e=n.toString("utf-8"),r=i.addWorksheet("Sheet1"),t=e.split("\n").map(e=>e.split(","));t.length>0&&r.addRows(t)}else await i.xlsx.load(s)}catch(e){return d.kg.error("Erro ao processar arquivo:",e),u.NextResponse.json({error:"Erro ao processar o arquivo. Formato inv\xe1lido ou arquivo corrompido."},{status:400})}let p={sheets:[]};i.eachSheet((e,r)=>{let t={id:r,name:e.name,rows:[]},o=[];e.eachRow({includeEmpty:!1},(e,r)=>{let a={cells:[]};e.eachCell({includeEmpty:!0},(e,t)=>{1===r&&o.push(e.value?.toString()||`Coluna ${t}`),a.cells.push({columnName:o[t-1]||`Coluna ${t}`,value:e.value,formula:e.formula||null,type:e.type})}),t.rows.push(a)}),p.sheets.push(t)});let f={sheets:[]},x=0,h=[];for(let e of p.sheets){let{sanitizedData:r,securityReport:t}={sanitizedData:JSON.parse(JSON.stringify(e)),securityReport:{hasDangerousFormulas:!1,formulasRejected:0,details:[]}};f.sheets.push(r),t.hasDangerousFormulas&&(x+=t.formulasRejected,h.push({sheetName:r.name,...t}))}x>0&&d.kg.warn(`[SECURITY_ALERT] Detectadas ${x} f\xf3rmulas potencialmente maliciosas em '${t.name}'`,{userId:r.user?.id,fileName:t.name,securityReports:h});let g=await m.prisma.workbook.create({data:{name:t.name,userId:r.user?.id||"",sheets:{create:f.sheets.map(e=>({name:e.name,data:JSON.stringify(e)}))}},include:{sheets:!0}}),v={success:!0,workbook:{id:g.id,name:g.name,sheetCount:g.sheets.length}};return x>0&&(v.warnings={security:{formulasRemoved:x,message:`${x} f\xf3rmula(s) potencialmente perigosa(s) foram removidas por motivos de seguran\xe7a.`}}),u.NextResponse.json(v)}catch(e){return d.kg.error("[EXCEL_PROCESSING_ERROR]",e),u.NextResponse.json({error:"Erro ao processar o arquivo. Por favor, tente novamente.",details:void 0},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/excel/route",pathname:"/api/excel",filename:"route",bundlePath:"app/api/excel/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\excel\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:v}=x,y="/api/excel/route";function q(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}},43895:(e,r,t)=>{let o;t.d(r,{kg:()=>c});var a=t(99557),s=t.n(a);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(o=>{r.includes(o)||(t[o]=e[o])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err}}};try{let e=u.production;o=s()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=s()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,r)=>{o.trace(r||{},e)},debug:(e,r)=>{o.debug(r||{},e)},info:(e,r)=>{o.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);o.warn(t,e)}else o.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:a,extractedMetadata:s}=i(r),n={...t||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.error(n,e)},fatal:(e,r,t)=>{let{normalizedError:a,extractedMetadata:s}=i(r),n={...t||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};o.fatal(n,e)},createChild:e=>{let r=o.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:o}=i(t);r.warn(o,e)}else r.warn(l(t)||{},e)},error:(e,t,o)=>{let{normalizedError:a,extractedMetadata:s}=i(t),n={...o||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.error(n,e)},fatal:(e,t,o)=>{let{normalizedError:a,extractedMetadata:s}=i(t),n={...o||{},...s,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.fatal(n,e)}}},child:function(e){return this.createChild(e)}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>i});var o=t(53524);let a={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},s={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},n=[],i=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...s,activeConnections:Math.min(Math.floor(5*Math.random())+1,s.maxPoolSize),poolSize:s.poolSize}}async function u(){try{await i.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}i.$on("query",e=>{s.totalQueries++,e.duration&&(n.push(e.duration),n.length>100&&n.shift(),s.averageQueryTime=n.reduce((e,r)=>e+r,0)/n.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),i.$on("error",e=>{s.failedQueries++,s.connectionFailures++,s.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,330,5609],()=>t(46135));module.exports=o})();