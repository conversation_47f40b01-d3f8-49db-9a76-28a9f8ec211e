"use strict";(()=>{var e={};e.id=4573,e.ids=[4573],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},3633:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>f,staticGenerationAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{POST:()=>c});var o=t(49303),s=t(88716),n=t(60670),i=t(87070),l=t(43895);async function c(e){try{let r=await e.json();if(!r.commandId||void 0===r.successful)return i.NextResponse.json({error:"Dados de feedback incompletos"},{status:400});return l.kg.info("Feedback recebido:",{commandId:r.commandId,successful:r.successful,hasText:!!r.feedbackText}),i.NextResponse.json({success:!0})}catch(e){return l.kg.error("Erro ao processar feedback:",e),i.NextResponse.json({error:"Erro interno ao processar feedback"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/feedback/route",pathname:"/api/feedback",filename:"route",bundlePath:"app/api/feedback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\feedback\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:f}=u,m="/api/feedback/route";function g(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:p})}},43895:(e,r,t)=>{let a;t.d(r,{kg:()=>u});var o=t(99557),s=t.n(o);function n(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function i(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(a=>{r.includes(a)||(t[a]=e[a])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:n(e),extractedMetadata:e}:{normalizedError:n(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let c={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:s().stdSerializers.err,error:s().stdSerializers.err}}};try{let e=c.production;a=s()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),a=s()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,r)=>{a.trace(r||{},e)},debug:(e,r)=>{a.debug(r||{},e)},info:(e,r)=>{a.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=i(r);a.warn(t,e)}else a.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:o,extractedMetadata:s}=i(r),n={...t||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};a.error(n,e)},fatal:(e,r,t)=>{let{normalizedError:o,extractedMetadata:s}=i(r),n={...t||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};a.fatal(n,e)},createChild:e=>{let r=a.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:a}=i(t);r.warn(a,e)}else r.warn(l(t)||{},e)},error:(e,t,a)=>{let{normalizedError:o,extractedMetadata:s}=i(t),n={...a||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.error(n,e)},fatal:(e,t,a)=>{let{normalizedError:o,extractedMetadata:s}=i(t),n={...a||{},...s,...o&&{error:{message:o.message,stack:o.stack,name:o.name}}};r.fatal(n,e)}}},child:function(e){return this.createChild(e)}}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557],()=>t(3633));module.exports=a})();