"use strict";(()=>{var e={};e.id=8751,e.ids=[8751],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},16910:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>b,staticGenerationAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>p,dynamic:()=>d});var a=r(49303),o=r(88716),i=r(60670),n=r(51557),l=r(43895),u=r(82840);let d="force-dynamic";async function p(e){try{let t=process.env.MCP_GITHUB_TOKEN,r=process.env.MCP_GITHUB_OWNER,s=process.env.MCP_GITHUB_REPO;if(!t)return u.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let{searchParams:a}=new URL(e.url),o=a.get("owner")||r,i=a.get("repo")||s,d=a.get("type")||"all",p=a.get("state")||"open",m=a.get("labels"),g=a.get("sort")||"updated",c=a.get("direction")||"desc",b=parseInt(a.get("per_page")||"30"),h=parseInt(a.get("page")||"1");if(!o||!i)return u.R.badRequest("owner e repo s\xe3o obrigat\xf3rios");if(b<1||b>100)return u.R.badRequest("Par\xe2metro per_page deve estar entre 1 e 100");if(h<1)return u.R.badRequest("Par\xe2metro page deve ser maior que 0");let _=new n.e({token:t}),R=[],f=[];("issues"===d||"all"===d)&&(R=(await _.getIssues({owner:o,repo:i,state:p,...m&&{labels:m},sort:g,direction:c,per_page:b,page:h})).issues),("pulls"===d||"all"===d)&&(f=(await _.getPullRequests({owner:o,repo:i,state:p,sort:g,direction:c,per_page:b,page:h})).pullRequests);let v=R.map(e=>({id:e.id,number:e.number,title:e.title,body:e.body,state:e.state,htmlUrl:e.html_url,user:{login:e.user.login,id:e.user.id,avatarUrl:e.user.avatar_url},assignees:e.assignees.map(e=>({login:e.login,id:e.id,avatarUrl:e.avatar_url})),labels:e.labels.map(e=>({id:e.id,name:e.name,color:e.color,description:e.description})),milestone:e.milestone?{id:e.milestone.id,title:e.milestone.title,description:e.milestone.description,state:e.milestone.state,dueOn:e.milestone.due_on}:null,createdAt:e.created_at,updatedAt:e.updated_at,closedAt:e.closed_at})),q=f.map(e=>({id:e.id,number:e.number,title:e.title,body:e.body,state:e.state,draft:e.draft,merged:e.merged,mergedAt:e.merged_at,htmlUrl:e.html_url,user:{login:e.user.login,id:e.user.id,avatarUrl:e.user.avatar_url},assignees:e.assignees.map(e=>({login:e.login,id:e.id,avatarUrl:e.avatar_url})),labels:e.labels.map(e=>({id:e.id,name:e.name,color:e.color,description:e.description})),head:{ref:e.head.ref,sha:e.head.sha,repo:e.head.repo?e.head.repo.full_name:null},base:{ref:e.base.ref,sha:e.base.sha,repo:e.base.repo?e.base.repo.full_name:null},mergeable:e.mergeable,mergeableState:e.mergeable_state,reviewComments:e.review_comments,commits:e.commits,additions:e.additions,deletions:e.deletions,changedFiles:e.changed_files,createdAt:e.created_at,updatedAt:e.updated_at,closedAt:e.closed_at})),x={repository:`${o}/${i}`,type:d,issues:v,pullRequests:q,pagination:{page:h,perPage:b,hasNext:v.length+q.length===b},filters:{state:p,labels:m,sort:g,direction:c},timestamp:new Date().toISOString()};return l.kg.info("Issues/PRs GitHub obtidos com sucesso",{repository:`${o}/${i}`,type:d,issuesCount:v.length,pullRequestsCount:q.length}),u.R.success(x)}catch(e){if(l.kg.error("Erro ao obter issues/PRs do GitHub",{error:e}),e instanceof Error)return u.R.error(`Erro ao conectar com GitHub: ${e.message}`,"GITHUB_API_ERROR",500);return u.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/github/issues/route",pathname:"/api/github/issues",filename:"route",bundlePath:"app/api/github/issues/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\issues\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:c,serverHooks:b}=m,h="/api/github/issues/route";function _(){return(0,i.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:c})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,9557,7410,2972,5072],()=>r(16910));module.exports=s})();