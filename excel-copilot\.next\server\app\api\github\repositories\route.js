"use strict";(()=>{var e={};e.id=8343,e.ids=[8343],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},96830:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>h,requestAsyncStorage:()=>_,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>R});var o={};t.r(o),t.d(o,{GET:()=>c,POST:()=>d,dynamic:()=>l});var s=t(49303),a=t(88716),i=t(60670),n=t(51557),u=t(43895),p=t(82840);let l="force-dynamic";async function c(e){try{let r=process.env.MCP_GITHUB_TOKEN,t=process.env.MCP_GITHUB_OWNER;if(!r)return p.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let{searchParams:o}=new URL(e.url),s=o.get("type")||"owner",a=o.get("sort")||"updated",i=o.get("direction")||"desc",l=parseInt(o.get("per_page")||"30"),c=parseInt(o.get("page")||"1");if(l<1||l>100)return p.R.badRequest("Par\xe2metro per_page deve estar entre 1 e 100");if(c<1)return p.R.badRequest("Par\xe2metro page deve ser maior que 0");let d=new n.e({token:r,...t&&{owner:t}}),g=await d.getRepositories({type:s,sort:a,direction:i,per_page:l,page:c}),_=g.repositories.map(e=>({id:e.id,name:e.name,fullName:e.full_name,description:e.description,private:e.private,htmlUrl:e.html_url,cloneUrl:e.clone_url,sshUrl:e.ssh_url,defaultBranch:e.default_branch,language:e.language,stars:e.stargazers_count,forks:e.forks_count,openIssues:e.open_issues_count,createdAt:e.created_at,updatedAt:e.updated_at,pushedAt:e.pushed_at,owner:{login:e.owner.login,id:e.owner.id,avatarUrl:e.owner.avatar_url,type:e.owner.type}})),R={repositories:_,pagination:{page:c,perPage:l,total:g.total,hasNext:_.length===l},filters:{type:s,sort:a,direction:i},timestamp:new Date().toISOString()};return u.kg.info("Reposit\xf3rios GitHub obtidos com sucesso",{count:_.length,type:s,sort:a}),p.R.success(R)}catch(e){if(u.kg.error("Erro ao obter reposit\xf3rios do GitHub",{error:e}),e instanceof Error)return p.R.error(`Erro ao conectar com GitHub: ${e.message}`,"GITHUB_API_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function d(e){try{let r=process.env.MCP_GITHUB_TOKEN;if(!r)return p.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let{owner:t,repo:o}=await e.json();if(!t||!o)return p.R.badRequest("owner e repo s\xe3o obrigat\xf3rios");let s=new n.e({token:r}),a=await s.getRepository(t,o),[i,l,c]=await Promise.all([s.getIssues({owner:t,repo:o,state:"open",per_page:5}),s.getPullRequests({owner:t,repo:o,state:"open",per_page:5}),s.getWorkflowRuns({owner:t,repo:o,per_page:5})]),d={repository:{id:a.id,name:a.name,fullName:a.full_name,description:a.description,private:a.private,htmlUrl:a.html_url,cloneUrl:a.clone_url,sshUrl:a.ssh_url,defaultBranch:a.default_branch,language:a.language,stars:a.stargazers_count,forks:a.forks_count,openIssues:a.open_issues_count,createdAt:a.created_at,updatedAt:a.updated_at,pushedAt:a.pushed_at,owner:{login:a.owner.login,id:a.owner.id,avatarUrl:a.owner.avatar_url,type:a.owner.type}},stats:{openIssues:i.total,openPullRequests:l.total,recentWorkflowRuns:c.total},recentActivity:{issues:i.issues.slice(0,3),pullRequests:l.pullRequests.slice(0,3),workflowRuns:c.workflowRuns.slice(0,3)},timestamp:new Date().toISOString()};return u.kg.info("Detalhes do reposit\xf3rio GitHub obtidos com sucesso",{repository:`${t}/${o}`}),p.R.success(d)}catch(e){if(u.kg.error("Erro ao obter detalhes do reposit\xf3rio GitHub",{error:e}),e instanceof Error)return p.R.error(`Erro ao conectar com GitHub: ${e.message}`,"GITHUB_API_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let g=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/github/repositories/route",pathname:"/api/github/repositories",filename:"route",bundlePath:"app/api/github/repositories/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\repositories\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:_,staticGenerationAsyncStorage:R,serverHooks:f}=g,m="/api/github/repositories/route";function h(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:R})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,2972,5072],()=>t(96830));module.exports=o})();