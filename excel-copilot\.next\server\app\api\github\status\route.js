"use strict";(()=>{var e={};e.id=4883,e.ids=[4883],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},18835:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>E,requestAsyncStorage:()=>g,routeModule:()=>R,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var o={};t.r(o),t.d(o,{GET:()=>d,POST:()=>l,dynamic:()=>p});var s=t(49303),a=t(88716),i=t(60670),n=t(51557),u=t(43895),c=t(82840);let p="force-dynamic";async function d(e){try{let e=process.env.MCP_GITHUB_TOKEN,r=process.env.MCP_GITHUB_OWNER,t=process.env.MCP_GITHUB_REPO;if(!e)return c.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let o=new n.w({token:e,...r&&{owner:r},...t&&{repo:t}}),s=null;if(r&&t)try{s=await o.getRepositoryDashboard(r,t)}catch(e){u.kg.warn("Erro ao obter dashboard do reposit\xf3rio:",e)}let a=null;if(r&&t)try{a=await o.getCICDMetrics(r,t)}catch(e){u.kg.warn("Erro ao obter m\xe9tricas de CI/CD:",e)}let i={status:"healthy",configured:{token:!!e,owner:!!r,repo:!!t},repository:s?{name:s.repository.full_name,description:s.repository.description,language:s.repository.language,stars:s.repository.stargazers_count,forks:s.repository.forks_count,openIssues:s.openIssues,openPullRequests:s.openPullRequests,healthStatus:s.healthStatus,lastUpdate:s.repository.updated_at}:null,cicd:a?{totalRuns:a.totalRuns,successRate:a.successRate,averageDuration:a.averageDuration,recentFailures:a.recentFailures.length}:null,timestamp:new Date().toISOString()};return u.kg.info("Status GitHub obtido com sucesso",{hasRepository:!!s,hasCICD:!!a}),c.R.success(i)}catch(e){if(u.kg.error("Erro ao obter status do GitHub",{error:e}),e instanceof Error)return c.R.error(`Erro ao conectar com GitHub: ${e.message}`,"GITHUB_API_ERROR",500);return c.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function l(e){try{let e=process.env.MCP_GITHUB_TOKEN,r=process.env.MCP_GITHUB_OWNER,t=process.env.MCP_GITHUB_REPO;if(!e)return c.R.error("GITHUB_TOKEN n\xe3o configurado","GITHUB_NOT_CONFIGURED",500);let o=new n.w({token:e,...r&&{owner:r},...t&&{repo:t}}),s=await o.getRepositoryDashboard(),a={status:"forced_check_completed",healthCheck:s,timestamp:new Date().toISOString()};return u.kg.info("Verifica\xe7\xe3o for\xe7ada de status GitHub conclu\xedda"),c.R.success(a)}catch(e){if(u.kg.error("Erro na verifica\xe7\xe3o for\xe7ada do GitHub",{error:e}),e instanceof Error)return c.R.error(`Erro na verifica\xe7\xe3o: ${e.message}`,"GITHUB_CHECK_ERROR",500);return c.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let R=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/github/status/route",pathname:"/api/github/status",filename:"route",bundlePath:"app/api/github/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\github\\status\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:h}=R,_="/api/github/status/route";function E(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,9557,7410,2972,5072],()=>t(18835));module.exports=o})();