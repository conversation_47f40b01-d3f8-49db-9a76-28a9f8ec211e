"use strict";(()=>{var e={};e.id=1177,e.ids=[1177],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},95967:(e,a,t)=>{t.r(a),t.d(a,{originalPathname:()=>g,patchFetch:()=>b,requestAsyncStorage:()=>m,routeModule:()=>u,serverHooks:()=>v,staticGenerationAsyncStorage:()=>f});var i={};t.r(i),t.d(i,{DELETE:()=>h,GET:()=>l,POST:()=>d,PUT:()=>p});var r=t(49303),s=t(88716),o=t(60670),n=t(87070),c=t(60756);async function l(e){try{let a=(0,c.getAvailableServices)(),t=[{name:"Health Geral",path:"/api/health",description:"Verifica a sa\xfade de todos os servi\xe7os do sistema",critical:!0,parameters:[{name:"type",description:"Tipo de verifica\xe7\xe3o (all|critical)",default:"all"},{name:"details",description:"Incluir detalhes na resposta",default:"true"}]},{name:"Health Cr\xedticos",path:"/api/health?type=critical",description:"Verifica apenas os servi\xe7os cr\xedticos (database, auth)",critical:!0,parameters:[]},{name:"Database",path:"/api/health/database",description:"Verifica a conectividade e performance do banco de dados",critical:!0,service:"database"},{name:"Authentication",path:"/api/health/auth",description:"Verifica a configura\xe7\xe3o do sistema de autentica\xe7\xe3o",critical:!0,service:"auth"},{name:"AI (Vertex AI)",path:"/api/health/ai",description:"Verifica a disponibilidade do sistema de IA",critical:!1,service:"ai"},{name:"Stripe",path:"/api/health/stripe",description:"Verifica a configura\xe7\xe3o do sistema de pagamentos",critical:!1,service:"stripe"},{name:"MCP Integrations",path:"/api/health/mcp",description:"Verifica as integra\xe7\xf5es MCP (Vercel, Linear, GitHub)",critical:!1,service:"mcp"},{name:"Test Endpoint",path:"/api/health/test",description:"Endpoint de teste simples para validar funcionamento",critical:!1,service:"test"},{name:"Debug Info",path:"/api/health/debug",description:"Informa\xe7\xf5es de debug sobre importa\xe7\xf5es e configura\xe7\xf5es",critical:!1,service:"debug"}],i={totalEndpoints:t.length,criticalEndpoints:t.filter(e=>e.critical).length,availableServices:a.length,serviceEndpoints:t.filter(e=>e.service).length},r={title:"Excel Copilot Health Checks API",description:"Sistema completo de monitoramento de sa\xfade dos servi\xe7os",version:"1.0.0",timestamp:new Date().toISOString(),stats:i,endpoints:t.map(t=>({...t,fullUrl:`${e.nextUrl.origin}${t.path}`,available:!t.service||a.includes(t.service)})),statusCodes:{200:"Servi\xe7o saud\xe1vel",503:"Servi\xe7o n\xe3o dispon\xedvel ou com falhas",500:"Erro interno do servidor",405:"M\xe9todo n\xe3o permitido (apenas GET \xe9 suportado)"},examples:[{title:"Verificar sa\xfade geral",url:"/api/health",description:"Verifica todos os servi\xe7os e retorna status geral"},{title:"Verificar apenas servi\xe7os cr\xedticos",url:"/api/health?type=critical",description:"Verifica apenas database e authentication"},{title:"Verificar database espec\xedfico",url:"/api/health/database",description:"Verifica conectividade e performance do banco"},{title:"Verificar sem detalhes",url:"/api/health?details=false",description:"Retorna apenas status b\xe1sico sem detalhes"}].map(a=>({...a,fullUrl:`${e.nextUrl.origin}${a.url}`})),usage:{monitoring:"Use para monitoramento cont\xednuo da sa\xfade do sistema",cicd:"Integre no pipeline CI/CD para valida\xe7\xe3o autom\xe1tica",alerts:"Configure alertas baseados nos c\xf3digos de status HTTP",dashboard:"Consuma os endpoints para criar dashboards de monitoramento"},recommendations:["Execute health checks a cada 30-60 segundos para monitoramento cont\xednuo","Configure alertas para falhas em servi\xe7os cr\xedticos (database, auth)","Use timeouts apropriados (5-10 segundos) ao consumir os endpoints","Monitore tempos de resposta para detectar degrada\xe7\xe3o de performance","Implemente retry logic para lidar com falhas tempor\xe1rias"]};return n.NextResponse.json(r,{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(a){let e=a instanceof Error?a.message:"Unknown error";return n.NextResponse.json({error:"Failed to retrieve health check information",message:e,timestamp:new Date().toISOString()},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function d(){return n.NextResponse.json({error:"Method not allowed"},{status:405})}async function p(){return n.NextResponse.json({error:"Method not allowed"},{status:405})}async function h(){return n.NextResponse.json({error:"Method not allowed"},{status:405})}let u=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/health/all/route",pathname:"/api/health/all",filename:"route",bundlePath:"app/api/health/all/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\all\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:v}=u,g="/api/health/all/route";function b(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:f})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),i=a.X(0,[8948,5972,756],()=>t(95967));module.exports=i})();