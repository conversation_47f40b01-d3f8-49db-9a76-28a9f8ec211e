"use strict";(()=>{var e={};e.id=4877,e.ids=[4877],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},14753:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{DELETE:()=>l,GET:()=>h,POST:()=>p,PUT:()=>c});var s=r(49303),o=r(88716),n=r(60670),u=r(87070),i=r(60756);async function h(e){try{let t=new URL(e.url),r="false"!==t.searchParams.get("details"),a=await (0,i.checkService)("auth"),s=(0,i.healthStatusToHttpCode)(a.status),o=(0,i.formatHealthResponse)(a,r);return u.NextResponse.json(o,{status:s,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(t){let e=t instanceof Error?t.message:"Unknown error";return u.NextResponse.json({status:"unhealthy",service:"auth",timestamp:new Date().toISOString(),responseTime:0,error:e},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function p(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}async function c(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}async function l(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}let d=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/health/auth/route",pathname:"/api/health/auth",filename:"route",bundlePath:"app/api/health/auth/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\auth\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:v}=d,f="/api/health/auth/route";function g(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,756],()=>r(14753));module.exports=a})();