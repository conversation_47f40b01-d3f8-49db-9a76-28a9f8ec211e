"use strict";(()=>{var e={};e.id=9447,e.ids=[9447],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},85989:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>h,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var r={};a.r(r),a.d(r,{DELETE:()=>l,GET:()=>p,POST:()=>c,PUT:()=>d});var s=a(49303),o=a(88716),n=a(60670),u=a(87070),i=a(60756);async function p(e){try{let t=new URL(e.url),a="false"!==t.searchParams.get("details"),r=await (0,i.checkService)("database"),s=(0,i.healthStatusToHttpCode)(r.status),o=(0,i.formatHealthResponse)(r,a);return u.NextResponse.json(o,{status:s,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(t){let e=t instanceof Error?t.message:"Unknown error";return u.NextResponse.json({status:"unhealthy",service:"database",timestamp:new Date().toISOString(),responseTime:0,error:e},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function c(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}async function d(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}async function l(){return u.NextResponse.json({error:"Method not allowed"},{status:405})}let h=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/health/database/route",pathname:"/api/health/database",filename:"route",bundlePath:"app/api/health/database/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\database\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:v}=h,f="/api/health/database/route";function g(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,5972,756],()=>a(85989));module.exports=r})();