"use strict";(()=>{var e={};e.id=4547,e.ids=[4547],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},23152:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>C,patchFetch:()=>k,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>E,staticGenerationAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>i,POST:()=>u,PUT:()=>l});var o=r(49303),n=r(88716),a=r(60670),c=r(87070);async function i(e){try{let e=new Date().toISOString(),t={success:!1};try{let e=await r.e(6348).then(r.bind(r,36348));t={success:!0,exports:Object.keys(e),hasCheckAllServices:"function"==typeof e.checkAllServices,hasCheckCriticalServices:"function"==typeof e.checkCriticalServices,hasHealthManager:!!e.healthManager}}catch(r){let e=r instanceof Error?r.message:"Unknown error";t={success:!1,error:e}}let s={success:!1};try{let e=await r.e(756).then(r.bind(r,60756));s={success:!0,exports:Object.keys(e),hasCheckAllServices:"function"==typeof e.checkAllServices,hasCheckCriticalServices:"function"==typeof e.checkCriticalServices}}catch(t){let e=t instanceof Error?t.message:"Unknown error";s={success:!1,error:e}}let o={status:"debug",timestamp:e,environment:"production",imports:{healthChecks:t,healthChecksIndex:s},availableEnvVars:{database:!!process.env.DB_DATABASE_URL,nextAuthSecret:!!process.env.AUTH_NEXTAUTH_SECRET,stripeSecret:!!process.env.STRIPE_SECRET_KEY,vertexAiProject:!!process.env.AI_VERTEX_PROJECT_ID,vercelToken:!!(process.env.MCP_VERCEL_TOKEN||process.env.MCP_VERCEL_TOKEN)}};return c.NextResponse.json(o,{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(r){let e=r instanceof Error?r.message:"Unknown error",t=r instanceof Error?r.stack:void 0;return c.NextResponse.json({status:"error",timestamp:new Date().toISOString(),error:e,stack:t},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function u(){return c.NextResponse.json({error:"Method not allowed"},{status:405})}async function l(){return c.NextResponse.json({error:"Method not allowed"},{status:405})}async function h(){return c.NextResponse.json({error:"Method not allowed"},{status:405})}let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/health/debug/route",pathname:"/api/health/debug",filename:"route",bundlePath:"app/api/health/debug/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\debug\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:v,serverHooks:E}=p,C="/api/health/debug/route";function k(){return(0,a.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:v})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(23152));module.exports=s})();