"use strict";(()=>{var e={};e.id=6733,e.ids=[6733],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},76663:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>u,POST:()=>c,PUT:()=>l});var a=r(49303),o=r(88716),n=r(60670),i=r(87070),p=r(60756);async function u(e){try{let t=new URL(e.url),r="false"!==t.searchParams.get("details"),s=await (0,p.checkService)("stripe"),a=(0,p.healthStatusToHttpCode)(s.status),o=(0,p.formatHealthResponse)(s,r);return i.NextResponse.json(o,{status:a,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(t){let e=t instanceof Error?t.message:"Unknown error";return i.NextResponse.json({status:"unhealthy",service:"stripe",timestamp:new Date().toISOString(),responseTime:0,error:e},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function c(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function l(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function h(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/health/stripe/route",pathname:"/api/health/stripe",filename:"route",bundlePath:"app/api/health/stripe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\stripe\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:x,serverHooks:v}=d,f="/api/health/stripe/route";function g(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,756],()=>r(76663));module.exports=s})();