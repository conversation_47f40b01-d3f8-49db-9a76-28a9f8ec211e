"use strict";(()=>{var e={};e.id=2432,e.ids=[2432],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},30684:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>h,serverHooks:()=>v,staticGenerationAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>u,POST:()=>p,PUT:()=>c});var o=r(49303),n=r(88716),a=r(60670),i=r(87070);async function u(e){try{let e=new Date().toISOString();return i.NextResponse.json({status:"healthy",timestamp:e,responseTime:50,service:"test",details:{message:"Health check test endpoint working",environment:"production",version:"1.0.0"}},{status:200,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}})}catch(e){return i.NextResponse.json({status:"unhealthy",service:"test",timestamp:new Date().toISOString(),responseTime:0,error:e instanceof Error?e.message:"Unknown error"},{status:500,headers:{"Cache-Control":"no-cache, no-store, must-revalidate"}})}}async function p(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function c(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}async function d(){return i.NextResponse.json({error:"Method not allowed"},{status:405})}let h=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/health/test/route",pathname:"/api/health/test",filename:"route",bundlePath:"app/api/health/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\health\\test\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:v}=h,x="/api/health/test/route";function g(){return(0,a.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(30684));module.exports=s})();