"use strict";(()=>{var e={};e.id=8961,e.ids=[8961],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59246:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>P,patchFetch:()=>A,requestAsyncStorage:()=>m,routeModule:()=>y,serverHooks:()=>x,staticGenerationAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{DELETE:()=>g,GET:()=>u,PATCH:()=>f,POST:()=>l,PUT:()=>h,dynamic:()=>d});var n=t(49303),o=t(88716),i=t(60670),s=t(87070),c=t(7843);let d="force-dynamic",p={};async function u(e,{params:r}){let t=Array.isArray(r.path)?r.path.join("/"):r.path,a=`/api/${t}`,n=p[a]||a;(0,c.sk)(a,e.headers.get("user-agent")||void 0,e.headers.get("referer")||void 0);let o=s.NextResponse.redirect(new URL(n,e.url));return o.headers.set("X-API-Deprecated","true"),o.headers.set("X-API-Deprecation-Warning",`Esta rota ser\xe1 descontinuada. Migre para a nova vers\xe3o: ${n}`),o}async function l(e,{params:r}){return u(e,{params:r})}async function h(e,{params:r}){return u(e,{params:r})}async function g(e,{params:r}){return u(e,{params:r})}async function f(e,{params:r}){return u(e,{params:r})}let y=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/legacy-redirect/[...path]/route",pathname:"/api/legacy-redirect/[...path]",filename:"route",bundlePath:"app/api/legacy-redirect/[...path]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\legacy-redirect\\[...path]\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:v,serverHooks:x}=y,P="/api/legacy-redirect/[...path]/route";function A(){return(0,i.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:v})}},7843:(e,r,t)=>{t.d(r,{sk:()=>n,xG:()=>o});let a={};function n(e,r,t){a[e]||(a[e]=0),a[e]++,console.warn(`[DEPRECATED API] Rota legada acessada: ${e}. Contador: ${a[e]}. User-Agent: ${r||"N\xe3o informado"}. Referer: ${t||"N\xe3o informado"}`)}function o(){return{...a}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972],()=>t(59246));module.exports=a})();