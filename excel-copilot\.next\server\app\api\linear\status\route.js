"use strict";(()=>{var e={};e.id=490,e.ids=[490],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},19127:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>h,requestAsyncStorage:()=>d,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>l});var a=r(49303),o=r(88716),i=r(60670),n=r(87070),u=r(87502),p=r(43895);async function c(e){try{let t=new u.n,{searchParams:r}=new URL(e.url),s="true"===r.get("metrics"),a="true"===r.get("issues"),o=await t.getWorkspaceSummary(),i={status:"success",timestamp:new Date().toISOString(),workspace:o.workspace,summary:o.summary};if(s)try{let e=await t.getDevelopmentMetrics();i.metrics=e}catch(e){p.kg.warn("Erro ao obter m\xe9tricas Linear:",e),i.metrics={error:"Falha ao obter m\xe9tricas"}}if(a)try{let e=await t.getExcelCopilotIssues();i.excelCopilotIssues=e,i.recentIssues=o.recentIssues}catch(e){p.kg.warn("Erro ao obter issues Linear:",e),i.issues={error:"Falha ao obter issues"}}return n.NextResponse.json(i)}catch(e){return p.kg.error("Erro no endpoint Linear status:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro interno do servidor",timestamp:new Date().toISOString()},{status:500})}}async function l(e){try{let{action:t,data:r}=await e.json(),s=new u.n;switch(t){case"create_issue":{if(!r.title||!r.type)return n.NextResponse.json({error:"Title e type s\xe3o obrigat\xf3rios"},{status:400});let e=await s.createExcelCopilotIssue({type:r.type,title:r.title,description:r.description||"",priority:r.priority,assigneeId:r.assigneeId});return n.NextResponse.json({status:"success",action:"issue_created",issue:e.issue,timestamp:new Date().toISOString()})}case"refresh_cache":{let e=await s.getWorkspaceSummary();return n.NextResponse.json({status:"success",action:"cache_refreshed",summary:e.summary,timestamp:new Date().toISOString()})}default:return n.NextResponse.json({error:`A\xe7\xe3o '${t}' n\xe3o suportada`},{status:400})}}catch(e){return p.kg.error("Erro no POST Linear status:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro interno do servidor",timestamp:new Date().toISOString()},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/linear/status/route",pathname:"/api/linear/status",filename:"route",bundlePath:"app/api/linear/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\status\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:x,serverHooks:g}=m,w="/api/linear/status/route";function h(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,9557,7410,2972,7502],()=>r(19127));module.exports=s})();