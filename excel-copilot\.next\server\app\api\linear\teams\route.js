"use strict";(()=>{var e={};e.id=4304,e.ids=[4304],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},68335:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>c,dynamic:()=>l});var a=r(49303),o=r(88716),i=r(60670),n=r(87070),u=r(87502),p=r(43895);let l="force-dynamic";async function c(e){try{let{searchParams:t}=new URL(e.url),r="false"!==t.get("includeStates"),s="true"===t.get("includeProjects"),a=new u.y,o=await a.getTeams(),i={status:"success",teams:o.teams,summary:{totalTeams:o.teams.length,teamKeys:o.teams.map(e=>e.key)},timestamp:new Date().toISOString()};if(r)try{let e=await a.getWorkflowStates();i.workflowStates=e.states;let t=e.states.reduce((e,t)=>{let r=t.team.name;return e[r]||(e[r]=[]),e[r].push(t),e},{});i.statesByTeam=t}catch(e){p.kg.warn("Erro ao obter workflow states:",e),i.workflowStates={error:"Falha ao obter workflow states"}}if(s)try{let e=await a.getProjects();i.projects=e.projects,i.summary.totalProjects=e.projects.length}catch(e){p.kg.warn("Erro ao obter projetos:",e),i.projects={error:"Falha ao obter projetos"}}return n.NextResponse.json(i)}catch(e){return p.kg.error("Erro ao listar teams Linear:",e),n.NextResponse.json({status:"error",message:e instanceof Error?e.message:"Erro interno do servidor",timestamp:new Date().toISOString()},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/linear/teams/route",pathname:"/api/linear/teams",filename:"route",bundlePath:"app/api/linear/teams/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\linear\\teams\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:x,serverHooks:g}=m,w="/api/linear/teams/route";function f(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,9557,7410,2972,7502],()=>r(68335));module.exports=s})();