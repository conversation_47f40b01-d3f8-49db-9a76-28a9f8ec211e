"use strict";(()=>{var e={};e.id=2996,e.ids=[2996],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},3246:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>b,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>q,dynamic:()=>x,runtime:()=>l});var i=t(49303),o=t(88716),a=t(60670),n=t(21270),p=t(99747),u=t(39762),c=t(63841),m=t(4579),d=t(82840);let x="force-dynamic",l="nodejs",q=(0,p.x)([n.l],async(e,r)=>{try{if(!(process.env.ADMIN_EMAILS?.split(",")||[]).includes(r.user?.email||""))return d.R.forbidden("Apenas administradores podem acessar m\xe9tricas");let e=(0,u.ON)(),t=(0,c.P)(),s=(0,m.UZ)();return d.R.success({api:e,database:t,cache:s,server:{memory:process.memoryUsage(),uptime:process.uptime(),timestamp:new Date().toISOString()}})}catch(e){if(e instanceof Error)return d.R.error(e.message);return d.R.error("Erro ao buscar m\xe9tricas")}}),g=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/metrics/route",pathname:"/api/metrics",filename:"route",bundlePath:"app/api/metrics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\metrics\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:v}=g,f="/api/metrics/route";function b(){return(0,a.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:y})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628,5590],()=>t(3246));module.exports=s})();