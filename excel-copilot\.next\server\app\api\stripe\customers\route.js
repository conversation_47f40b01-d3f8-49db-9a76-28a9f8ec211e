"use strict";(()=>{var e={};e.id=4318,e.ids=[4318],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},90105:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>I,requestAsyncStorage:()=>R,routeModule:()=>S,serverHooks:()=>g,staticGenerationAsyncStorage:()=>E});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>l,dynamic:()=>p,runtime:()=>m});var o=t(49303),i=t(88716),n=t(60670),a=t(43895),u=t(69327),c=t(82840);let p="force-dynamic",m="nodejs";async function d(e){try{let r;let t=process.env.STRIPE_SECRET_KEY;if(!t)return c.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let{searchParams:s}=new URL(e.url),o=parseInt(s.get("limit")||"50"),i=s.get("email")||void 0,n=s.get("created_after"),p=s.get("created_before");if(o>100)return c.R.error("Limite m\xe1ximo \xe9 100 clientes","INVALID_LIMIT",400);if(n||p){if(r={},n){let e=parseInt(n);if(isNaN(e))return c.R.error("created_after deve ser um timestamp Unix v\xe1lido","INVALID_TIMESTAMP",400);r.gte=e}if(p){let e=parseInt(p);if(isNaN(e))return c.R.error("created_before deve ser um timestamp Unix v\xe1lido","INVALID_TIMESTAMP",400);r.lte=e}}let m=new u.L({apiKey:t}),d=await m.getCustomers({limit:o,...i&&{email:i},...r&&{created:r}}),l={customers:d.customers.map(e=>({id:e.id,email:e.email,name:e.name,created:new Date(1e3*e.created).toISOString(),subscriptions:e.subscriptions,totalSpent:e.totalSpent/100,currency:e.currency,defaultPaymentMethod:e.defaultPaymentMethod})),pagination:{limit:o,count:d.customers.length,hasMore:d.customers.length===o},filters:{email:i,createdAfter:n?new Date(1e3*parseInt(n)).toISOString():void 0,createdBefore:p?new Date(1e3*parseInt(p)).toISOString():void 0},timestamp:new Date().toISOString()};return a.kg.info("Clientes Stripe obtidos com sucesso",{count:d.customers.length,filters:{email:i,createdAfter:n,createdBefore:p}}),c.R.success(l)}catch(e){if(a.kg.error("Erro ao obter clientes Stripe",{error:e}),e instanceof Error)return c.R.error(`Erro ao buscar clientes: ${e.message}`,"STRIPE_API_ERROR",500);return c.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function l(e){try{let r=process.env.STRIPE_SECRET_KEY;if(!r)return c.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let{filters:t={},analytics:s=!1,includeSubscriptions:o=!0,includePayments:i=!1}=await e.json(),n=new u.L({apiKey:r}),p=await n.getCustomers({limit:t.limit||50,email:t.email,created:t.created}),m={customers:p.customers,summary:{totalCustomers:p.customers.length,totalSpent:p.customers.reduce((e,r)=>e+r.totalSpent,0)/100,averageSpent:p.customers.length>0?p.customers.reduce((e,r)=>e+r.totalSpent,0)/p.customers.length/100:0},timestamp:new Date().toISOString()};if(s){let e=p.customers.reduce((e,r)=>{let t=new Date(1e3*r.created).toISOString().substring(0,7);return e[t]=(e[t]||0)+1,e},{}),r=p.customers.reduce((e,r)=>(e.withSubscriptions+=r.subscriptions.total>0?1:0,e.activeSubscriptions+=r.subscriptions.active,e.canceledSubscriptions+=r.subscriptions.canceled,e),{withSubscriptions:0,activeSubscriptions:0,canceledSubscriptions:0});m.analytics={customersByMonth:e,subscriptionStats:r,paymentMethods:p.customers.reduce((e,r)=>{let t=r.defaultPaymentMethod?.type||"none";return e[t]=(e[t]||0)+1,e},{})}}if(i){let e=p.customers.slice(0,10).map(async e=>{try{let r=await n.getPayments({customer:e.id,limit:5});return{customerId:e.id,recentPayments:r.payments}}catch(r){return a.kg.warn(`Erro ao obter pagamentos do cliente ${e.id}:`,r),{customerId:e.id,recentPayments:[]}}}),r=await Promise.all(e);m.payments=r}return a.kg.info("Busca avan\xe7ada de clientes realizada",{count:p.customers.length,analytics:s,includePayments:i}),c.R.success(m)}catch(e){if(a.kg.error("Erro na busca avan\xe7ada de clientes",{error:e}),e instanceof Error)return c.R.error(`Erro na busca: ${e.message}`,"STRIPE_SEARCH_ERROR",500);return c.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let S=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/stripe/customers/route",pathname:"/api/stripe/customers",filename:"route",bundlePath:"app/api/stripe/customers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\customers\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:R,staticGenerationAsyncStorage:E,serverHooks:g}=S,f="/api/stripe/customers/route";function I(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:E})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972,9557,1059,5767],()=>t(90105));module.exports=s})();