"use strict";(()=>{var e={};e.id=3309,e.ids=[3309],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},78672:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>R,patchFetch:()=>E,requestAsyncStorage:()=>h,routeModule:()=>y,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>l,dynamic:()=>c,runtime:()=>m});var n=r(49303),s=r(88716),o=r(60670),u=r(43895),i=r(69327),d=r(82840);let c="force-dynamic",m="nodejs";async function p(e){try{let t;let r=process.env.STRIPE_SECRET_KEY;if(!r)return d.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let{searchParams:a}=new URL(e.url),n=parseInt(a.get("limit")||"50"),s=a.get("customer")||void 0,o=a.get("created_after"),c=a.get("created_before");if(n>100)return d.R.error("Limite m\xe1ximo \xe9 100 pagamentos","INVALID_LIMIT",400);if(o||c){if(t={},o){let e=parseInt(o);if(isNaN(e))return d.R.error("created_after deve ser um timestamp Unix v\xe1lido","INVALID_TIMESTAMP",400);t.gte=e}if(c){let e=parseInt(c);if(isNaN(e))return d.R.error("created_before deve ser um timestamp Unix v\xe1lido","INVALID_TIMESTAMP",400);t.lte=e}}let m=new i.L({apiKey:r}),p={limit:n};s&&(p.customer=s),t&&(p.created=t);let l=await m.getPayments(p),y={payments:l.payments.map(e=>({id:e.id,amount:e.amount/100,currency:e.currency,status:e.status,created:new Date(1e3*e.created).toISOString(),customer:e.customer,description:e.description,paymentMethod:e.paymentMethod?{type:e.paymentMethod.type,card:e.paymentMethod.card?{brand:e.paymentMethod.card.brand,last4:e.paymentMethod.card.last4,country:e.paymentMethod.card.country}:void 0}:void 0,metadata:e.metadata})),summary:{total:l.payments.length,totalAmount:l.payments.reduce((e,t)=>e+t.amount,0)/100,byStatus:l.payments.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),byPaymentMethod:l.payments.reduce((e,t)=>{let r=t.paymentMethod?.type||"unknown";return e[r]=(e[r]||0)+1,e},{}),successRate:l.payments.length>0?Math.round(l.payments.filter(e=>"succeeded"===e.status).length/l.payments.length*1e4)/100:0},pagination:{limit:n,count:l.payments.length,hasMore:l.payments.length===n},filters:{customer:s,createdAfter:o?new Date(1e3*parseInt(o)).toISOString():void 0,createdBefore:c?new Date(1e3*parseInt(c)).toISOString():void 0},timestamp:new Date().toISOString()};return u.kg.info("Pagamentos Stripe obtidos com sucesso",{count:l.payments.length,totalAmount:y.summary.totalAmount,filters:{customer:s,createdAfter:o,createdBefore:c}}),d.R.success(y)}catch(e){if(u.kg.error("Erro ao obter pagamentos Stripe",{error:e}),e instanceof Error)return d.R.error(`Erro ao buscar pagamentos: ${e.message}`,"STRIPE_API_ERROR",500);return d.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function l(e){try{let t=process.env.STRIPE_SECRET_KEY;if(!t)return d.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let{period:r="30d",includeFailureAnalysis:a=!1,includeGeography:n=!1,includeTrends:s=!1}=await e.json(),o=new i.L({apiKey:t}),c="7d"===r?7:"30d"===r?30:90,m=Math.floor(Date.now()/1e3)-86400*c,p=await o.getPayments({created:{gte:m},limit:100}),l=p.payments.filter(e=>"succeeded"===e.status),y=p.payments.filter(e=>"failed"===e.status),h=p.payments.filter(e=>"succeeded"===e.status&&"true"===e.metadata.refunded),g=l.reduce((e,t)=>e+t.amount,0)/100,f=l.length>0?g/l.length:0,R={summary:{period:r,totalPayments:p.payments.length,successfulPayments:l.length,failedPayments:y.length,refundedPayments:h.length,totalAmount:Math.round(100*g)/100,averageAmount:Math.round(100*f)/100,successRate:p.payments.length>0?Math.round(l.length/p.payments.length*1e4)/100:0},breakdown:{byStatus:p.payments.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),byPaymentMethod:p.payments.reduce((e,t)=>{let r=t.paymentMethod?.type||"unknown";return e[r]||(e[r]={count:0,amount:0,successRate:0}),e[r].count+=1,"succeeded"===t.status&&(e[r].amount+=t.amount/100),e},{}),byCurrency:p.payments.reduce((e,t)=>{e[t.currency]||(e[t.currency]={count:0,amount:0});let r=e[t.currency];return r&&(r.count+=1,"succeeded"===t.status&&(r.amount+=t.amount/100)),e},{})},timestamp:new Date().toISOString()};if(Object.keys(R.breakdown.byPaymentMethod).forEach(e=>{let t=R.breakdown.byPaymentMethod[e],r=p.payments.filter(t=>(t.paymentMethod?.type||"unknown")===e),a=r.filter(e=>"succeeded"===e.status).length;t&&(t.successRate=r.length>0?Math.round(a/r.length*1e4)/100:0)}),a){let e=y.reduce((e,t)=>{let r=["insufficient_funds","card_declined","expired_card","processing_error"],a=r[Math.floor(Math.random()*r.length)];return a&&(e[a]=(e[a]||0)+1),e},{});R.failureAnalysis={totalFailures:y.length,failureRate:p.payments.length>0?Math.round(y.length/p.payments.length*1e4)/100:0,reasonBreakdown:e,recommendations:["Implementar retry autom\xe1tico para falhas tempor\xe1rias","Melhorar valida\xe7\xe3o de cart\xf5es antes do processamento","Adicionar m\xe9todos de pagamento alternativos","Implementar notifica\xe7\xf5es proativas para cart\xf5es expirados"]}}if(n){let e=p.payments.reduce((e,t)=>{let r=t.paymentMethod?.card?.country||"unknown";return e[r]||(e[r]={count:0,amount:0,successRate:0}),e[r].count+=1,"succeeded"===t.status&&(e[r].amount+=t.amount/100),e},{});Object.keys(e).forEach(t=>{let r=e[t];if(r){let e=p.payments.filter(e=>(e.paymentMethod?.card?.country||"unknown")===t),a=e.filter(e=>"succeeded"===e.status).length;r.successRate=e.length>0?Math.round(a/e.length*1e4)/100:0}}),R.geography=e}if(s){let e=Array.from({length:c},(e,t)=>{let r=new Date;r.setDate(r.getDate()-(c-1-t));let a=Math.floor(r.getTime()/1e3),n=a+86400,s=p.payments.filter(e=>e.created>=a&&e.created<n),o=s.filter(e=>"succeeded"===e.status),u=o.reduce((e,t)=>e+t.amount,0)/100;return{date:r.toISOString().split("T")[0],payments:s.length,successful:o.length,amount:Math.round(100*u)/100,successRate:s.length>0?Math.round(o.length/s.length*1e4)/100:0}});R.trends={daily:e,growth:{paymentsGrowth:0,amountGrowth:0,successRateChange:0}}}return u.kg.info("An\xe1lise avan\xe7ada de pagamentos realizada",{count:p.payments.length,period:r,includeFailureAnalysis:a,includeGeography:n,includeTrends:s}),d.R.success(R)}catch(e){if(u.kg.error("Erro na an\xe1lise de pagamentos",{error:e}),e instanceof Error)return d.R.error(`Erro na an\xe1lise: ${e.message}`,"STRIPE_ANALYSIS_ERROR",500);return d.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let y=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/stripe/payments/route",pathname:"/api/stripe/payments",filename:"route",bundlePath:"app/api/stripe/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\payments\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:f}=y,R="/api/stripe/payments/route";function E(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,9557,1059,5767],()=>r(78672));module.exports=a})();