"use strict";(()=>{var e={};e.id=9575,e.ids=[9575],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},27740:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>E,patchFetch:()=>S,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>b,staticGenerationAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>m,dynamic:()=>p,runtime:()=>l});var n=r(49303),i=r(88716),a=r(60670),o=r(43895),u=r(69327),c=r(82840);let p="force-dynamic",l="nodejs";async function d(e){try{let t=process.env.STRIPE_SECRET_KEY;if(!t)return c.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let{searchParams:r}=new URL(e.url),s=parseInt(r.get("limit")||"50"),n=r.get("status"),i=r.get("customer")||void 0,a=r.get("price")||void 0;if(s>100)return c.R.error("Limite m\xe1ximo \xe9 100 assinaturas","INVALID_LIMIT",400);let p=["active","canceled","incomplete","incomplete_expired","past_due","trialing","unpaid"];if(n&&!p.includes(n))return c.R.error(`Status deve ser um dos: ${p.join(", ")}`,"INVALID_STATUS",400);let l=new u.L({apiKey:t}),d={limit:s};n&&(d.status=n),i&&(d.customer=i),a&&(d.price=a);let m=await l.getSubscriptions(d),g={subscriptions:m.subscriptions.map(e=>({id:e.id,customer:e.customer,status:e.status,currentPeriod:{start:new Date(1e3*e.currentPeriodStart).toISOString(),end:new Date(1e3*e.currentPeriodEnd).toISOString()},plan:{id:e.plan.id,name:e.plan.nickname||"Unnamed Plan",amount:e.plan.amount/100,currency:e.plan.currency,interval:e.plan.interval},cancelAtPeriodEnd:e.cancelAtPeriodEnd,trialEnd:e.trialEnd?new Date(1e3*e.trialEnd).toISOString():null,metadata:e.metadata})),summary:{total:m.subscriptions.length,byStatus:m.subscriptions.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),totalRevenue:m.subscriptions.filter(e=>"active"===e.status).reduce((e,t)=>e+t.plan.amount,0)/100},pagination:{limit:s,count:m.subscriptions.length,hasMore:m.subscriptions.length===s},filters:{status:n,customer:i,price:a},timestamp:new Date().toISOString()};return o.kg.info("Assinaturas Stripe obtidas com sucesso",{count:m.subscriptions.length,filters:{status:n,customer:i,price:a}}),c.R.success(g)}catch(e){if(o.kg.error("Erro ao obter assinaturas Stripe",{error:e}),e instanceof Error)return c.R.error(`Erro ao buscar assinaturas: ${e.message}`,"STRIPE_API_ERROR",500);return c.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function m(e){try{let t=process.env.STRIPE_SECRET_KEY;if(!t)return c.R.error("STRIPE_SECRET_KEY n\xe3o configurado","STRIPE_NOT_CONFIGURED",500);let{analysis:r="basic",period:s="30d",includeChurn:n=!1,includeCohorts:i=!1}=await e.json(),a=new u.L({apiKey:t}),p=await a.getSubscriptions({limit:100}),l=p.subscriptions.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),d=p.subscriptions.reduce((e,t)=>{let r=`${t.plan.nickname||t.plan.id} (${t.plan.interval})`;return e[r]||(e[r]={count:0,revenue:0}),e[r].count+=1,"active"===t.status&&(e[r].revenue+=t.plan.amount),e},{}),m=p.subscriptions.filter(e=>"active"===e.status).reduce((e,t)=>{let r=t.plan.amount,s=t.plan.interval;return"year"===s?e+r/12:"month"===s?e+r:e},0)/100,g={summary:{totalSubscriptions:p.subscriptions.length,activeSubscriptions:l.active||0,trialingSubscriptions:l.trialing||0,canceledSubscriptions:l.canceled||0,mrr:Math.round(100*m)/100,averageRevenuePerSubscription:p.subscriptions.length>0?Math.round(m/(l.active||1)*100)/100:0},breakdown:{byStatus:l,byPlan:Object.entries(d).map(([e,t])=>({plan:e,subscribers:t.count,revenue:Math.round(t.revenue/100*100)/100}))},timestamp:new Date().toISOString()};if(n){let e=new Date,t=new Date(e.getTime()-2592e6),r=p.subscriptions.filter(e=>"canceled"===e.status&&e.currentPeriodEnd>Math.floor(t.getTime()/1e3)),s=p.subscriptions.length>0?r.length/p.subscriptions.length*100:0;g.churn={rate:Math.round(100*s)/100,recentCancellations:r.length,reasons:{payment_failed:Math.floor(.4*r.length),customer_request:Math.floor(.3*r.length),price_increase:Math.floor(.2*r.length),other:Math.floor(.1*r.length)}}}if(i){let e=p.subscriptions.reduce((e,t)=>{let r=new Date(1e3*t.currentPeriodStart).toISOString().substring(0,7);return e[r]||(e[r]={started:0,active:0,revenue:0}),e[r].started+=1,"active"===t.status&&(e[r].active+=1,e[r].revenue+=t.plan.amount),e},{});g.cohorts=Object.entries(e).map(([e,t])=>({month:e,started:t.started,active:t.active,retention:t.started>0?Math.round(t.active/t.started*1e4)/100:0,revenue:Math.round(t.revenue/100*100)/100})).sort((e,t)=>t.month.localeCompare(e.month)).slice(0,12)}return o.kg.info("An\xe1lise avan\xe7ada de assinaturas realizada",{count:p.subscriptions.length,analysis:r,includeChurn:n,includeCohorts:i}),c.R.success(g)}catch(e){if(o.kg.error("Erro na an\xe1lise de assinaturas",{error:e}),e instanceof Error)return c.R.error(`Erro na an\xe1lise: ${e.message}`,"STRIPE_ANALYSIS_ERROR",500);return c.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let g=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/stripe/subscriptions/route",pathname:"/api/stripe/subscriptions",filename:"route",bundlePath:"app/api/stripe/subscriptions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\stripe\\subscriptions\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:v,serverHooks:b}=g,E="/api/stripe/subscriptions/route";function S(){return(0,a.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:v})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972,9557,1059,5767],()=>r(27740));module.exports=s})();