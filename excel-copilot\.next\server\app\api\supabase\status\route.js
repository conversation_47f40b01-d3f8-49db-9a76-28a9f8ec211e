"use strict";(()=>{var e={};e.id=6235,e.ids=[6235],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},99660:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>R,patchFetch:()=>_,requestAsyncStorage:()=>f,routeModule:()=>E,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>S,POST:()=>b,dynamic:()=>d,runtime:()=>m});var s=r(49303),o=r(88716),u=r(60670),n=r(87070),i=r(43895),c=r(57530),l=r(62091),p=r(82840);let d="force-dynamic",m="nodejs";async function S(e){try{let t=await (0,l.VF)(e,new n.NextResponse);if(t)return t;let r=process.env.SUPABASE_SERVICE_ROLE_KEY,a=process.env.SUPABASE_URL;if(!r||!a)return p.R.error("Credenciais do Supabase n\xe3o configuradas","SUPABASE_NOT_CONFIGURED",500);let s=new c.$({serviceRoleKey:r,projectUrl:a}),o=await s.getProjectStatus(),u=null;if("down"!==o.status)try{u=await s.getPerformanceMetrics()}catch(e){i.kg.warn("Erro ao obter m\xe9tricas de performance do Supabase:",e)}let d={status:o.status,message:o.message,project:o.project?{id:o.project.id,name:o.project.name,status:o.project.status,created_at:o.project.created_at,database_version:o.project.database?.version}:null,services:o.services,metrics:u?{database:{tableCount:u.database.tableCount,totalSize:u.database.totalSize,connectionCount:u.database.connectionCount},storage:{bucketCount:u.storage.bucketCount,totalObjects:u.storage.totalObjects,totalSize:u.storage.totalSize},realtime:{activeConnections:u.realtime.activeConnections,channelsCount:u.realtime.channelsCount}}:null,timestamp:new Date().toISOString()};return i.kg.info("Status Supabase obtido com sucesso",{status:o.status,services:o.services}),p.R.success(d)}catch(e){if(i.kg.error("Erro ao obter status do Supabase",{error:e}),e instanceof Error)return p.R.error(`Erro ao conectar com Supabase: ${e.message}`,"SUPABASE_API_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function b(e){try{let t=await (0,l.VF)(e,new n.NextResponse);if(t)return t;let r=process.env.SUPABASE_SERVICE_ROLE_KEY,a=process.env.SUPABASE_URL;if(!r||!a)return p.R.error("Credenciais do Supabase n\xe3o configuradas","SUPABASE_NOT_CONFIGURED",500);let s=new c.$({serviceRoleKey:r,projectUrl:a}),[o,u,d]=await Promise.allSettled([s.getProjectStatus(),s.getDatabaseSummary(),s.getStorageSummary()]),m={status:"forced_check_completed",projectStatus:"fulfilled"===o.status?o.value:{status:"error",error:o.reason?.message||"Erro desconhecido"},databaseSummary:"fulfilled"===u.status?{totalTables:u.value.totalTables,totalSize:u.value.totalSize,largestTables:u.value.largestTables.slice(0,3).map(e=>({name:e.name,schema:e.schema,size:e.size}))}:{error:u.reason?.message||"Erro ao acessar banco"},storageSummary:"fulfilled"===d.status?{totalBuckets:d.value.totalBuckets,publicBuckets:d.value.publicBuckets,privateBuckets:d.value.privateBuckets,buckets:d.value.buckets.map(e=>({name:e.name,public:e.public,created_at:e.created_at}))}:{error:d.reason?.message||"Erro ao acessar storage"},timestamp:new Date().toISOString()};return i.kg.info("Verifica\xe7\xe3o for\xe7ada de status Supabase conclu\xedda"),p.R.success(m)}catch(e){if(i.kg.error("Erro na verifica\xe7\xe3o for\xe7ada do Supabase",{error:e}),e instanceof Error)return p.R.error(`Erro na verifica\xe7\xe3o: ${e.message}`,"SUPABASE_CHECK_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}let E=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/supabase/status/route",pathname:"/api/supabase/status",filename:"route",bundlePath:"app/api/supabase/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\status\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:v}=E,R="/api/supabase/status/route";function _(){return(0,u.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,9557,7410,2972,8525],()=>r(99660));module.exports=a})();