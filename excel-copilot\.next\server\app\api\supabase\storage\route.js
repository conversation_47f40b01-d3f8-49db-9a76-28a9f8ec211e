"use strict";(()=>{var e={};e.id=1896,e.ids=[1896],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},70740:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>E,patchFetch:()=>y,requestAsyncStorage:()=>g,routeModule:()=>S,serverHooks:()=>R,staticGenerationAsyncStorage:()=>f});var r={};a.r(r),a.d(r,{GET:()=>b,POST:()=>_,dynamic:()=>m,runtime:()=>d});var s=a(49303),o=a(88716),i=a(60670),u=a(87070),c=a(43895),n=a(57530),l=a(62091),p=a(82840);let m="force-dynamic",d="nodejs";async function b(e){try{let t=await (0,l.VF)(e,new u.NextResponse);if(t)return t;let a=process.env.SUPABASE_SERVICE_ROLE_KEY,r=process.env.SUPABASE_URL;if(!a||!r)return p.R.error("Credenciais do Supabase n\xe3o configuradas","SUPABASE_NOT_CONFIGURED",500);let s=new n.$({serviceRoleKey:a,projectUrl:r}),{searchParams:o}=new URL(e.url),i=o.get("bucket"),m="true"===o.get("objects"),d=o.get("path")||"";if(i){let e=await s.getBucketContents(i,d),t={bucket:e.bucket?{name:e.bucket.name,public:e.bucket.public,file_size_limit:e.bucket.file_size_limit,allowed_mime_types:e.bucket.allowed_mime_types,created_at:e.bucket.created_at,updated_at:e.bucket.updated_at}:null,summary:{totalObjects:e.totalObjects,totalSize:k(e.totalSize),path:d||"/"},objects:m?e.objects.map(e=>({name:e.name,id:e.id,size:k(e.metadata?.size||0),mimetype:e.metadata?.mimetype,created_at:e.created_at,updated_at:e.updated_at,last_accessed_at:e.last_accessed_at})):[],timestamp:new Date().toISOString()};return c.kg.info("Conte\xfado do bucket Supabase obtido com sucesso",{bucket:i,totalObjects:e.totalObjects,path:d}),p.R.success(t)}{let e=await s.getStorageSummary(),t={summary:{totalBuckets:e.totalBuckets,publicBuckets:e.publicBuckets,privateBuckets:e.privateBuckets,totalObjects:e.bucketDetails.reduce((e,t)=>e+t.objectCount,0),totalSize:k(e.bucketDetails.reduce((e,t)=>e+t.totalSize,0))},buckets:e.bucketDetails.map(e=>({name:e.bucket.name,public:e.bucket.public,objectCount:e.objectCount,totalSize:k(e.totalSize),file_size_limit:e.bucket.file_size_limit,allowed_mime_types:e.bucket.allowed_mime_types,created_at:e.bucket.created_at,updated_at:e.bucket.updated_at})),timestamp:new Date().toISOString()};return c.kg.info("Resumo do storage Supabase obtido com sucesso",{totalBuckets:e.totalBuckets,totalObjects:t.summary.totalObjects}),p.R.success(t)}}catch(e){if(c.kg.error("Erro ao obter storage do Supabase",{error:e}),e instanceof Error)return p.R.error(`Erro ao acessar storage: ${e.message}`,"SUPABASE_STORAGE_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function _(e){try{let t=await (0,l.VF)(e,new u.NextResponse);if(t)return t;let a=process.env.SUPABASE_SERVICE_ROLE_KEY,r=process.env.SUPABASE_URL;if(!a||!r)return p.R.error("Credenciais do Supabase n\xe3o configuradas","SUPABASE_NOT_CONFIGURED",500);let{action:s,bucketName:o,options:i}=await e.json(),c=new n.$({serviceRoleKey:a,projectUrl:r});switch(s){case"analyze_bucket":if(!o)return p.R.error("Nome do bucket \xe9 obrigat\xf3rio para an\xe1lise","MISSING_BUCKET_NAME",400);{let e=await c.getBucketContents(o);if(!e.bucket)return p.R.error(`Bucket '${o}' n\xe3o encontrado`,"BUCKET_NOT_FOUND",404);let t=new Map,a=null,r=null,s=null;e.objects.forEach(e=>{let o=e.metadata?.mimetype||"unknown",i=e.metadata?.size||0,u=new Date(e.created_at),c=t.get(o)||{count:0,totalSize:0};t.set(o,{count:c.count+1,totalSize:c.totalSize+i}),(!a||u<new Date(a.created_at))&&(a=e),(!r||u>new Date(r.created_at))&&(r=e),(!s||i>(s.metadata?.size||0))&&(s=e)});let i={bucket:{name:e.bucket.name,public:e.bucket.public,file_size_limit:e.bucket.file_size_limit,allowed_mime_types:e.bucket.allowed_mime_types},statistics:{totalObjects:e.totalObjects,totalSize:k(e.totalSize),averageFileSize:e.totalObjects>0?k(e.totalSize/e.totalObjects):"0 Bytes"},fileTypes:Array.from(t.entries()).map(([t,a])=>({mimetype:t,count:a.count,totalSize:k(a.totalSize),percentage:(a.count/e.totalObjects*100).toFixed(1)})).sort((e,t)=>t.count-e.count),files:{oldest:a?{name:a.name,size:k(a.metadata?.size||0),created_at:a.created_at}:null,newest:r?{name:r.name,size:k(r.metadata?.size||0),created_at:r.created_at}:null,largest:s?{name:s.name,size:k(s.metadata?.size||0),created_at:s.created_at}:null},recommendations:[]},u=[];return e.totalObjects>1e4&&u.push({type:"performance",priority:"medium",message:"Bucket tem muitos objetos, considere organizar em subpastas",action:"organize_files"}),e.totalSize>5368709120&&u.push({type:"cost",priority:"medium",message:"Bucket est\xe1 usando muito espa\xe7o, considere arquivar arquivos antigos",action:"archive_old_files"}),e.bucket.public||e.bucket.allowed_mime_types?.length!==0||u.push({type:"security",priority:"low",message:"Considere definir tipos de arquivo permitidos para maior seguran\xe7a",action:"set_mime_restrictions"}),i.recommendations=u,p.R.success({action:"bucket_analyzed",bucketName:o,analysis:i,timestamp:new Date().toISOString()})}case"refresh_storage":{let e=await c.getStorageSummary();return p.R.success({action:"storage_refreshed",summary:{totalBuckets:e.totalBuckets,publicBuckets:e.publicBuckets,privateBuckets:e.privateBuckets,buckets:e.buckets.map(e=>({name:e.name,public:e.public,created_at:e.created_at}))},timestamp:new Date().toISOString()})}default:return p.R.error(`A\xe7\xe3o '${s}' n\xe3o suportada`,"UNSUPPORTED_ACTION",400)}}catch(e){if(c.kg.error("Erro no POST storage Supabase",{error:e}),e instanceof Error)return p.R.error(`Erro na opera\xe7\xe3o: ${e.message}`,"SUPABASE_OPERATION_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}function k(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]}let S=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/supabase/storage/route",pathname:"/api/supabase/storage",filename:"route",bundlePath:"app/api/supabase/storage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\supabase\\storage\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:R}=S,E="/api/supabase/storage/route";function y(){return(0,i.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:f})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,5972,9557,7410,2972,8525],()=>a(70740));module.exports=r})();