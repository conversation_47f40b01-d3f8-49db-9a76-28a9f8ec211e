"use strict";(()=>{var e={};e.id=8706,e.ids=[8706],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},9034:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>w,requestAsyncStorage:()=>v,routeModule:()=>g,serverHooks:()=>E,staticGenerationAsyncStorage:()=>h});var n={};t.r(n),t.d(n,{GET:()=>f,dynamic:()=>p,runtime:()=>m});var a=t(49303),o=t(88716),i=t(60670),s=t(87070),l=t(45609),u=t(43895),c=t(46029),d=t(63841);let p="force-dynamic",m="nodejs";async function f(e){try{let e=await (0,l.getServerSession)();if(!e?.user)return s.NextResponse.json({error:"N\xe3o autorizado. Fa\xe7a login para continuar."},{status:401});let r=e.user.id,t=await d.prisma.subscription.findFirst({where:{userId:r,OR:[{status:"active"},{status:"trialing"}]},orderBy:{createdAt:"desc"}});if(!t)return s.NextResponse.json({plan:c.Xf.FREE,apiCallsLimit:c.cb[c.Xf.FREE],apiCallsUsed:0,percentUsed:0});let n=new Date;n.setDate(1),n.setHours(0,0,0,0);let a=(await d.prisma.apiUsage.aggregate({where:{userId:r,createdAt:{gte:n}},_sum:{count:!0}}))._sum.count||0,o=Math.min(Math.round(a/t.apiCallsLimit*100),100);return await d.prisma.subscription.update({where:{id:t.id},data:{apiCallsUsed:a}}),s.NextResponse.json({plan:t.plan,apiCallsLimit:t.apiCallsLimit,apiCallsUsed:a,percentUsed:o,currentPeriodEnd:t.currentPeriodEnd})}catch(e){return u.kg.error("[API_USAGE_ERROR]",e),s.NextResponse.json({error:"Erro ao buscar informa\xe7\xf5es de uso."},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/user/api-usage/route",pathname:"/api/user/api-usage",filename:"route",bundlePath:"app/api/user/api-usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\user\\api-usage\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:v,staticGenerationAsyncStorage:h,serverHooks:E}=g,x="/api/user/api-usage/route";function w(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:h})}},43895:(e,r,t)=>{let n;t.d(r,{kg:()=>c});var a=t(99557),o=t.n(a);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function s(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let r=["name","message","stack"],t={};return Object.keys(e).forEach(n=>{r.includes(n)||(t[n]=e[n])}),{normalizedError:e,extractedMetadata:t}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function l(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let u={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:o().stdSerializers.err,error:o().stdSerializers.err}}};try{let e=u.production;n=o()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),n=o()({level:"info",formatters:{level:e=>({level:e})}})}let c={trace:(e,r)=>{n.trace(r||{},e)},debug:(e,r)=>{n.debug(r||{},e)},info:(e,r)=>{n.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:t}=s(r);n.warn(t,e)}else n.warn(l(r)||{},e)},error:(e,r,t)=>{let{normalizedError:a,extractedMetadata:o}=s(r),i={...t||{},...o,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};n.error(i,e)},fatal:(e,r,t)=>{let{normalizedError:a,extractedMetadata:o}=s(r),i={...t||{},...o,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};n.fatal(i,e)},createChild:e=>{let r=n.child(e);return{trace:(e,t)=>{r.trace(t||{},e)},debug:(e,t)=>{r.debug(t||{},e)},info:(e,t)=>{r.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:n}=s(t);r.warn(n,e)}else r.warn(l(t)||{},e)},error:(e,t,n)=>{let{normalizedError:a,extractedMetadata:o}=s(t),i={...n||{},...o,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.error(i,e)},fatal:(e,t,n)=>{let{normalizedError:a,extractedMetadata:o}=s(t),i={...n||{},...o,...a&&{error:{message:a.message,stack:a.stack,name:a.name}}};r.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},46029:(e,r,t)=>{t.d(r,{Ag:()=>u,Al:()=>c,Cf:()=>l,DI:()=>d,Xf:()=>o,cb:()=>i}),t(30468);var n=t(31059);let a={PRO_MONTHLY:"price_1RWHbARrKLXtzZkME498Zuab",PRO_ANNUAL:"price_1RWHckRrKLXtzZkMLLn1vFvh"},o={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"},i={[o.FREE]:50,[o.PRO_MONTHLY]:500,[o.PRO_ANNUAL]:1e3},s=process.env.STRIPE_SECRET_KEY||"",l=process.env.STRIPE_WEBHOOK_SECRET||"",u=s?new n.Z(s,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}}):null;function c(e){switch(e){case o.PRO_MONTHLY:return a.PRO_MONTHLY;case o.PRO_ANNUAL:return a.PRO_ANNUAL;default:return a.PRO_MONTHLY}}function d(e){switch(e){case"active":case"trialing":return"active";case"canceled":case"unpaid":case"incomplete_expired":return"canceled";case"past_due":return"past_due";case"incomplete":return"incomplete";default:return"unknown"}}},63841:(e,r,t)=>{t.d(r,{P:()=>l,prisma:()=>s});var n=t(53524);let a={info:(e,...r)=>{},error:(e,...r)=>{console.error(`[DB ERROR] ${e}`,...r)},warn:(e,...r)=>{console.warn(`[DB WARNING] ${e}`,...r)}},o={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],s=global.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...o,activeConnections:Math.min(Math.floor(5*Math.random())+1,o.maxPoolSize),poolSize:o.poolSize}}async function u(){try{await s.$disconnect(),a.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){a.error("Erro ao desconectar do banco de dados",e)}}s.$on("query",e=>{o.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),o.averageQueryTime=i.reduce((e,r)=>e+r,0)/i.length),e.duration&&e.duration>500&&a.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),s.$on("error",e=>{o.failedQueries++,o.connectionFailures++,o.lastConnectionFailure=new Date().toISOString(),a.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{u()})},30468:()=>{var e,r="https://js.stripe.com",t="".concat(r,"/").concat("basil","/stripe.js"),n=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,a=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,o=function(){for(var e=document.querySelectorAll('script[src^="'.concat(r,'"]')),t=0;t<e.length;t++){var o,i=e[t];if(o=i.src,n.test(o)||a.test(o))return i}return null},i=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(t).concat(r);var a=document.head||document.body;if(!a)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return a.appendChild(n),n},s=null,l=null,u=null;Promise.resolve().then(function(){return e||(e=(null!==s?s:(s=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,n=o();n?n&&null!==u&&null!==l&&(n.removeEventListener("load",u),n.removeEventListener("error",l),null===(t=n.parentNode)||void 0===t||t.removeChild(n),n=i(null)):n=i(null),u=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},l=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",u),n.addEventListener("error",l)}catch(e){r(e);return}})).catch(function(e){return s=null,Promise.reject(e)})).catch(function(r){return e=null,Promise.reject(r)}))}).catch(function(e){console.warn(e)})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,5972,9557,330,5609,1059],()=>t(9034));module.exports=n})();