"use strict";(()=>{var e={};e.id=5460,e.ids=[5460],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},98892:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>I,patchFetch:()=>k,requestAsyncStorage:()=>_,routeModule:()=>y,serverHooks:()=>f,staticGenerationAsyncStorage:()=>T});var a={};t.r(a),t.d(a,{GET:()=>m,POST:()=>v});var s=t(49303),o=t(88716),i=t(60670),n=t(6113),u=t.n(n),c=t(87070),p=t(52972),l=t(43895),d=t(89314);let E=[{key:"AUTH_NEXTAUTH_SECRET",value:u().randomBytes(32).toString("base64"),type:"encrypted",target:["production","preview"],description:"Chave secreta para NextAuth"},{key:"AUTH_NEXTAUTH_URL",value:"https://excel-copilot-eight.vercel.app",type:"plain",target:["production","preview"],description:"URL base para NextAuth"},{key:"AUTH_GOOGLE_CLIENT_ID",value:"217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com",type:"plain",target:["production","preview"],description:"Google OAuth Client ID"},{key:"AUTH_GITHUB_CLIENT_ID",value:"c5d97a325b78e452d671",type:"plain",target:["production","preview"],description:"GitHub OAuth Client ID"},{key:"AI_USE_MOCK",value:"false",type:"plain",target:["production","preview"],description:"Desativar mocks de IA em produ\xe7\xe3o"},{key:"NEXT_PUBLIC_USE_MOCK_AI",value:"false",type:"plain",target:["production","preview"],description:"Desativar mocks de IA no cliente"},{key:"AUTH_SKIP_PROVIDERS",value:"false",type:"plain",target:["production","preview"],description:"N\xe3o pular providers OAuth"},{key:"AI_USE_MOCK",value:"false",type:"plain",target:["production","preview"],description:"N\xe3o for\xe7ar mocks do Google"},{key:"NODE_ENV",value:"production",type:"plain",target:["production"],description:"Ambiente de produ\xe7\xe3o"},{key:"DEV_FORCE_PRODUCTION",value:"true",type:"plain",target:["production","preview"],description:"For\xe7ar modo produ\xe7\xe3o"},{key:"AI_ENABLED",value:"true",type:"plain",target:["production","preview"],description:"Habilitar Vertex AI"},{key:"AI_VERTEX_PROJECT_ID",value:"excel-copilot",type:"plain",target:["production","preview"],description:"ID do projeto Vertex AI"},{key:"AI_VERTEX_LOCATION",value:"us-central1",type:"plain",target:["production","preview"],description:"Localiza\xe7\xe3o do Vertex AI"},{key:"AI_VERTEX_MODEL",value:"gemini-1.5-pro",type:"plain",target:["production","preview"],description:"Modelo do Vertex AI"},{key:"NEXT_PUBLIC_APP_URL",value:"https://excel-copilot-eight.vercel.app",type:"plain",target:["production","preview"],description:"URL da aplica\xe7\xe3o"},{key:"APP_VERSION",value:"1.0.0",type:"plain",target:["production","preview"],description:"Vers\xe3o da aplica\xe7\xe3o"}],g=["AUTH_GOOGLE_CLIENT_SECRET","AUTH_GITHUB_CLIENT_SECRET","STRIPE_SECRET_KEY","NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY","STRIPE_WEBHOOK_SECRET","VERTEX_AI_CREDENTIALS"];async function m(){try{let e=p.Vi.VERCEL_API_TOKEN,r=p.Vi.VERCEL_PROJECT_ID||"excel-copilot-eight";if(!e)return c.NextResponse.json({success:!1,error:"VERCEL_API_TOKEN n\xe3o configurado",message:"Configure o token da Vercel para usar esta funcionalidade"},{status:400});let t={apiToken:e,projectId:r};p.Vi.VERCEL_TEAM_ID&&(t.teamId=p.Vi.VERCEL_TEAM_ID);let a=new d.w(t),s=await a.getEnvironmentVariables(),o=s.map(e=>e.key),i=E.filter(e=>!o.includes(e.key)),n=g.filter(e=>!o.includes(e));return c.NextResponse.json({success:!0,data:{total:s.length,existing:o,missing:{critical:i.map(e=>({key:e.key,description:e.description})),manual:n},status:{criticalConfigured:E.length-i.length,criticalTotal:E.length,manualConfigured:g.length-n.length,manualTotal:g.length,readyForAuth:0===i.length&&n.length<=2}}})}catch(e){return l.kg.error("Erro ao listar vari\xe1veis de ambiente",e),c.NextResponse.json({success:!1,error:"Erro interno",message:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}async function v(e){try{let{action:r,variables:t}=await e.json(),a=p.Vi.VERCEL_API_TOKEN,s=p.Vi.VERCEL_PROJECT_ID||"excel-copilot-eight";if(!a)return c.NextResponse.json({success:!1,error:"VERCEL_API_TOKEN n\xe3o configurado",message:"Configure o token da Vercel para usar esta funcionalidade"},{status:400});let o={apiToken:a,projectId:s};p.Vi.VERCEL_TEAM_ID&&(o.teamId=p.Vi.VERCEL_TEAM_ID);let i=new d.w(o);if("configure-critical"===r){l.kg.info("Configurando vari\xe1veis cr\xedticas para resolver problema de autentica\xe7\xe3o");let e=await i.setMultipleEnvironmentVariables(E.map(e=>({key:e.key,value:e.value,target:[...e.target],type:e.type})));return c.NextResponse.json({success:e.success,data:{summary:e.summary,results:e.results,nextSteps:["Configure GOOGLE_CLIENT_SECRET no painel da Vercel","Configure GITHUB_CLIENT_SECRET no painel da Vercel","Atualize URLs de callback nos consoles OAuth","Fa\xe7a redeploy do projeto"]},message:`Configura\xe7\xe3o conclu\xedda: ${e.summary.configured} configuradas, ${e.summary.skipped} puladas, ${e.summary.failed} falharam`})}if("configure-custom"===r&&t){let e=await i.setMultipleEnvironmentVariables(t);return c.NextResponse.json({success:e.success,data:{summary:e.summary,results:e.results},message:`Configura\xe7\xe3o customizada: ${e.summary.configured} configuradas, ${e.summary.failed} falharam`})}{if("update-existing"!==r||!t)return c.NextResponse.json({success:!1,error:"A\xe7\xe3o inv\xe1lida",message:'Use action: "configure-critical" ou "configure-custom" com variables'},{status:400});let e=[],a=0,s=0,o=await i.getEnvironmentVariables();for(let r of t)try{let t=o.find(e=>e.key===r.key);if(t&&t.id){let o=await i.deleteEnvironmentVariable(t.id);if(o.success){await new Promise(e=>setTimeout(e,500));let t=await i.setEnvironmentVariable(r.key,r.value,r.target||["production","preview"],r.type||"plain");t.success?(e.push({key:r.key,success:!0,message:"Atualizada com sucesso"}),a++):(e.push({key:r.key,success:!1,message:`Erro ao recriar: ${t.message}`}),s++)}else e.push({key:r.key,success:!1,message:`Erro ao remover: ${o.message}`}),s++}else{let t=await i.setEnvironmentVariable(r.key,r.value,r.target||["production","preview"],r.type||"plain");e.push({key:r.key,success:t.success,message:t.success?"Criada com sucesso":t.message}),t.success?a++:s++}await new Promise(e=>setTimeout(e,300))}catch(t){e.push({key:r.key,success:!1,message:`Erro: ${t instanceof Error?t.message:"Erro desconhecido"}`}),s++}return c.NextResponse.json({success:0===s,data:{summary:{updated:a,failed:s,total:t.length},results:e},message:`Atualiza\xe7\xe3o conclu\xedda: ${a} atualizadas, ${s} falharam`})}}catch(e){return l.kg.error("Erro ao configurar vari\xe1veis de ambiente",e),c.NextResponse.json({success:!1,error:"Erro interno",message:e instanceof Error?e.message:"Erro desconhecido"},{status:500})}}let y=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/vercel/env/route",pathname:"/api/vercel/env",filename:"route",bundlePath:"app/api/vercel/env/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\env\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:_,staticGenerationAsyncStorage:T,serverHooks:f}=y,I="/api/vercel/env/route";function k(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:T})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,5972,9557,7410,2972,9314],()=>t(98892));module.exports=a})();