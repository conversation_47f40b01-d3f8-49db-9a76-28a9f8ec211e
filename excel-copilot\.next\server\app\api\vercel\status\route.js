"use strict";(()=>{var e={};e.id=8300,e.ids=[8300],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},99523:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>D,patchFetch:()=>O,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>v,staticGenerationAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>E,dynamic:()=>l,runtime:()=>R});var o=t(49303),a=t(88716),n=t(60670),i=t(52972),u=t(43895),c=t(89314),p=t(82840);let l="force-dynamic",R="nodejs";async function d(e){try{let e=process.env.MCP_VERCEL_TOKEN,r=process.env.MCP_VERCEL_PROJECT_ID,t=process.env.MCP_VERCEL_TEAM_ID;if(!e)return p.R.error("VERCEL_API_TOKEN n\xe3o configurado","VERCEL_CONFIG_ERROR",500);let s=new c.n(e,t,r),o=await s.getProjectStatus(),a=await s.getPerformanceMetrics(),n={project:{name:"excel-copilot",environment:i.Vi.IS_PRODUCTION?"production":"development",status:o.status,message:o.message,uptime:o.uptime,lastDeployment:o.lastDeployment?{id:o.lastDeployment.uid,url:o.lastDeployment.url,state:o.lastDeployment.state,created:new Date(o.lastDeployment.created).toISOString(),target:o.lastDeployment.target}:null},metrics:{requests24h:a.requests,errors24h:a.errors,errorRate:a.errorRate,averageResponseTime:a.averageResponseTime,bandwidth24h:a.bandwidth,cacheHitRate:a.cacheHitRate,recentErrors:o.recentErrors},timestamp:new Date().toISOString()};return u.kg.info("Status Vercel obtido com sucesso",{status:o.status,errors:o.recentErrors}),p.R.success(n)}catch(e){if(u.kg.error("Erro ao obter status do Vercel",{error:e}),e instanceof Error)return p.R.error(`Erro ao conectar com Vercel: ${e.message}`,"VERCEL_API_ERROR",500);return p.R.error("Erro interno do servidor","INTERNAL_ERROR",500)}}async function E(e){try{let e=process.env.MCP_VERCEL_TOKEN,r=process.env.MCP_VERCEL_PROJECT_ID,t=process.env.MCP_VERCEL_TEAM_ID;if(!e)return p.R.error("VERCEL_API_TOKEN n\xe3o configurado","VERCEL_CONFIG_ERROR",500);let s=new c.n(e,t,r),o=await s.getFilteredLogs({level:"error",limit:20}),a=await s.getProjectStatus(),n={status:a.status,message:a.message,recentErrors:o.length,errorDetails:o.slice(0,5).map(e=>({timestamp:new Date(e.timestamp).toISOString(),message:e.message,source:e.source,deploymentId:e.deploymentId})),lastDeployment:a.lastDeployment,timestamp:new Date().toISOString()};return p.R.success(n)}catch(e){return u.kg.error("Erro na verifica\xe7\xe3o for\xe7ada do Vercel",{error:e}),p.R.error("Erro ao verificar status","VERCEL_STATUS_ERROR",500)}}let m=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/vercel/status/route",pathname:"/api/vercel/status",filename:"route",bundlePath:"app/api/vercel/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\vercel\\status\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:_,serverHooks:v}=m,D="/api/vercel/status/route";function O(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:_})}},82840:(e,r,t)=>{t.d(r,{R:()=>a});var s=t(87070),o=t(43895);let a={success(e,r,t=200){let o={data:e,...r&&{meta:r}};return s.NextResponse.json(o,{status:t})},error(e,r="INTERNAL_ERROR",t=500,a){let n={code:r,message:e,timestamp:new Date().toISOString(),...void 0!==a&&{details:a}};return o.kg.error(`API Error [${r}]: ${e}`,{details:a}),s.NextResponse.json(n,{status:t})},unauthorized(e="N\xe3o autorizado",r){return this.error(e,"UNAUTHORIZED",401,r)},badRequest(e,r){return this.error(e,"BAD_REQUEST",400,r)},notFound(e="Recurso n\xe3o encontrado",r){return this.error(e,"NOT_FOUND",404,r)},forbidden(e="Acesso negado",r){return this.error(e,"FORBIDDEN",403,r)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r){let t={};return r&&(t["Retry-After"]=r.toString()),s.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:t})}}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972,9557,7410,2972,9314],()=>t(99523));module.exports=s})();