"use strict";(()=>{var e={};e.id=4124,e.ids=[4124],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},70214:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>b,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>p,serverHooks:()=>k,staticGenerationAsyncStorage:()=>m});var o={};r.r(o),r.d(o,{GET:()=>d});var s=r(49303),a=r(88716),i=r(60670),n=r(87070),l=r(45609);class c{constructor(){this.activeCollaborators=new Map,this.userSockets=new Map,this.socketUsers=new Map,this.workbookSockets=new Map}static getInstance(){return c.instance||(c.instance=new c),c.instance}addCollaborator(e,t){let r=this.activeCollaborators.get(e)||[],o=r.findIndex(e=>e.id===t.id);o>=0?r[o]=t:r.push(t),this.activeCollaborators.set(e,r),this.userSockets.set(t.id,t.socket),this.socketUsers.set(t.socket,t.id);let s=this.workbookSockets.get(e)||new Set;s.add(t.socket),this.workbookSockets.set(e,s)}removeCollaborator(e){let t=this.socketUsers.get(e);if(!t)return[];let r=[];return this.activeCollaborators.forEach((o,s)=>{let a=o.findIndex(e=>e.id===t);if(a>=0){o.splice(a,1),this.activeCollaborators.set(s,o),r.push(s);let t=this.workbookSockets.get(s);t&&(t.delete(e),0===t.size?this.workbookSockets.delete(s):this.workbookSockets.set(s,t))}}),this.userSockets.delete(t),this.socketUsers.delete(e),r}getCollaborators(e){return this.activeCollaborators.get(e)||[]}updateCollaboratorPosition(e,t){let r=this.socketUsers.get(e);if(!r)return null;let o=[];return this.activeCollaborators.forEach((e,s)=>{let a=e.find(e=>e.id===r);a&&(a.position=t,a.lastActive=new Date,a.status="active",o.push(s))}),{userId:r,workbookIds:o}}updateCollaboratorStatus(e,t){let r=[];return this.activeCollaborators.forEach((o,s)=>{let a=o.find(t=>t.id===e);a&&(a.status=t,a.lastActive=new Date,r.push(s))}),r}getWorkbookSockets(e){let t=this.workbookSockets.get(e);return t?Array.from(t):[]}getUserIdFromSocket(e){return this.socketUsers.get(e)}getSocketFromUserId(e){return this.userSockets.get(e)}}var u=r(63841);async function d(e,{params:t}){try{let e=await (0,l.getServerSession)();if(!e?.user)return n.NextResponse.json({error:"Voc\xea precisa estar autenticado"},{status:401});let r=t.id;if(!r)return n.NextResponse.json({error:"ID da planilha \xe9 obrigat\xf3rio"},{status:400});let o=e.user.id,s=await u.prisma.workbook.findUnique({where:{id:r},include:{user:{select:{id:!0,name:!0,email:!0,image:!0}}}});if(!s)return n.NextResponse.json({error:"Planilha n\xe3o encontrada"},{status:404});if(s.userId!==o&&!s.isPublic&&!await u.prisma.workbookShare.findFirst({where:{workbookId:r,sharedWithUserId:o}}))return n.NextResponse.json({error:"Acesso negado"},{status:403});let a=await u.prisma.workbookShare.findMany({where:{workbookId:r},include:{sharedWithUser:{select:{id:!0,name:!0,email:!0,image:!0}}}}),i=[];i.push({id:s.user.id,name:s.user.name||"Usu\xe1rio",email:s.user.email||"",avatar:s.user.image||void 0,role:"owner",permission:"edit",lastActive:new Date}),a.length>0&&i.push(...a.map(e=>({id:e.sharedWithUser.id,name:e.sharedWithUser.name||"Usu\xe1rio",email:e.sharedWithUser.email||"",avatar:e.sharedWithUser.image||void 0,role:"collaborator",permission:e.permissionLevel,lastActive:e.updatedAt||new Date})));let d=c.getInstance().getCollaborators(r),p=i.map(e=>{let t=d.find(t=>t.id===e.id);return{...e,status:t?t.status:"offline",position:t?t.position:void 0,lastActive:t?t.lastActive:e.lastActive}});return d.forEach(e=>{i.some(t=>t.id===e.id)||p.push({id:e.id,name:e.name||"Convidado",email:e.email||"",avatar:e.avatar,role:"guest",permission:"view",status:e.status,position:e.position||{row:0,col:0},lastActive:e.lastActive})}),n.NextResponse.json({collaborators:p})}catch(e){return console.error("Erro ao obter colaboradores:",e),n.NextResponse.json({error:"Erro interno do servidor"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/workbooks/[id]/collaborators/route",pathname:"/api/workbooks/[id]/collaborators",filename:"route",bundlePath:"app/api/workbooks/[id]/collaborators/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\[id]\\collaborators\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:k}=p,b="/api/workbooks/[id]/collaborators/route";function v(){return(0,i.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:m})}},63841:(e,t,r)=>{r.d(t,{P:()=>l,prisma:()=>n});var o=r(53524);let s={info:(e,...t)=>{},error:(e,...t)=>{console.error(`[DB ERROR] ${e}`,...t)},warn:(e,...t)=>{console.warn(`[DB WARNING] ${e}`,...t)}},a={activeConnections:0,totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null,poolSize:0,maxPoolSize:5},i=[],n=global.prisma||new o.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});function l(){return{...a,activeConnections:Math.min(Math.floor(5*Math.random())+1,a.maxPoolSize),poolSize:a.poolSize}}async function c(){try{await n.$disconnect(),s.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){s.error("Erro ao desconectar do banco de dados",e)}}n.$on("query",e=>{a.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),a.averageQueryTime=i.reduce((e,t)=>e+t,0)/i.length),e.duration&&e.duration>500&&s.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),n.$on("error",e=>{a.failedQueries++,a.connectionFailures++,a.lastConnectionFailure=new Date().toISOString(),s.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{c()})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,330,5609],()=>r(70214));module.exports=o})();