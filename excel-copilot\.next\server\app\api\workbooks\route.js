"use strict";(()=>{var e={};e.id=8854,e.ids=[8854],e.modules={53524:e=>{e.exports=require("@prisma/client")},4530:e=>{e.exports=require("@prisma/client/runtime/library")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},98188:e=>{e.exports=require("module")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},71267:e=>{e.exports=require("worker_threads")},59796:e=>{e.exports=require("zlib")},52345:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>F,patchFetch:()=>W,requestAsyncStorage:()=>L,routeModule:()=>z,serverHooks:()=>_,staticGenerationAsyncStorage:()=>T});var t={};o.r(t),o.d(t,{DELETE:()=>S,GET:()=>O,PATCH:()=>M,POST:()=>q,dynamic:()=>C,runtime:()=>R});var a=o(49303),i=o(88716),s=o(60670),n=o(43895);class c{constructor(){this.cache=new Map,this.isConnected=!0,setInterval(()=>this.cleanExpired(),3e5)}static getInstance(){return c.instance||(c.instance=new c),c.instance}cleanExpired(){let e=Date.now();for(let[r,o]of this.cache.entries())o.expiry&&o.expiry<e&&this.cache.delete(r)}async get(e){try{let r=this.cache.get(e);if(!r)return n.kg.debug("❌ Cache miss:",e),null;if(r.expiry&&r.expiry<Date.now())return this.cache.delete(e),n.kg.debug("⏰ Cache expired:",e),null;return n.kg.debug("✅ Cache hit:",e),r.value}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao buscar no cache:",r,{key:e}),null}}async set(e,r,o){try{let t={value:r,expiry:o?Date.now()+1e3*o:void 0};return this.cache.set(e,t),n.kg.debug("\uD83D\uDCBE Cache set:",e,o?`TTL: ${o}s`:"sem TTL"),!0}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao salvar no cache:",r,{key:e}),!1}}async del(e){try{let r=this.cache.has(e);return this.cache.delete(e),n.kg.debug("\uD83D\uDDD1️ Cache deleted:",e,`Existed: ${r}`),r}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao deletar do cache:",r,{key:e}),!1}}async exists(e){try{let r=this.cache.get(e);if(!r)return!1;if(r.expiry&&r.expiry<Date.now())return this.cache.delete(e),!1;return!0}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao verificar exist\xeancia no cache:",r,{key:e}),!1}}async flushPattern(e){try{let r=new RegExp(e.replace(/\*/g,".*")),o=0;for(let e of this.cache.keys())r.test(e)&&(this.cache.delete(e),o++);return n.kg.info("\uD83E\uDDF9 Cache pattern flushed:",e,`Deleted: ${o} keys`),o}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao limpar pattern do cache:",r,{pattern:e}),0}}async getStats(){try{return{connected:this.isConnected,connectionAttempts:0,memoryUsage:`${Math.round(process.memoryUsage().heapUsed/1024/1024)}MB`,keyCount:this.cache.size}}catch(e){return n.kg.error("\uD83D\uDCA5 Erro ao obter stats do cache:",e),{connected:this.isConnected,connectionAttempts:0}}}async disconnect(){this.cache.clear(),n.kg.info("\uD83D\uDC4B Cache em mem\xf3ria limpo")}}let u=c.getInstance(),d={WORKBOOK_METADATA:900,TEMPLATES:86400,POPULAR_COMMANDS:7200},l={WORKBOOK_LIST:e=>`workbooks:list:${e}`,TEMPLATES:()=>"templates:popular",POPULAR_AI_COMMANDS:()=>"ai:commands:popular"};class k{async get(e){try{let r=await u.get(e);if(null===r)return this.metrics.misses++,null;return this.metrics.hits++,JSON.parse(r)}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao buscar do cache:",r,{key:e}),null}}async set(e,r,o){try{let t=JSON.stringify(r),a=await u.set(e,t,o);return a?this.metrics.sets++:this.metrics.errors++,a}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao salvar no cache:",r,{key:e}),!1}}async delete(e){try{let r=await u.del(e);return r&&this.metrics.deletes++,r}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao deletar do cache:",r,{key:e}),!1}}async exists(e){try{return await u.exists(e)}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao verificar exist\xeancia no cache:",r,{key:e}),!1}}async getOrSet(e,r,o){let t=await this.get(e);if(null!==t)return t;try{let t=await r();return await this.set(e,t,o),t}catch(r){return n.kg.error("\uD83D\uDCA5 Erro ao executar fun\xe7\xe3o para cache:",r,{key:e}),null}}async invalidatePattern(e){try{let r=await u.flushPattern(e);return this.metrics.deletes+=r,n.kg.info("\uD83E\uDDF9 Cache invalidado por padr\xe3o:",e,`${r} chaves`),r}catch(r){return this.metrics.errors++,n.kg.error("\uD83D\uDCA5 Erro ao invalidar padr\xe3o do cache:",r,{pattern:e}),0}}async invalidateUserCache(e){let r=[`dashboard:metrics:${e}`,`workbooks:list:${e}`,`activity:recent:${e}`,`session:${e}`,`permissions:${e}:*`];await Promise.all(r.map(e=>this.invalidatePattern(e))),n.kg.info("\uD83D\uDC64 Cache do usu\xe1rio invalidado:",e)}async invalidateWorkbookCache(e){let r=[`workbook:meta:${e}`,`permissions:*:${e}`];await Promise.all(r.map(e=>this.invalidatePattern(e))),n.kg.info("\uD83D\uDCCA Cache do workbook invalidado:",e)}async warmupPopularTemplates(){try{n.kg.info("\uD83D\uDD25 Iniciando pr\xe9-aquecimento de templates populares..."),await this.set(l.TEMPLATES(),[{id:"financial",name:"Controle Financeiro",usage:1250},{id:"dashboard",name:"Dashboard",usage:980},{id:"calculator",name:"Calculadora",usage:750}],d.TEMPLATES),n.kg.info("✅ Templates populares pr\xe9-aquecidos")}catch(e){n.kg.error("\uD83D\uDCA5 Erro no pr\xe9-aquecimento de templates:",e)}}async warmupPopularAICommands(){try{n.kg.info("\uD83D\uDD25 Iniciando pr\xe9-aquecimento de comandos de IA populares..."),await this.set(l.POPULAR_AI_COMMANDS(),["Criar planilha de controle financeiro com categorias","Adicionar gr\xe1fico de barras para vendas mensais","Calcular soma total da coluna","Criar tabela din\xe2mica com dados","Formatar c\xe9lulas como moeda"],d.POPULAR_COMMANDS),n.kg.info("✅ Comandos de IA populares pr\xe9-aquecidos")}catch(e){n.kg.error("\uD83D\uDCA5 Erro no pr\xe9-aquecimento de comandos de IA:",e)}}async warmupCache(){n.kg.info("\uD83D\uDE80 Iniciando pr\xe9-aquecimento completo do cache..."),await Promise.all([this.warmupPopularTemplates(),this.warmupPopularAICommands()]),n.kg.info("\uD83C\uDF89 Pr\xe9-aquecimento do cache conclu\xeddo")}getMetrics(){let e=this.metrics.hits+this.metrics.misses,r=e>0?this.metrics.hits/e*100:0;return{...this.metrics,hitRate:Math.round(100*r)/100}}resetMetrics(){this.metrics={hits:0,misses:0,sets:0,deletes:0,errors:0}}async getDetailedStats(){return{redis:await u.getStats(),cache:this.getMetrics(),timestamp:new Date().toISOString()}}constructor(){this.metrics={hits:0,misses:0,sets:0,deletes:0,errors:0}}}let m=new k;var p=o(21270),h=o(99747),w=o(39762),g=o(62091),b=o(36044),D=o(63841),f=o(21954);async function y(e){try{let r=(0,f.ZR)(e.userId,e.endpoint,e.count??1,e.workbookId,e.billable??!0);await D.prisma.apiUsage.create({data:r})}catch(r){n.kg.error("Erro ao registrar uso da API",{userId:e.userId,endpoint:e.endpoint,error:r})}}var I=o(4579);let v={addJob:()=>Promise.resolve({id:"mock-job"}),getJobStatus:()=>Promise.resolve({status:"completed"})},x={sendNotification:()=>Promise.resolve(),scheduleNotification:()=>Promise.resolve()},A={MAX_WORKBOOKS:{free:5,pro_monthly:1/0,pro_annual:1/0},MAX_CELLS:{free:1e3,pro_monthly:5e4,pro_annual:1/0}},E={async getUserWorkbooks(e,r){try{let o=r?.limit||20,t=r?.page||0,[a,i]=await Promise.all([(0,I.jw)(()=>D.prisma.workbook.findMany({where:{userId:e,...r?.isPublic!==void 0&&{isPublic:r.isPublic},...r?.search&&{OR:[{name:{contains:r.search,mode:"insensitive"}},{description:{contains:r.search,mode:"insensitive"}}]}},include:{sheets:{select:{id:!0,name:!0,updatedAt:!0}}},orderBy:{updatedAt:"desc"},take:o,skip:t*o}),["user-workbooks",e,r],{ttl:60}),(0,I.jw)(()=>D.prisma.workbook.count({where:{userId:e,...r?.isPublic!==void 0&&{isPublic:r.isPublic},...r?.search&&{OR:[{name:{contains:r.search,mode:"insensitive"}},{description:{contains:r.search,mode:"insensitive"}}]}}}),["user-workbooks-count",e,r],{ttl:60})]),s=Math.ceil(i/o);return{workbooks:a,pagination:{page:t,limit:o,totalItems:i,totalPages:s,hasMore:t<s-1}}}catch(r){throw n.kg.error("Erro ao buscar workbooks do usu\xe1rio",{userId:e,error:r}),Error("Falha ao buscar workbooks")}},async getPublicWorkbooks(e){try{return await (0,I.jw)(()=>D.prisma.workbook.findMany({where:{isPublic:!0,...e?.search&&{OR:[{name:{contains:e.search,mode:"insensitive"}},{description:{contains:e.search,mode:"insensitive"}}]}},include:{user:{select:{id:!0,name:!0,image:!0}},sheets:{select:{id:!0,name:!0}}},orderBy:{updatedAt:"desc"},take:e?.limit||20,skip:e?.page?e.page*(e.limit||20):0}),["public-workbooks",e],{ttl:300})}catch(e){throw n.kg.error("Erro ao buscar workbooks p\xfablicos",{error:e}),Error("Falha ao buscar workbooks p\xfablicos")}},async getWorkbookById(e,r){try{let o=await D.prisma.workbook.findUnique({where:{id:e},include:{sheets:!0,user:{select:{id:!0,name:!0,image:!0}}}});if(!o)throw Error("Workbook n\xe3o encontrado");if(r&&o.userId!==r&&!o.isPublic)throw await D.prisma.securityLog.create({data:{userId:r,eventType:"unauthorized_workbook_access",details:JSON.stringify({workbookId:e,ownerId:o.userId}),timestamp:new Date}}),Error("Usu\xe1rio n\xe3o autorizado a acessar este workbook");return o}catch(o){throw n.kg.error("Erro ao buscar workbook por ID",{workbookId:e,userId:r,error:o}),o}},async createWorkbook(e,r){try{let o=await D.prisma.$transaction(async o=>{let t=await o.subscription.findFirst({where:{userId:r,OR:[{status:"active"},{status:"trialing"}]},select:{plan:!0,apiCallsLimit:!0}}),a=await o.workbook.count({where:{userId:r}}),i={FREE:"free"},s=t?.plan||i.FREE,c=A.MAX_WORKBOOKS[s]||A.MAX_WORKBOOKS[i.FREE];if(a>=c)throw Error(`Limite de planilhas excedido. Voc\xea tem ${a} de ${c} planilhas permitidas para seu plano.`);if(e.initialData){let r=function(e){if(!e)return 0;let r=0;return e.rows&&Array.isArray(e.rows)?e.rows.forEach(e=>{e&&e.cells&&Array.isArray(e.cells)&&(r+=e.cells.filter(e=>e&&null!==e.value&&void 0!==e.value).length)}):e.cells&&"object"==typeof e.cells&&(r=Object.keys(e.cells).length),r}(e.initialData),o=A.MAX_CELLS[s]||A.MAX_CELLS[i.FREE];if(r>o)throw Error(`Os dados iniciais cont\xeam ${r} c\xe9lulas, excedendo o limite de ${o} do seu plano.`)}let u=await o.workbook.create({data:{name:e.name,description:e.description||"",isPublic:e.isPublic??!1,userId:r,sheets:{create:{name:"Planilha 1",data:e.initialData?JSON.stringify(e.initialData):null}}},include:{sheets:!0}});if(e.aiCommand){n.kg.info("Workbook criado com comando de IA - iniciando processamento ass\xedncrono",{workbookId:u.id,userId:r,aiCommand:e.aiCommand.substring(0,100)+"..."});try{await v.initialize();let o=await v.addAIProcessingJob({workbookId:u.id,userId:r,command:e.aiCommand,context:{headers:[],rowCount:0,colCount:0,existingData:e.initialData},priority:"normal"});await x.sendAIProcessingStarted(r,u.id,e.aiCommand),n.kg.info("✅ Job de IA adicionado \xe0 fila",{workbookId:u.id,jobId:o.id,userId:r})}catch(e){n.kg.error("\uD83D\uDCA5 Erro ao adicionar job de IA \xe0 fila:",e,{workbookId:u.id,userId:r})}}return u});return n.kg.info("Novo workbook criado",{workbookId:o.id,userId:r}),o}catch(o){throw n.kg.error("Erro ao criar workbook",{userId:r,data:e,error:o}),o instanceof Error?o:Error("Falha ao criar workbook")}},async updateWorkbook(e,r){try{let o=await D.prisma.$transaction(async o=>{if(!await o.workbook.findFirst({where:{id:e.id,userId:r}}))throw await o.securityLog.create({data:{userId:r,eventType:"unauthorized_workbook_update",details:JSON.stringify({workbookId:e.id}),timestamp:new Date}}),Error("Workbook n\xe3o encontrado ou n\xe3o pertence ao usu\xe1rio");let t={};void 0!==e.name&&(t.name=e.name),void 0!==e.description&&(t.description=e.description),void 0!==e.isPublic&&(t.isPublic=e.isPublic);let a=await o.workbook.update({where:{id:e.id},data:t,include:{sheets:!0}});return await o.userActionLog.create({data:{userId:r,action:"workbook_updated",details:JSON.stringify({workbookId:e.id,fields:Object.keys(t)}),timestamp:new Date}}),a});return n.kg.info("Workbook atualizado",{workbookId:o.id,userId:r}),o}catch(o){throw n.kg.error("Erro ao atualizar workbook",{workbookId:e.id,userId:r,error:o}),o}},async deleteWorkbook(e,r){try{return await D.prisma.$transaction(async o=>{if(!await o.workbook.findFirst({where:{id:e,userId:r}}))throw await o.securityLog.create({data:{userId:r,eventType:"unauthorized_workbook_deletion",details:JSON.stringify({workbookId:e}),timestamp:new Date}}),Error("Workbook n\xe3o encontrado ou n\xe3o pertence ao usu\xe1rio");await o.workbook.delete({where:{id:e}}),await o.userActionLog.create({data:{userId:r,action:"workbook_deleted",details:JSON.stringify({workbookId:e}),timestamp:new Date}})}),n.kg.info("Workbook exclu\xeddo",{workbookId:e,userId:r}),!0}catch(o){throw n.kg.error("Erro ao excluir workbook",{workbookId:e,userId:r,error:o}),o}}};var P=o(82840);let C="force-dynamic",R="nodejs",O=(0,h.x)([g.VF,w.Ev,p.l],async(e,r)=>{try{let o;n.kg.info("\uD83D\uDE80 API /workbooks: Iniciando processamento",{userId:r.userId});let t=e.nextUrl.searchParams,a={isPublic:t.has("isPublic")?"true"===t.get("isPublic"):void 0,search:t.get("search")||void 0,limit:t.has("limit")?parseInt(t.get("limit"),10):void 0,page:t.has("page")?parseInt(t.get("page"),10):void 0};n.kg.info("\uD83D\uDCCB API /workbooks: Filtros extra\xeddos",a);let i=b.S9.safeParse(a);if(!i.success)return n.kg.warn("❌ API /workbooks: Filtros inv\xe1lidos",i.error),(0,w.ng)(P.R.badRequest("Par\xe2metros de filtro inv\xe1lidos",i.error.format()),r);n.kg.info("✅ API /workbooks: Filtros validados",i.data);let s=l.WORKBOOK_LIST(r.userId);i.data.search||i.data.isPublic?(n.kg.info("\uD83D\uDD0D API /workbooks: Buscando workbooks (query complexa)..."),o=await E.getUserWorkbooks(r.userId,i.data)):o=await m.getOrSet(s,()=>E.getUserWorkbooks(r.userId,i.data),d.WORKBOOK_METADATA),n.kg.info("\uD83D\uDCCA API /workbooks: Workbooks encontrados",{count:o?.workbooks?.length||0,data:o}),await y({userId:r.userId,endpoint:"workbooks/list",count:1}),n.kg.info("\uD83C\uDFAF API /workbooks: Criando resposta de sucesso");let c=P.R.success(o);return n.kg.info("\uD83D\uDCE4 API /workbooks: Resposta criada",{status:c.status,hasBody:!!c.body}),(0,w.ng)(c,r)}catch(e){if(n.kg.error("❌ API /workbooks: Erro no processamento",e),e instanceof Error)return(0,w.ng)(P.R.error(e.message),r);return(0,w.ng)(P.R.error("Erro ao buscar workbooks"),r)}}),q=(0,h.x)([g.VF,w.Ev,p.l],async(e,r)=>{try{let o=await e.json(),t=b.t9.safeParse(o);if(!t.success)return(0,w.ng)(P.R.badRequest("Dados inv\xe1lidos para criar workbook",t.error.format()),r);let a=await E.createWorkbook(t.data,r.userId);return await m.invalidateUserCache(r.userId),(0,I.e5)(`user-workbooks:${r.userId}`),await y({userId:r.userId,endpoint:"workbooks/create",count:1,workbookId:a.id}),(0,w.ng)(P.R.success(a,void 0,201),r)}catch(e){if(e instanceof Error)return(0,w.ng)(P.R.error(e.message),r);return(0,w.ng)(P.R.error("Erro ao criar workbook"),r)}}),M=(0,h.x)([g.VF,w.Ev,p.l],async(e,r)=>{try{let o=await e.json(),t=b.OR.safeParse(o);if(!t.success)return(0,w.ng)(P.R.badRequest("Dados inv\xe1lidos para atualizar workbook",t.error.format()),r);let a=await E.updateWorkbook(t.data,r.userId);return(0,I.e5)(`user-workbooks:${r.userId}`),await y({userId:r.userId,endpoint:"workbooks/update",count:1,workbookId:t.data.id}),(0,w.ng)(P.R.success(a),r)}catch(e){if(e instanceof Error)return(0,w.ng)(P.R.error(e.message),r);return(0,w.ng)(P.R.error("Erro ao atualizar workbook"),r)}}),S=(0,h.x)([g.VF,w.Ev,p.l],async(e,r)=>{try{let o=e.nextUrl.searchParams.get("id");if(!o)return(0,w.ng)(P.R.badRequest("ID do workbook \xe9 obrigat\xf3rio"),r);if(!b.ou.safeParse({id:o}).success)return(0,w.ng)(P.R.badRequest("ID de workbook inv\xe1lido"),r);return await E.deleteWorkbook(o,r.userId),(0,I.e5)(`user-workbooks:${r.userId}`),await y({userId:r.userId,endpoint:"workbooks/delete",count:1,workbookId:o}),(0,w.ng)(P.R.success({success:!0,message:"Workbook exclu\xeddo com sucesso"}),r)}catch(e){if(e instanceof Error)return(0,w.ng)(P.R.error(e.message),r);return(0,w.ng)(P.R.error("Erro ao excluir workbook"),r)}}),z=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/workbooks/route",pathname:"/api/workbooks",filename:"route",bundlePath:"app/api/workbooks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\workbooks\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:L,staticGenerationAsyncStorage:T,serverHooks:_}=z,F="/api/workbooks/route";function W(){return(0,s.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:T})}},62091:(e,r,o)=>{o.d(r,{VF:()=>c});var t=o(43895),a=o(82840);let i=new Map;function s(){let e=Date.now();for(let[r,o]of i.entries())e>o.resetAt&&i.delete(r)}function n(e=100,r=6e4,o=e=>e.ip||"unknown"){return async(n,c)=>{let u=o(n),d=Date.now();.01>Math.random()&&s();let l=i.get(u);if((!l||d>l.resetAt)&&(l={count:0,resetAt:d+r}),l.count+=1,i.set(u,l),c.headers.set("X-RateLimit-Limit",e.toString()),c.headers.set("X-RateLimit-Remaining",Math.max(0,e-l.count).toString()),c.headers.set("X-RateLimit-Reset",Math.ceil(l.resetAt/1e3).toString()),l.count>e){t.kg.warn(`Rate limit excedido para ${u}`,{path:n.nextUrl.pathname,method:n.method,key:u,count:l.count,limit:e});let r=Math.ceil((l.resetAt-d)/1e3);return a.R.tooManyRequests("Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",r)}}}"undefined"!=typeof setInterval&&setInterval(s,3e5);let c=n(100,6e4);n(20,6e4),n(30,6e4)},36044:(e,r,o)=>{o.d(r,{I9:()=>c,OR:()=>s,S9:()=>n,ou:()=>a,t9:()=>i});var t=o(7410);let a=t.z.object({id:t.z.string().cuid({message:"ID de workbook inv\xe1lido"})}),i=t.z.object({name:t.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}),description:t.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),isPublic:t.z.boolean().default(!1),initialData:t.z.any().optional(),aiCommand:t.z.string().max(1e3,{message:"Comando de IA deve ter no m\xe1ximo 1000 caracteres"}).optional()}),s=t.z.object({id:t.z.string().cuid({message:"ID de workbook inv\xe1lido"}),name:t.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}).optional(),description:t.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),isPublic:t.z.boolean().optional()});t.z.object({name:t.z.string().min(1,{message:"Nome da folha \xe9 obrigat\xf3rio"}).max(50,{message:"Nome da folha deve ter no m\xe1ximo 50 caracteres"}),workbookId:t.z.string().cuid({message:"ID de workbook inv\xe1lido"}),data:t.z.any().optional()});let n=t.z.object({isPublic:t.z.boolean().optional(),search:t.z.string().optional(),limit:t.z.number().int().positive().default(10),page:t.z.number().int().nonnegative().default(0)}),c=t.z.object({row:t.z.number().int().nonnegative(),col:t.z.number().int().nonnegative(),value:t.z.any(),formula:t.z.string().optional(),style:t.z.any().optional()})},21954:(e,r,o)=>{function t(e,r,o,t,a){return{userId:e,endpoint:r,count:o,workbookId:t||null,billable:a}}function a(e,r,o,t){return{name:e,userId:r,description:o||null,sheets:{create:t}}}function i(e,r,o,t){return{userId:e,message:r,response:o,workbookId:t||null}}o.d(r,{PL:()=>i,ZR:()=>t,fp:()=>a})}};var r=require("../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8948,5972,9557,7410,330,5609,2972,1628,5590],()=>o(52345));module.exports=t})();