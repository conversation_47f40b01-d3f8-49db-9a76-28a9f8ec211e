(()=>{var e={};e.id=6711,e.ids=[6711],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},98188:e=>{"use strict";e.exports=require("module")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},39512:e=>{"use strict";e.exports=require("timers")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},59796:e=>{"use strict";e.exports=require("zlib")},39697:()=>{},62302:()=>{},30107:()=>{},47524:()=>{},70937:(e,t,r)=>{"use strict";let o;r.r(t),r.d(t,{originalPathname:()=>E,patchFetch:()=>S,requestAsyncStorage:()=>w,routeModule:()=>y,serverHooks:()=>x,staticGenerationAsyncStorage:()=>I});var s={};r.r(s),r.d(s,{GET:()=>f,dynamic:()=>b});var a=r(49303),i=r(88716),n=r(60670),c=r(45609),d=r(43895),u=r(7410),l=r(36044);let m=u.z.discriminatedUnion("type",[u.z.object({type:u.z.literal("join_room"),workbookId:u.z.string().cuid({message:"ID de workbook inv\xe1lido"})}),u.z.object({type:u.z.literal("leave_room"),workbookId:u.z.string().cuid({message:"ID de workbook inv\xe1lido"})}),u.z.object({type:u.z.literal("cell_update"),workbookId:u.z.string().cuid({message:"ID de workbook inv\xe1lido"}),sheetId:u.z.string().cuid({message:"ID de sheet inv\xe1lido"}),cell:l.I9}),u.z.object({type:u.z.literal("user_activity"),workbookId:u.z.string().cuid({message:"ID de workbook inv\xe1lido"}),sheetId:u.z.string().cuid({message:"ID de sheet inv\xe1lido"}),activity:u.z.enum(["typing","editing","idle"]),position:u.z.object({row:u.z.number().int().nonnegative().optional(),col:u.z.number().int().nonnegative().optional()}).optional()}),u.z.object({type:u.z.literal("add_comment"),workbookId:u.z.string().cuid({message:"ID de workbook inv\xe1lido"}),sheetId:u.z.string().cuid({message:"ID de sheet inv\xe1lido"}),position:u.z.object({row:u.z.number().int().nonnegative(),col:u.z.number().int().nonnegative()}),content:u.z.string().min(1,{message:"Conte\xfado do coment\xe1rio \xe9 obrigat\xf3rio"})}),u.z.object({type:u.z.literal("heartbeat")})]);u.z.discriminatedUnion("type",[u.z.object({type:u.z.literal("cell_updated"),sheetId:u.z.string().cuid(),cell:l.I9,userId:u.z.string(),userName:u.z.string().optional(),timestamp:u.z.number()}),u.z.object({type:u.z.literal("user_presence"),workbookId:u.z.string().cuid(),users:u.z.array(u.z.object({id:u.z.string(),name:u.z.string().optional(),status:u.z.enum(["connected","disconnected"]),activity:u.z.enum(["typing","editing","idle"]).optional(),position:u.z.object({sheetId:u.z.string().optional(),row:u.z.number().int().nonnegative().optional(),col:u.z.number().int().nonnegative().optional()}).optional()}))}),u.z.object({type:u.z.literal("error"),message:u.z.string(),details:u.z.any().optional()})]);var p=r(90117);class g{static init(){if(this.instance)return this.instance;try{let e={path:"/api/ws",transports:["websocket","polling"],cors:{origin:process.env.NEXT_PUBLIC_BASE_URL||"*",methods:["GET","POST"],credentials:!0},connectTimeout:3e4,pingTimeout:2e4,pingInterval:25e3,maxHttpBufferSize:1e6};return this.instance=new p.xF(e),d.kg.info("Servidor WebSocket inicializado"),this.instance.on("connection",e=>{e.setMaxListeners(20),d.kg.debug(`Socket conectado: ${e.id}`)}),this.instance}catch(e){throw d.kg.error("Erro ao inicializar servidor WebSocket",e),e}}static getInstance(){return this.instance||null}static shutdown(){this.instance&&(this.instance.close(),this.instance=null,d.kg.info("Servidor WebSocket encerrado"))}static broadcast(e,t,r){if(!this.instance){d.kg.warn("Tentativa de broadcast sem servidor WebSocket inicializado");return}this.instance.to(e).emit(t,r)}static async getClientsInRoom(e){return this.instance?(await this.instance.in(e).fetchSockets()).length:0}static async getRoomsInfo(){if(!this.instance)return{};let e={};for(let t of(await this.instance.fetchSockets()))for(let r of t.rooms)r!==t.id&&r.startsWith("workbook:")&&(e[r]=(e[r]||0)+1);return e}}var k=r(82840);let b="force-dynamic",z=!1;async function f(e){try{let t=e.headers.get("upgrade");if("websocket"!==t)return k.R.badRequest("Requisi\xe7\xe3o n\xe3o \xe9 uma conex\xe3o WebSocket");let r=await (0,c.getServerSession)();if(!r||!r.user)return k.R.unauthorized("Usu\xe1rio n\xe3o autenticado");return z||(o=g.init(),z=!0,o.on("connection",e=>{let t=r.user?.id,o=r.user?.name||"Usu\xe1rio an\xf4nimo";d.kg.debug("Nova conex\xe3o WebSocket",{socketId:e.id,userId:t}),e.data.userId=t,e.data.userName=o,e.on("message",async t=>{try{let o=m.safeParse(t);if(!o.success){e.emit("error",{type:"error",message:"Formato de mensagem inv\xe1lido",details:o.error.format()});return}let s=o.data;switch(s.type){case"join_room":await v(e,s.workbookId);break;case"leave_room":var r;r=s.workbookId,e.leave(`workbook:${r}`),e.to(`workbook:${r}`).emit("user_presence",{type:"user_presence",workbookId:r,users:[{id:e.data.userId,name:e.data.userName,status:"disconnected"}]}),d.kg.debug("Usu\xe1rio saiu da sala",{workbookId:r,userId:e.data.userId});break;case"cell_update":(function(e,t){let{workbookId:r,sheetId:o,cell:s}=t;e.to(`workbook:${r}`).emit("cell_updated",{type:"cell_updated",workbookId:r,sheetId:o,cell:s,userId:e.data.userId,userName:e.data.userName,timestamp:Date.now()}),d.kg.debug("C\xe9lula atualizada",{workbookId:r,sheetId:o,cell:`${s.row},${s.col}`,userId:e.data.userId})})(e,s);break;case"user_activity":(function(e,t){let{workbookId:r,sheetId:o,activity:s,position:a}=t;e.data.activity=s,a&&(e.data.position={sheetId:o,...a}),e.to(`workbook:${r}`).emit("user_presence",{type:"user_presence",workbookId:r,users:[{id:e.data.userId,name:e.data.userName,status:"connected",activity:s,position:e.data.position}]})})(e,s);break;case"heartbeat":e.emit("pong",{timestamp:Date.now()});break;default:e.emit("error",{type:"error",message:"Tipo de mensagem desconhecido"})}}catch(t){d.kg.error("Erro ao processar mensagem WebSocket",{socketId:e.id,userId:e.data.userId,error:t}),e.emit("error",{type:"error",message:"Erro ao processar mensagem"})}}),e.on("disconnect",()=>{(function(e){let t=Array.from(e.rooms).filter(e=>e.startsWith("workbook:")).map(e=>e.replace("workbook:",""));t.forEach(t=>{e.to(`workbook:${t}`).emit("user_presence",{type:"user_presence",workbookId:t,users:[{id:e.data.userId,name:e.data.userName,status:"disconnected"}]})}),d.kg.debug("Usu\xe1rio desconectado",{userId:e.data.userId,socketId:e.id,rooms:t})})(e)})})),new Response(null,{status:101,headers:{Upgrade:"websocket",Connection:"Upgrade"}})}catch(e){return d.kg.error("Erro ao configurar WebSocket",e),k.R.error("Erro ao configurar WebSocket")}}async function v(e,t){try{if(!await h(t,e.data.userId)){e.emit("error",{type:"error",message:"Sem permiss\xe3o para acessar este workbook"});return}e.join(`workbook:${t}`),e.to(`workbook:${t}`).emit("user_presence",{type:"user_presence",workbookId:t,users:[{id:e.data.userId,name:e.data.userName,status:"connected",activity:"idle"}]});let r=(await o.in(`workbook:${t}`).fetchSockets()).map(e=>({id:e.data.userId,name:e.data.userName,status:"connected",activity:e.data.activity||"idle",position:e.data.position}));e.emit("user_presence",{type:"user_presence",workbookId:t,users:r}),d.kg.debug("Usu\xe1rio entrou na sala",{workbookId:t,userId:e.data.userId,socketId:e.id})}catch(r){d.kg.error("Erro ao entrar na sala",{workbookId:t,userId:e.data.userId,error:r}),e.emit("error",{type:"error",message:"Erro ao entrar na sala"})}}async function h(e,t){try{return!0}catch(r){return d.kg.error("Erro ao verificar acesso ao workbook",{workbookId:e,userId:t,error:r}),!1}}let y=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/ws/route",pathname:"/api/ws",filename:"route",bundlePath:"app/api/ws/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\api\\ws\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:I,serverHooks:x}=y,E="/api/ws/route";function S(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:I})}},43895:(e,t,r)=>{"use strict";let o;r.d(t,{kg:()=>u});var s=r(99557),a=r.n(s);function i(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{return Error(JSON.stringify(e))}catch{return Error("Unknown error")}}}function n(e){if(null==e)return{normalizedError:void 0,extractedMetadata:{}};if(e instanceof Error){let t=["name","message","stack"],r={};return Object.keys(e).forEach(o=>{t.includes(o)||(r[o]=e[o])}),{normalizedError:e,extractedMetadata:r}}return"object"==typeof e&&null!==e?{normalizedError:i(e),extractedMetadata:e}:{normalizedError:i(e),extractedMetadata:{}}}function c(e){return null==e?void 0:"object"==typeof e&&null!==e?e:{value:e}}let d={development:{level:"debug",formatters:{level:e=>({level:e}),log:e=>e},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err},timestamp:()=>`,"time":"${new Date().toLocaleString("pt-BR",{timeZone:"America/Sao_Paulo",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}"`},test:{level:"error",enabled:"true"===process.env.DEBUG_LOGS},production:{level:"info",enabled:!0,formatters:{level:e=>({level:e})},serializers:{err:a().stdSerializers.err,error:a().stdSerializers.err}}};try{let e=d.production;o=a()(e)}catch(e){console.warn("Falha ao inicializar Pino logger, usando fallback:",e),o=a()({level:"info",formatters:{level:e=>({level:e})}})}let u={trace:(e,t)=>{o.trace(t||{},e)},debug:(e,t)=>{o.debug(t||{},e)},info:(e,t)=>{o.info(t||{},e)},warn:(e,t)=>{if(t instanceof Error||"object"==typeof t&&null!==t){let{extractedMetadata:r}=n(t);o.warn(r,e)}else o.warn(c(t)||{},e)},error:(e,t,r)=>{let{normalizedError:s,extractedMetadata:a}=n(t),i={...r||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};o.error(i,e)},fatal:(e,t,r)=>{let{normalizedError:s,extractedMetadata:a}=n(t),i={...r||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};o.fatal(i,e)},createChild:e=>{let t=o.child(e);return{trace:(e,r)=>{t.trace(r||{},e)},debug:(e,r)=>{t.debug(r||{},e)},info:(e,r)=>{t.info(r||{},e)},warn:(e,r)=>{if(r instanceof Error||"object"==typeof r&&null!==r){let{extractedMetadata:o}=n(r);t.warn(o,e)}else t.warn(c(r)||{},e)},error:(e,r,o)=>{let{normalizedError:s,extractedMetadata:a}=n(r),i={...o||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.error(i,e)},fatal:(e,r,o)=>{let{normalizedError:s,extractedMetadata:a}=n(r),i={...o||{},...a,...s&&{error:{message:s.message,stack:s.stack,name:s.name}}};t.fatal(i,e)}}},child:function(e){return this.createChild(e)}}},36044:(e,t,r)=>{"use strict";r.d(t,{I9:()=>c,OR:()=>i,S9:()=>n,ou:()=>s,t9:()=>a});var o=r(7410);let s=o.z.object({id:o.z.string().cuid({message:"ID de workbook inv\xe1lido"})}),a=o.z.object({name:o.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),isPublic:o.z.boolean().default(!1),initialData:o.z.any().optional(),aiCommand:o.z.string().max(1e3,{message:"Comando de IA deve ter no m\xe1ximo 1000 caracteres"}).optional()}),i=o.z.object({id:o.z.string().cuid({message:"ID de workbook inv\xe1lido"}),name:o.z.string().min(1,{message:"Nome da planilha \xe9 obrigat\xf3rio"}).max(100,{message:"Nome da planilha deve ter no m\xe1ximo 100 caracteres"}).optional(),description:o.z.string().max(500,{message:"Descri\xe7\xe3o deve ter no m\xe1ximo 500 caracteres"}).optional(),isPublic:o.z.boolean().optional()});o.z.object({name:o.z.string().min(1,{message:"Nome da folha \xe9 obrigat\xf3rio"}).max(50,{message:"Nome da folha deve ter no m\xe1ximo 50 caracteres"}),workbookId:o.z.string().cuid({message:"ID de workbook inv\xe1lido"}),data:o.z.any().optional()});let n=o.z.object({isPublic:o.z.boolean().optional(),search:o.z.string().optional(),limit:o.z.number().int().positive().default(10),page:o.z.number().int().nonnegative().default(0)}),c=o.z.object({row:o.z.number().int().nonnegative(),col:o.z.number().int().nonnegative(),value:o.z.any(),formula:o.z.string().optional(),style:o.z.any().optional()})},82840:(e,t,r)=>{"use strict";r.d(t,{R:()=>a});var o=r(87070),s=r(43895);let a={success(e,t,r=200){let s={data:e,...t&&{meta:t}};return o.NextResponse.json(s,{status:r})},error(e,t="INTERNAL_ERROR",r=500,a){let i={code:t,message:e,timestamp:new Date().toISOString(),...void 0!==a&&{details:a}};return s.kg.error(`API Error [${t}]: ${e}`,{details:a}),o.NextResponse.json(i,{status:r})},unauthorized(e="N\xe3o autorizado",t){return this.error(e,"UNAUTHORIZED",401,t)},badRequest(e,t){return this.error(e,"BAD_REQUEST",400,t)},notFound(e="Recurso n\xe3o encontrado",t){return this.error(e,"NOT_FOUND",404,t)},forbidden(e="Acesso negado",t){return this.error(e,"FORBIDDEN",403,t)},tooManyRequests(e="Muitas requisi\xe7\xf5es. Tente novamente mais tarde.",t){let r={};return t&&(r["Retry-After"]=t.toString()),o.NextResponse.json({code:"RATE_LIMIT_EXCEEDED",message:e,timestamp:new Date().toISOString()},{status:429,headers:r})}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972,9557,7410,330,5609,117],()=>r(70937));module.exports=o})();