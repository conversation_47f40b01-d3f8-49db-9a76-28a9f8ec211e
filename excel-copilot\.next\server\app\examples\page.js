(()=>{var e={};e.id=4668,e.ids=[4668],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},85339:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c}),r(82577),r(65675),r(12523);var t=r(23191),o=r(88716),a=r(37922),n=r.n(a),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let c=["",{children:["examples",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,82577)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\examples\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\examples\\page.tsx"],p="/examples/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/examples/page",pathname:"/examples",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},17594:(e,s,r)=>{Promise.resolve().then(r.bind(r,75982))},82577:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(19510),o=r(68570);let a=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook-templates.tsx`),{__esModule:n,$$typeof:i}=a;a.default,(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook-templates.tsx#WorkbookTemplates`);let l=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\workbook-templates.tsx#WorkbookTemplatesServer`);function c(){return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-10",children:[t.jsx("h1",{className:"text-3xl font-bold tracking-tight mb-4",children:"Modelos e Exemplos"}),t.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Comece rapidamente com nossos modelos e veja exemplos de comandos poderosos."})]}),(0,t.jsxs)("section",{className:"py-8",children:[t.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Modelos Prontos"}),t.jsx(l,{})]}),(0,t.jsxs)("section",{className:"py-8",children:[t.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Exemplos de Comandos"}),(0,t.jsxs)("div",{className:"bg-card p-6 rounded-lg border shadow-sm",children:[t.jsx("p",{className:"mb-4",children:"Confira exemplos de comandos que voc\xea pode usar com nossa IA:"}),(0,t.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[t.jsx("li",{children:"Crie uma planilha de controle financeiro com categorias de gastos"}),t.jsx("li",{children:"Gere um gr\xe1fico de barras com os dados das colunas A e B"}),t.jsx("li",{children:"Some os valores da coluna Vendas"}),t.jsx("li",{children:"Filtre os dados onde Pre\xe7o \xe9 maior que 100"}),t.jsx("li",{children:"Converta a planilha atual para formato de tabela"}),t.jsx("li",{children:"Crie uma tabela din\xe2mica agrupando vendas por regi\xe3o"}),t.jsx("li",{children:"Fa\xe7a uma an\xe1lise de correla\xe7\xe3o entre pre\xe7o e demanda"})]})]})]})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,9557,7410,86,7915,5999,2972,4433,6841,611],()=>r(85339));module.exports=t})();