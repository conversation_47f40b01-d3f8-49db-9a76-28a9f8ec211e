"use strict";(()=>{var e={};e.id=3703,e.ids=[3703],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},86208:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>v,patchFetch:()=>h,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{GET:()=>l});var a=r(49303),s=r(88716),n=r(60670),i=r(55661);let p=process.env.NEXT_PUBLIC_APP_URL||"https://excel-copilot.vercel.app";var u=r(60707);async function l(){let e=await {rules:{userAgent:"*",allow:"/",disallow:["/api/","/admin/","/user-profile/","/_next/","/private/","/checkout/","/auth/"]},sitemap:`${p}/sitemap.xml`,host:p},t=(0,u.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let d=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Frobots.txt%2Froute&filePath=C%3A%5CUsers%5CMn%5CDesktop%5Cdo%20vscode%5Cexcel-copilot%5Csrc%5Capp%5Crobots.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:x,serverHooks:m}=d,v="/robots.txt/route";function h(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:x})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,1346],()=>r(86208));module.exports=o})();