(()=>{var e={};e.id=2483,e.ids=[2483],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},23532:e=>{"use strict";e.exports=require("exceljs")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},98188:e=>{"use strict";e.exports=require("module")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},85477:e=>{"use strict";e.exports=require("punycode")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},59796:e=>{"use strict";e.exports=require("zlib")},51532:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>u,routeModule:()=>x,tree:()=>l}),r(98910),r(36958),r(65675),r(12523);var s=r(23191),o=r(88716),i=r(37922),a=r.n(i),n=r(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(t,c);let l=["",{children:["workbook",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,98910)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,36958)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],u=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\workbook\\new\\page.tsx"],p="/workbook/new/page",d={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/workbook/new/page",pathname:"/workbook/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},68915:(e,t,r)=>{Promise.resolve().then(r.bind(r,4120))},35303:()=>{},4120:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(10326),o=r(75290),i=r(35047),a=r(77109),n=r(17577);r(85999);var c=r(24079);let l={headers:["A","B","C","D","E"],rows:[["","","","",""],["","","","",""],["","","","",""],["","","","",""],["","","","",""]],charts:[],name:"Nova Planilha"};function u(){let{status:e}=(0,a.useSession)();(0,i.useSearchParams)(),(0,i.useRouter)();let[t,r]=(0,n.useState)(null),[u,p]=(0,n.useState)(!0);return"loading"===e||u?s.jsx("div",{className:"h-full w-full flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[s.jsx(o.Z,{className:"h-10 w-10 animate-spin text-primary"}),s.jsx("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):"unauthenticated"===e?null:s.jsx("div",{className:"w-full h-[calc(100vh-64px)] flex flex-col",children:s.jsx(n.Suspense,{fallback:(0,s.jsxs)("div",{className:"h-full w-full flex items-center justify-center",children:[s.jsx(o.Z,{className:"h-8 w-8 animate-spin text-primary"}),s.jsx("span",{className:"ml-2",children:"Preparando nova planilha..."})]}),children:s.jsx(c.SpreadsheetEditor,{workbookId:"new",initialData:l,initialCommand:t})})})}},38238:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},36958:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>o});var s=r(19510);let o={title:"Nova Planilha | Excel Copilot",description:"Crie uma nova planilha e comece a trabalhar com o poder da IA"};function i({children:e}){return s.jsx(s.Fragment,{children:e})}},98910:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>n});var s=r(68570);let o=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\workbook\new\page.tsx`),{__esModule:i,$$typeof:a}=o;o.default;let n=(0,s.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\app\workbook\new\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,9557,7410,86,7915,5999,2956,2495,9817,2972,4433,6841,304,4079],()=>r(51532));module.exports=s})();