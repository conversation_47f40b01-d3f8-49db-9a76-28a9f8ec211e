"use strict";exports.id=6348,exports.ids=[6348],exports.modules={36348:(e,t,r)=>{r.r(t),r.d(t,{BaseHealthCheck:()=>c,HEALTH_CHECK_CONFIGS:()=>i,HealthCheckLogger:()=>m,TIMEOUTS:()=>a,createHealthReport:()=>u,createHealthResult:()=>o,determineOverallStatus:()=>h,determineSeverity:()=>l,healthLogger:()=>d,measureTime:()=>s,withTimeout:()=>n});let i={database:{timeout:5e3,retries:2,interval:3e4,enabled:!0},auth:{timeout:3e3,retries:1,interval:6e4,enabled:!0},ai:{timeout:1e4,retries:1,interval:12e4,enabled:!0},stripe:{timeout:5e3,retries:2,interval:6e4,enabled:!0},mcp:{timeout:8e3,retries:1,interval:3e5,enabled:!0}},a={DATABASE_QUERY:5e3,AUTH_VALIDATION:3e3,AI_REQUEST:1e4,STRIPE_API:5e3,MCP_REQUEST:8e3,OVERALL_CHECK:3e4};async function n(e,t,r="Operation timed out"){return Promise.race([e,new Promise((e,i)=>{setTimeout(()=>i(Error(r)),t)})])}async function s(e){let t=Date.now();try{let r=await e(),i=Date.now()-t;return{result:r,time:i}}catch(e){throw{error:e,time:Date.now()-t}}}function l(e,t){return"healthy"===t?"low":["database","auth"].includes(e)?"unhealthy"===t?"critical":"high":["stripe","ai"].includes(e)?"unhealthy"===t?"high":"medium":"unhealthy"===t?"medium":"low"}function o(e,t,r,i){let a={service:e,status:t,responseTime:r,timestamp:new Date().toISOString(),severity:l(e,t)};return void 0!==i&&(a.details=i),a}function h(e){if(0===e.length)return"unknown";let t=e.map(e=>e.status),r=["database","auth"];return e.filter(e=>r.includes(e.service)).some(e=>"unhealthy"===e.status)?"unhealthy":t.some(e=>"degraded"===e||"unhealthy"===e)?"degraded":t.every(e=>"healthy"===e)?"healthy":"unknown"}function u(e,t){let r={healthy:e.filter(e=>"healthy"===e.status).length,unhealthy:e.filter(e=>"unhealthy"===e.status).length,degraded:e.filter(e=>"degraded"===e.status).length,total:e.length};return{overall:h(e),timestamp:new Date().toISOString(),responseTime:t,services:e,summary:r}}class c{constructor(e,t){this.serviceName=e,this.config={...i[e]||{},...t}}async execute(){if(!this.config.enabled)return o(this.serviceName,"unknown",0,{message:"Health check disabled"});let e=null;for(let t=0;t<=this.config.retries;t++)try{let{result:e,time:t}=await s(()=>n(this.check(),this.config.timeout,`${this.serviceName} health check timed out`));return o(this.serviceName,e.status,t,e.details)}catch(i){let r=i&&"object"==typeof i&&"error"in i?i.error:i;e=r instanceof Error?r:Error("Unknown error"),t<this.config.retries&&await new Promise(e=>setTimeout(e,1e3*(t+1)))}return o(this.serviceName,"unhealthy",0,{error:e?.message||"Unknown error",message:`Failed after ${this.config.retries+1} attempts`})}}class m{static getInstance(){return m.instance||(m.instance=new m),m.instance}log(e,t,r){new Date().toISOString()}info(e,t){this.log("info",e,t)}warn(e,t){this.log("warn",e,t)}error(e,t){this.log("error",e,t)}}let d=m.getInstance()}};