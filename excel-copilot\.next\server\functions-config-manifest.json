{"version": 1, "functions": {"/api/api-docs": {}, "/api/auth-callback": {}, "/api/auth/[...nextauth]": {}, "/api/auth/capture-oauth-error": {}, "/api/auth/check-env": {}, "/api/auth/debug-flow": {}, "/api/auth/debug-google": {}, "/api/auth/debug-oauth": {}, "/api/auth/debug-providers": {}, "/api/auth/debug": {}, "/api/auth/health": {}, "/api/auth/reset-rate-limit": {}, "/api/auth/test-google": {}, "/api/auth/test-login": {}, "/api/analytics/vitals": {}, "/api/auth/test-providers": {}, "/api/billing/customer-portal": {}, "/api/chat": {}, "/api/checkout": {}, "/api/checkout/trial": {}, "/api/csrf": {}, "/api/db-status": {}, "/api/excel/download/[id]": {}, "/api/excel": {}, "/api/github/issues": {}, "/api/github/repositories": {}, "/api/github/status": {}, "/api/github/workflows": {}, "/api/health/db": {}, "/api/health": {}, "/api/legacy-redirect/[...path]": {}, "/api/linear/teams": {}, "/api/migration-example": {}, "/api/metrics": {}, "/api/socket": {}, "/api/stripe/customers": {}, "/api/stripe/payments": {}, "/api/stripe/status": {}, "/api/stripe/subscriptions": {}, "/api/supabase/status": {}, "/api/supabase/storage": {}, "/api/supabase/tables": {}, "/api/trpc/[trpc]": {}, "/api/user/api-usage": {}, "/api/user/subscription": {}, "/api/vercel/deployments": {}, "/api/vercel/logs": {}, "/api/vercel/status": {}, "/api/webhooks/stripe": {}, "/api/workbook/save": {}, "/api/workbooks/[id]/export": {}, "/api/workbooks/[id]": {}, "/api/workbooks/[id]/sheets/[sheetId]/chunks": {}, "/api/workbooks/recent": {}, "/api/admin/security-stats": {}, "/api/workbooks": {}, "/api/workbooks/shared": {}, "/api/admin/subscription-integrity": {}, "/api/auth/test-config": {}, "/terms": {}, "/api/ws": {}, "/privacy": {}}}