"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1567],{77515:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},38711:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},58215:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("BarChart4",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M13 17V9",key:"1fwyjl"}],["path",{d:"M18 17V5",key:"sfb6ij"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},77424:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},34567:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},24241:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},70518:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},40933:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6884:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},23787:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},36356:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("FileSpreadsheet",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M14 17h2",key:"10kma7"}]])},22023:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},74109:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},37733:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},39451:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]])},97529:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},39127:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2h0V5z",key:"uf6l00"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h0",key:"k2aug8"}]])},92513:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},54817:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},24258:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},33907:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},92222:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},10883:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},58184:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},11240:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},74697:function(e,t,n){n.d(t,{Z:function(){return a}});let a=(0,n(81066).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66648:function(e,t,n){n.d(t,{default:function(){return i.a}});var a=n(61669),i=n.n(a)},87138:function(e,t,n){n.d(t,{default:function(){return i.a}});var a=n(231),i=n.n(a)},38173:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let a=n(99920),i=n(41452),r=n(57437),o=i._(n(2265)),d=a._(n(54887)),u=a._(n(28321)),l=n(80497),s=n(7103),c=n(93938);n(72301);let f=n(60291),h=a._(n(21241)),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1};function y(e,t,n,a,i,r,o){let d=null==e?void 0:e.src;e&&e["data-loaded-src"]!==d&&(e["data-loaded-src"]=d,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let a=!1,i=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>a,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{a=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==a?void 0:a.current)&&a.current(e)}}))}function p(e){let[t,n]=o.version.split(".",2),a=parseInt(t,10),i=parseInt(n,10);return a>18||18===a&&i>=3?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let g=(0,o.forwardRef)((e,t)=>{let{src:n,srcSet:a,sizes:i,height:d,width:u,decoding:l,className:s,style:c,fetchPriority:f,placeholder:h,loading:m,unoptimized:g,fill:v,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:M,setShowAltText:k,sizesInput:x,onLoad:j,onError:P,...S}=e;return(0,r.jsx)("img",{...S,...p(f),loading:m,width:u,height:d,decoding:l,"data-nimg":v?"fill":"1",className:s,style:c,sizes:i,srcSet:a,src:n,ref:(0,o.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(P&&(e.src=e.src),e.complete&&y(e,h,b,w,M,g,x))},[n,h,b,w,M,P,g,x,t]),onLoad:e=>{y(e.currentTarget,h,b,w,M,g,x)},onError:e=>{k(!0),"empty"!==h&&M(!0),P&&P(e)}})});function v(e){let{isAppRouter:t,imgAttributes:n}=e,a={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...p(n.fetchPriority)};return t&&d.default.preload?(d.default.preload(n.src,a),null):(0,r.jsx)(u.default,{children:(0,r.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...a},"__nimg-"+n.src+n.srcSet+n.sizes)})}let b=(0,o.forwardRef)((e,t)=>{let n=(0,o.useContext)(f.RouterContext),a=(0,o.useContext)(c.ImageConfigContext),i=(0,o.useMemo)(()=>{let e=m||a||s.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),n=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:n}},[a]),{onLoad:d,onLoadingComplete:u}=e,y=(0,o.useRef)(d);(0,o.useEffect)(()=>{y.current=d},[d]);let p=(0,o.useRef)(u);(0,o.useEffect)(()=>{p.current=u},[u]);let[b,w]=(0,o.useState)(!1),[M,k]=(0,o.useState)(!1),{props:x,meta:j}=(0,l.getImgProps)(e,{defaultLoader:h.default,imgConf:i,blurComplete:b,showAltText:M});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{...x,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:y,onLoadingCompleteRef:p,setBlurComplete:w,setShowAltText:k,sizesInput:e.sizes,ref:t}),j.priority?(0,r.jsx)(v,{isAppRouter:!n,imgAttributes:x}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82901:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return a}});let a=n(99920)._(n(2265)).default.createContext({})},40687:function(e,t){function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:a=!1}=void 0===e?{}:e;return t||n&&a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},80497:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return d}}),n(72301);let a=n(51564),i=n(7103);function r(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function d(e,t){var n;let d,u,l,{src:s,sizes:c,unoptimized:f=!1,priority:h=!1,loading:m,className:y,quality:p,width:g,height:v,fill:b=!1,style:w,overrideSrc:M,onLoad:k,onLoadingComplete:x,placeholder:j="empty",blurDataURL:P,fetchPriority:S,layout:C,objectFit:W,objectPosition:z,lazyBoundary:_,lazyRoot:D,...Z}=e,{imgConf:O,showAltText:T,blurComplete:A,defaultLoader:E}=t,I=O||i.imageConfigDefault;if("allSizes"in I)d=I;else{let e=[...I.deviceSizes,...I.imageSizes].sort((e,t)=>e-t),t=I.deviceSizes.sort((e,t)=>e-t);d={...I,allSizes:e,deviceSizes:t}}if(void 0===E)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let q=Z.loader||E;delete Z.loader,delete Z.srcSet;let V="__next_img_default"in q;if(V){if("custom"===d.loader)throw Error('Image with src "'+s+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=q;q=t=>{let{config:n,...a}=t;return e(a)}}if(C){"fill"===C&&(b=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[C];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[C];t&&!c&&(c=t)}let F="",Y=o(g),R=o(v);if("object"==typeof(n=s)&&(r(n)||void 0!==n.src)){let e=r(s)?s.default:s;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(u=e.blurWidth,l=e.blurHeight,P=P||e.blurDataURL,F=e.src,!b){if(Y||R){if(Y&&!R){let t=Y/e.width;R=Math.round(e.height*t)}else if(!Y&&R){let t=R/e.height;Y=Math.round(e.width*t)}}else Y=e.width,R=e.height}}let H=!h&&("lazy"===m||void 0===m);(!(s="string"==typeof s?s:F)||s.startsWith("data:")||s.startsWith("blob:"))&&(f=!0,H=!1),d.unoptimized&&(f=!0),V&&s.endsWith(".svg")&&!d.dangerouslyAllowSVG&&(f=!0),h&&(S="high");let X=o(p),L=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:W,objectPosition:z}:{},T?{}:{color:"transparent"},w),N=A||"empty"===j?null:"blur"===j?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:Y,heightInt:R,blurWidth:u,blurHeight:l,blurDataURL:P||"",objectFit:L.objectFit})+'")':'url("'+j+'")',B=N?{backgroundSize:L.objectFit||"cover",backgroundPosition:L.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:N}:{},U=function(e){let{config:t,src:n,unoptimized:a,width:i,quality:r,sizes:o,loader:d}=e;if(a)return{src:n,srcSet:void 0,sizes:void 0};let{widths:u,kind:l}=function(e,t,n){let{deviceSizes:a,allSizes:i}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let a;a=e.exec(n);a)t.push(parseInt(a[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=a[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:a,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,o),s=u.length-1;return{sizes:o||"w"!==l?o:"100vw",srcSet:u.map((e,a)=>d({config:t,src:n,quality:r,width:e})+" "+("w"===l?e:a+1)+l).join(", "),src:d({config:t,src:n,quality:r,width:u[s]})}}({config:d,src:s,unoptimized:f,width:Y,quality:X,sizes:c,loader:q});return{props:{...Z,loading:H?"lazy":m,fetchPriority:S,width:Y,height:R,decoding:"async",className:y,style:{...L,...B},sizes:U.sizes,srcSet:U.srcSet,src:M||U.src},meta:{unoptimized:f,priority:h,placeholder:j,fill:b}}}},28321:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return y},defaultHead:function(){return c}});let a=n(99920),i=n(41452),r=n(57437),o=i._(n(2265)),d=a._(n(65960)),u=n(82901),l=n(36590),s=n(40687);function c(e){void 0===e&&(e=!1);let t=[(0,r.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,r.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(72301);let h=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:n}=t;return e.reduce(f,[]).reverse().concat(c(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,a={};return i=>{let r=!0,o=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){o=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?r=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?r=!1:t.add(i.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)n.has(t)?r=!1:n.add(t);else{let e=i.props[t],n=a[t]||new Set;("name"!==t||!o)&&n.has(e)?r=!1:(n.add(e),a[t]=n)}}}}return r}}()).reverse().map((e,t)=>{let a=e.key||t;if(!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:a})})}let y=function(e){let{children:t}=e,n=(0,o.useContext)(u.AmpStateContext),a=(0,o.useContext)(l.HeadManagerContext);return(0,r.jsx)(d.default,{reduceComponentsToState:m,headManager:a,inAmpMode:(0,s.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51564:function(e,t){function n(e){let{widthInt:t,heightInt:n,blurWidth:a,blurHeight:i,blurDataURL:r,objectFit:o}=e,d=a?40*a:t,u=i?40*i:n,l=d&&u?"viewBox='0 0 "+d+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+l+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(l?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+r+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},93938:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return r}});let a=n(99920)._(n(2265)),i=n(7103),r=a.default.createContext(i.imageConfigDefault)},7103:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return a}});let n=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},61669:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return u},getImageProps:function(){return d}});let a=n(99920),i=n(80497),r=n(38173),o=a._(n(21241));function d(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let u=r.Image},21241:function(e,t){function n(e){let{config:t,src:n,width:a,quality:i}=e;return t.path+"?url="+encodeURIComponent(n)+"&w="+a+"&q="+(i||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),n.__next_img_default=!0;let a=n},65960:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let a=n(2265),i="undefined"==typeof window,r=i?()=>{}:a.useLayoutEffect,o=i?()=>{}:a.useEffect;function d(e){let{headManager:t,reduceComponentsToState:n}=e;function d(){if(t&&t.mountedInstances){let i=a.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(i,e))}}if(i){var u;null==t||null==(u=t.mountedInstances)||u.add(e.children),d()}return r(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),r(()=>(t&&(t._pendingUpdate=d),()=>{t&&(t._pendingUpdate=d)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},38364:function(e,t,n){n.d(t,{f:function(){return d}});var a=n(2265),i=n(25171),r=n(57437),o=a.forwardRef((e,t)=>(0,r.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var d=o},62447:function(e,t,n){n.d(t,{VY:function(){return D},aV:function(){return z},fC:function(){return W},xz:function(){return _}});var a=n(2265),i=n(78149),r=n(98324),o=n(53398),d=n(31383),u=n(25171),l=n(87513),s=n(91715),c=n(53201),f=n(57437),h="Tabs",[m,y]=(0,r.b)(h,[o.Pc]),p=(0,o.Pc)(),[g,v]=m(h),b=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,onValueChange:i,defaultValue:r,orientation:o="horizontal",dir:d,activationMode:m="automatic",...y}=e,p=(0,l.gm)(d),[v,b]=(0,s.T)({prop:a,onChange:i,defaultProp:null!=r?r:"",caller:h});return(0,f.jsx)(g,{scope:n,baseId:(0,c.M)(),value:v,onValueChange:b,orientation:o,dir:p,activationMode:m,children:(0,f.jsx)(u.WV.div,{dir:p,"data-orientation":o,...y,ref:t})})});b.displayName=h;var w="TabsList",M=a.forwardRef((e,t)=>{let{__scopeTabs:n,loop:a=!0,...i}=e,r=v(w,n),d=p(n);return(0,f.jsx)(o.fC,{asChild:!0,...d,orientation:r.orientation,dir:r.dir,loop:a,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":r.orientation,...i,ref:t})})});M.displayName=w;var k="TabsTrigger",x=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,disabled:r=!1,...d}=e,l=v(k,n),s=p(n),c=S(l.baseId,a),h=C(l.baseId,a),m=a===l.value;return(0,f.jsx)(o.ck,{asChild:!0,...s,focusable:!r,active:m,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":h,"data-state":m?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:c,...d,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{r||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||r||!e||l.onValueChange(a)})})})});x.displayName=k;var j="TabsContent",P=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,forceMount:r,children:o,...l}=e,s=v(j,n),c=S(s.baseId,i),h=C(s.baseId,i),m=i===s.value,y=a.useRef(m);return a.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(d.z,{present:r||m,children:n=>{let{present:a}=n;return(0,f.jsx)(u.WV.div,{"data-state":m?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":c,hidden:!a,id:h,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:a&&o})}})});function S(e,t){return"".concat(e,"-trigger-").concat(t)}function C(e,t){return"".concat(e,"-content-").concat(t)}P.displayName=j;var W=b,z=M,_=x,D=P},33142:function(e,t,n){n.d(t,{Q:function(){return v}});let a=Symbol.for("constructDateFrom");function i(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&a in e?e[a](t):e instanceof Date?new e.constructor(t):new Date(t)}let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};var o=n(7423);let d={date:(0,o.l)({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:(0,o.l)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,o.l)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},u={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};var l=n(91980);let s={ordinalNumber:(e,t)=>{let n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:(0,l.Y)({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:(0,l.Y)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,l.Y)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:(0,l.Y)({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:(0,l.Y)({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};var c=n(36572);let f={code:"en-US",formatDistance:(e,t,n)=>{let a;let i=r[e];return(a="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+a:a+" ago":a},formatLong:d,formatRelative:(e,t,n,a)=>u[e],localize:s,match:{ordinalNumber:(0,n(12602).y)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,c.t)({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,c.t)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,c.t)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,c.t)({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,c.t)({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},h={};function m(e,t){return i(t||e,e)}function y(e){let t=m(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function p(e){for(var t=arguments.length,n=Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];let r=i.bind(null,e||n.find(e=>"object"==typeof e));return n.map(r)}function g(e,t){let n=+m(e)-+m(t);return n<0?-1:n>0?1:n}function v(e,t){return function(e,t,n){var a,i,r,o,d,u;let l;let s=null!==(i=null!==(a=null==n?void 0:n.locale)&&void 0!==a?a:h.locale)&&void 0!==i?i:f,c=g(e,t);if(isNaN(c))throw RangeError("Invalid time value");let v=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:c}),[b,w]=p(null==n?void 0:n.in,...c>0?[t,e]:[e,t]),M=(r=w,o=b,(u=null==void 0?void 0:(void 0).roundingMethod,e=>{let t=(u?Math[u]:Math.trunc)(e);return 0===t?0:t})((+m(r)-+m(o))/1e3)),k=Math.round((M-(y(w)-y(b))/1e3)/60);if(k<2){if(null==n?void 0:n.includeSeconds){if(M<5)return s.formatDistance("lessThanXSeconds",5,v);if(M<10)return s.formatDistance("lessThanXSeconds",10,v);if(M<20)return s.formatDistance("lessThanXSeconds",20,v);if(M<40)return s.formatDistance("halfAMinute",0,v);else if(M<60)return s.formatDistance("lessThanXMinutes",1,v);else return s.formatDistance("xMinutes",1,v)}return 0===k?s.formatDistance("lessThanXMinutes",1,v):s.formatDistance("xMinutes",k,v)}if(k<45)return s.formatDistance("xMinutes",k,v);if(k<90)return s.formatDistance("aboutXHours",1,v);if(k<1440)return s.formatDistance("aboutXHours",Math.round(k/60),v);if(k<2520)return s.formatDistance("xDays",1,v);if(k<43200)return s.formatDistance("xDays",Math.round(k/1440),v);if(k<86400)return l=Math.round(k/43200),s.formatDistance("aboutXMonths",l,v);if((l=function(e,t,n){let[a,i,r]=p(void 0,e,e,t),o=g(i,r),d=Math.abs(function(e,t,n){let[a,i]=p(void 0,e,t);return 12*(a.getFullYear()-i.getFullYear())+(a.getMonth()-i.getMonth())}(i,r));if(d<1)return 0;1===i.getMonth()&&i.getDate()>27&&i.setDate(30),i.setMonth(i.getMonth()-o*d);let u=g(i,r)===-o;(function(e,t){let n=m(e,void 0);return+function(e,t){let n=m(e,null==t?void 0:t.in);return n.setHours(23,59,59,999),n}(n,void 0)==+function(e,t){let n=m(e,null==t?void 0:t.in),a=n.getMonth();return n.setFullYear(n.getFullYear(),a+1,0),n.setHours(23,59,59,999),n}(n,void 0)})(a)&&1===d&&1===g(a,r)&&(u=!1);let l=o*(d-+u);return 0===l?0:l}(w,b))<12)return s.formatDistance("xMonths",Math.round(k/43200),v);{let e=l%12,t=Math.trunc(l/12);return e<3?s.formatDistance("aboutXYears",t,v):e<9?s.formatDistance("overXYears",t,v):s.formatDistance("almostXYears",t+1,v)}}(e,i(e,Date.now()),t)}},7423:function(e,t,n){n.d(t,{l:function(){return a}});function a(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}},91980:function(e,t,n){n.d(t,{Y:function(){return a}});function a(e){return(t,n)=>{let a;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,i=(null==n?void 0:n.width)?String(n.width):t;a=e.formattingValues[i]||e.formattingValues[t]}else{let t=e.defaultWidth,i=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;a=e.values[i]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}},36572:function(e,t,n){function a(e){return function(t){let n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=a.width,r=i&&e.matchPatterns[i]||e.matchPatterns[e.defaultMatchWidth],o=t.match(r);if(!o)return null;let d=o[0],u=i&&e.parsePatterns[i]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(u)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(u,e=>e.test(d)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(u,e=>e.test(d));return n=e.valueCallback?e.valueCallback(l):l,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(d.length)}}}n.d(t,{t:function(){return a}})},12602:function(e,t,n){n.d(t,{y:function(){return a}});function a(e){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.match(e.matchPattern);if(!a)return null;let i=a[0],r=t.match(e.parsePattern);if(!r)return null;let o=e.valueCallback?e.valueCallback(r[0]):r[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:t.slice(i.length)}}}},47201:function(e,t,n){n.d(t,{F:function(){return s}});let a={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 m\xeas",other:"cerca de {{count}} meses"},xMonths:{one:"1 m\xeas",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}};var i=n(7423);let r={date:(0,i.l)({formats:{full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,i.l)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,i.l)({formats:{full:"{{date}} '\xe0s' {{time}}",long:"{{date}} '\xe0s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:e=>{let t=e.getDay();return"'"+(0===t||6===t?"\xfaltimo":"\xfaltima")+"' eeee '\xe0s' p"},yesterday:"'ontem \xe0s' p",today:"'hoje \xe0s' p",tomorrow:"'amanh\xe3 \xe0s' p",nextWeek:"eeee '\xe0s' p",other:"P"};var d=n(91980);let u={ordinalNumber:(e,t)=>{let n=Number(e);return(null==t?void 0:t.unit)==="week"?n+"\xaa":n+"\xba"},era:(0,d.Y)({values:{narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},defaultWidth:"wide"}),quarter:(0,d.Y)({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xba trimestre","2\xba trimestre","3\xba trimestre","4\xba trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,d.Y)({values:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","mar\xe7o","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},defaultWidth:"wide"}),day:(0,d.Y)({values:{narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","ter\xe7a","quarta","quinta","sexta","s\xe1bado"],wide:["domingo","segunda-feira","ter\xe7a-feira","quarta-feira","quinta-feira","sexta-feira","s\xe1bado"]},defaultWidth:"wide"}),dayPeriod:(0,d.Y)({values:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manh\xe3",afternoon:"tarde",evening:"tarde",night:"noite"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manh\xe3",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},defaultFormattingWidth:"wide"})};var l=n(36572);let s={code:"pt-BR",formatDistance:(e,t,n)=>{let i;let r=a[e];return(i="string"==typeof r?r:1===t?r.one:r.other.replace("{{count}}",String(t)),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"em "+i:"h\xe1 "+i:i},formatLong:r,formatRelative:(e,t,n,a)=>{let i=o[e];return"function"==typeof i?i(t):i},localize:u,match:{ordinalNumber:(0,n(12602).y)({matchPattern:/^(\d+)[ºªo]?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,l.t)({matchPatterns:{narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},defaultParseWidth:"any"}),quarter:(0,l.t)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,l.t)({matchPatterns:{narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},defaultParseWidth:"any"}),day:(0,l.t)({matchPatterns:{narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},defaultMatchWidth:"wide",parsePatterns:{short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},defaultParseWidth:"any"}),dayPeriod:(0,l.t)({matchPatterns:{narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}}}]);