"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[193],{78369:function(e,n,t){t.d(n,{Ry:function(){return l}});var r=new WeakMap,o=new WeakMap,a={},u=0,i=function(e){return e&&(e.host||i(e.parentNode))},c=function(e,n,t,c){var l=(Array.isArray(e)?e:[e]).map(function(e){if(n.contains(e))return e;var t=i(e);return t&&n.contains(t)?t:(console.error("aria-hidden",e,"in not contained inside",n,". Doing nothing"),null)}).filter(function(e){return!!e});a[t]||(a[t]=new WeakMap);var d=a[t],s=[],f=new Set,p=new Set(l),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};l.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var n=e.getAttribute(c),a=null!==n&&"false"!==n,u=(r.get(e)||0)+1,i=(d.get(e)||0)+1;r.set(e,u),d.set(e,i),s.push(e),1===u&&a&&o.set(e,!0),1===i&&e.setAttribute(t,"true"),a||e.setAttribute(c,"true")}catch(n){console.error("aria-hidden: cannot operate on ",e,n)}})};return h(n),f.clear(),u++,function(){s.forEach(function(e){var n=r.get(e)-1,a=d.get(e)-1;r.set(e,n),d.set(e,a),n||(o.has(e)||e.removeAttribute(c),o.delete(e)),a||e.removeAttribute(t)}),--u||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},l=function(e,n,t){void 0===t&&(t="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=n||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),c(r,o,t,"aria-hidden")):function(){return null}}},87592:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(81066).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},28165:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(81066).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},49418:function(e,n,t){t.d(n,{Z:function(){return q}});var r,o,a,u,i,c,l,d=function(){return(d=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)};function s(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>n.indexOf(r)&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>n.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]]);return t}"function"==typeof SuppressedError&&SuppressedError;var f=t(2265),p="right-scroll-bar-position",v="width-before-scroll-bar";function h(e,n){return"function"==typeof e?e(n):e&&(e.current=n),e}var m="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,g=new WeakMap,w=(void 0===o&&(o={}),(void 0===a&&(a=function(e){return e}),u=[],i=!1,c={read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return u.length?u[u.length-1]:null},useMedium:function(e){var n=a(e,i);return u.push(n),function(){u=u.filter(function(e){return e!==n})}},assignSyncMedium:function(e){for(i=!0;u.length;){var n=u;u=[],n.forEach(e)}u={push:function(n){return e(n)},filter:function(){return u}}},assignMedium:function(e){i=!0;var n=[];if(u.length){var t=u;u=[],t.forEach(e),n=u}var r=function(){var t=n;n=[],t.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),u={push:function(e){n.push(e),o()},filter:function(e){return n=n.filter(e),u}}}}).options=d({async:!0,ssr:!1},o),c),y=function(){},b=f.forwardRef(function(e,n){var t,r,o,a,u=f.useRef(null),i=f.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),c=i[0],l=i[1],p=e.forwardProps,v=e.children,b=e.className,x=e.removeScrollBar,M=e.enabled,E=e.shards,C=e.sideCar,R=e.noIsolation,k=e.inert,S=e.allowPinchZoom,j=e.as,D=e.gapMode,P=s(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(t=[u,n],r=function(e){return t.forEach(function(n){return h(n,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,m(function(){var e=g.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach(function(e){r.has(e)||h(e,null)}),r.forEach(function(e){n.has(e)||h(e,o)})}g.set(a,t)},[t]),a),N=d(d({},P),c);return f.createElement(f.Fragment,null,M&&f.createElement(C,{sideCar:w,removeScrollBar:x,shards:E,noIsolation:R,inert:k,setCallbacks:l,allowPinchZoom:!!S,lockRef:u,gapMode:D}),p?f.cloneElement(f.Children.only(v),d(d({},N),{ref:T})):f.createElement(void 0===j?"div":j,d({},N,{className:b,ref:T}),v))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:v,zeroRight:p};var x=function(e){var n=e.sideCar,t=s(e,["sideCar"]);if(!n)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=n.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,d({},t))};x.isSideCarExport=!0;var M=function(){var e=0,n=null;return{add:function(o){if(0==e&&(n=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var n=r||t.nc;return n&&e.setAttribute("nonce",n),e}())){var a,u;(a=n).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),u=n,(document.head||document.getElementsByTagName("head")[0]).appendChild(u)}e++},remove:function(){--e||!n||(n.parentNode&&n.parentNode.removeChild(n),n=null)}}},E=function(){var e=M();return function(n,t){f.useEffect(function(){return e.add(n),function(){e.remove()}},[n&&t])}},C=function(){var e=E();return function(n){return e(n.styles,n.dynamic),null}},R={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},S=function(e){var n=window.getComputedStyle(document.body),t=n["padding"===e?"paddingLeft":"marginLeft"],r=n["padding"===e?"paddingTop":"marginTop"],o=n["padding"===e?"paddingRight":"marginRight"];return[k(t),k(r),k(o)]},j=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var n=S(e),t=document.documentElement.clientWidth,r=window.innerWidth;return{left:n[0],top:n[1],right:n[2],gap:Math.max(0,r-t+n[2]-n[0])}},D=C(),P="data-scroll-locked",T=function(e,n,t,r){var o=e.left,a=e.top,u=e.right,i=e.gap;return void 0===t&&(t="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(P,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([n&&"position: relative ".concat(r,";"),"margin"===t&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===t&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(p," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(v," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(v," .").concat(v," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(P,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},N=function(){var e=parseInt(document.body.getAttribute(P)||"0",10);return isFinite(e)?e:0},_=function(){f.useEffect(function(){return document.body.setAttribute(P,(N()+1).toString()),function(){var e=N()-1;e<=0?document.body.removeAttribute(P):document.body.setAttribute(P,e.toString())}},[])},I=function(e){var n=e.noRelative,t=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;_();var a=f.useMemo(function(){return j(o)},[o]);return f.createElement(D,{styles:T(a,!n,o,t?"":"!important")})},L=!1;if("undefined"!=typeof window)try{var A=Object.defineProperty({},"passive",{get:function(){return L=!0,!0}});window.addEventListener("test",A,A),window.removeEventListener("test",A,A)}catch(e){L=!1}var O=!!L&&{passive:!1},F=function(e,n){if(!(e instanceof Element))return!1;var t=window.getComputedStyle(e);return"hidden"!==t[n]&&!(t.overflowY===t.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===t[n])},W=function(e,n){var t=n.ownerDocument,r=n;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),K(e,r)){var o=B(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==t.body);return!1},K=function(e,n){return"v"===e?F(n,"overflowY"):F(n,"overflowX")},B=function(e,n){return"v"===e?[n.scrollTop,n.scrollHeight,n.clientHeight]:[n.scrollLeft,n.scrollWidth,n.clientWidth]},V=function(e,n,t,r,o){var a,u=(a=window.getComputedStyle(n).direction,"h"===e&&"rtl"===a?-1:1),i=u*r,c=t.target,l=n.contains(c),d=!1,s=i>0,f=0,p=0;do{var v=B(e,c),h=v[0],m=v[1]-v[2]-u*h;(h||m)&&K(e,c)&&(f+=m,p+=h),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!l&&c!==document.body||l&&(n.contains(c)||n===c));return s&&(o&&1>Math.abs(f)||!o&&i>f)?d=!0:!s&&(o&&1>Math.abs(p)||!o&&-i>p)&&(d=!0),d},Z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},X=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},Y=0,G=[],z=(l=function(e){var n=f.useRef([]),t=f.useRef([0,0]),r=f.useRef(),o=f.useState(Y++)[0],a=f.useState(C)[0],u=f.useRef(e);f.useEffect(function(){u.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var n=(function(e,n,t){if(t||2==arguments.length)for(var r,o=0,a=n.length;o<a;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return n.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),n.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=f.useCallback(function(e,n){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=Z(e),i=t.current,c="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,s=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===s&&"range"===d.type)return!1;var f=W(s,d);if(!f)return!0;if(f?o=s:(o="v"===s?"h":"v",f=W(s,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var p=r.current||o;return V(p,n,e,"h"===p?c:l,!0)},[]),c=f.useCallback(function(e){if(G.length&&G[G.length-1]===a){var t="deltaY"in e?X(e):Z(e),r=n.current.filter(function(n){var r;return n.name===e.type&&(n.target===e.target||e.target===n.shadowParent)&&(r=n.delta)[0]===t[0]&&r[1]===t[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(U).filter(Boolean).filter(function(n){return n.contains(e.target)});(o.length>0?i(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),l=f.useCallback(function(e,t,r,o){var a={name:e,delta:t,target:r,should:o,shadowParent:function(e){for(var n=null;null!==e;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}(r)};n.current.push(a),setTimeout(function(){n.current=n.current.filter(function(e){return e!==a})},1)},[]),d=f.useCallback(function(e){t.current=Z(e),r.current=void 0},[]),s=f.useCallback(function(n){l(n.type,X(n),n.target,i(n,e.lockRef.current))},[]),p=f.useCallback(function(n){l(n.type,Z(n),n.target,i(n,e.lockRef.current))},[]);f.useEffect(function(){return G.push(a),e.setCallbacks({onScrollCapture:s,onWheelCapture:s,onTouchMoveCapture:p}),document.addEventListener("wheel",c,O),document.addEventListener("touchmove",c,O),document.addEventListener("touchstart",d,O),function(){G=G.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,O),document.removeEventListener("touchmove",c,O),document.removeEventListener("touchstart",d,O)}},[]);var v=e.removeScrollBar,h=e.inert;return f.createElement(f.Fragment,null,h?f.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?f.createElement(I,{gapMode:e.gapMode}):null)},w.useMedium(l),x),H=f.forwardRef(function(e,n){return f.createElement(b,d({},e,{ref:n,sideCar:z}))});H.classNames=b.classNames;var q=H},81622:function(e,n,t){t.d(n,{oC:function(){return e6},VY:function(){return e5},ZA:function(){return e8},ck:function(){return e7},wU:function(){return ne},__:function(){return e3},Uv:function(){return e2},Ee:function(){return e9},Rk:function(){return e4},fC:function(){return e0},Z0:function(){return nn},Tr:function(){return nt},tu:function(){return no},fF:function(){return nr},xz:function(){return e1}});var r=t(2265),o=t(78149),a=t(1584),u=t(98324),i=t(91715),c=t(25171),l=t(38620),d=t(87513),s=t(53938),f=t(20589),p=t(80467),v=t(53201),h=t(77683),m=t(56935),g=t(31383),w=t(53398),y=t(71538),b=t(75137),x=t(78369),M=t(49418),E=t(57437),C=["Enter"," "],R=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...R],S={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},j={ltr:["ArrowLeft"],rtl:["ArrowRight"]},D="Menu",[P,T,N]=(0,l.B)(D),[_,I]=(0,u.b)(D,[N,h.D7,w.Pc]),L=(0,h.D7)(),A=(0,w.Pc)(),[O,F]=_(D),[W,K]=_(D),B=e=>{let{__scopeMenu:n,open:t=!1,children:o,dir:a,onOpenChange:u,modal:i=!0}=e,c=L(n),[l,s]=r.useState(null),f=r.useRef(!1),p=(0,b.W)(u),v=(0,d.gm)(a);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,E.jsx)(h.fC,{...c,children:(0,E.jsx)(O,{scope:n,open:t,onOpenChange:p,content:l,onContentChange:s,children:(0,E.jsx)(W,{scope:n,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:i,children:o})})})};B.displayName=D;var V=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e,o=L(t);return(0,E.jsx)(h.ee,{...o,...r,ref:n})});V.displayName="MenuAnchor";var Z="MenuPortal",[X,U]=_(Z,{forceMount:void 0}),Y=e=>{let{__scopeMenu:n,forceMount:t,children:r,container:o}=e,a=F(Z,n);return(0,E.jsx)(X,{scope:n,forceMount:t,children:(0,E.jsx)(g.z,{present:t||a.open,children:(0,E.jsx)(m.h,{asChild:!0,container:o,children:r})})})};Y.displayName=Z;var G="MenuContent",[z,H]=_(G),q=r.forwardRef((e,n)=>{let t=U(G,e.__scopeMenu),{forceMount:r=t.forceMount,...o}=e,a=F(G,e.__scopeMenu),u=K(G,e.__scopeMenu);return(0,E.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,E.jsx)(g.z,{present:r||a.open,children:(0,E.jsx)(P.Slot,{scope:e.__scopeMenu,children:u.modal?(0,E.jsx)(J,{...o,ref:n}):(0,E.jsx)(Q,{...o,ref:n})})})})}),J=r.forwardRef((e,n)=>{let t=F(G,e.__scopeMenu),u=r.useRef(null),i=(0,a.e)(n,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,x.Ry)(e)},[]),(0,E.jsx)(ee,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),Q=r.forwardRef((e,n)=>{let t=F(G,e.__scopeMenu);return(0,E.jsx)(ee,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),$=(0,y.Z8)("MenuContent.ScrollLock"),ee=r.forwardRef((e,n)=>{let{__scopeMenu:t,loop:u=!1,trapFocus:i,onOpenAutoFocus:c,onCloseAutoFocus:l,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:x,disableOutsideScroll:C,...S}=e,j=F(G,t),D=K(G,t),P=L(t),N=A(t),_=T(t),[I,O]=r.useState(null),W=r.useRef(null),B=(0,a.e)(n,W,j.onContentChange),V=r.useRef(0),Z=r.useRef(""),X=r.useRef(0),U=r.useRef(null),Y=r.useRef("right"),H=r.useRef(0),q=C?M.Z:r.Fragment,J=e=>{var n,t;let r=Z.current+e,o=_().filter(e=>!e.disabled),a=document.activeElement,u=null===(n=o.find(e=>e.ref.current===a))||void 0===n?void 0:n.textValue,i=function(e,n,t){var r;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=(r=Math.max(t?e.indexOf(t):-1,0),e.map((n,t)=>e[(r+t)%e.length]));1===o.length&&(a=a.filter(e=>e!==t));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==t?u:void 0}(o.map(e=>e.textValue),r,u),c=null===(t=o.find(e=>e.textValue===i))||void 0===t?void 0:t.ref.current;!function e(n){Z.current=n,window.clearTimeout(V.current),""!==n&&(V.current=window.setTimeout(()=>e(""),1e3))}(r),c&&setTimeout(()=>c.focus())};r.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.EW)();let Q=r.useCallback(e=>{var n,t,r;return Y.current===(null===(n=U.current)||void 0===n?void 0:n.side)&&!!(r=null===(t=U.current)||void 0===t?void 0:t.area)&&function(e,n){let{x:t,y:r}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],i=n[a],c=u.x,l=u.y,d=i.x,s=i.y;l>r!=s>r&&t<(d-c)*(r-l)/(s-l)+c&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)},[]);return(0,E.jsx)(z,{scope:t,searchRef:Z,onItemEnter:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:r.useCallback(e=>{var n;Q(e)||(null===(n=W.current)||void 0===n||n.focus(),O(null))},[Q]),onTriggerLeave:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:X,onPointerGraceIntentChange:r.useCallback(e=>{U.current=e},[]),children:(0,E.jsx)(q,{...C?{as:$,allowPinchZoom:!0}:void 0,children:(0,E.jsx)(p.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.M)(c,e=>{var n;e.preventDefault(),null===(n=W.current)||void 0===n||n.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,E.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:x,children:(0,E.jsx)(w.fC,{asChild:!0,...N,dir:D.dir,orientation:"vertical",loop:u,currentTabStopId:I,onCurrentTabStopIdChange:O,onEntryFocus:(0,o.M)(v,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,E.jsx)(h.VY,{role:"menu","aria-orientation":"vertical","data-state":ej(j.open),"data-radix-menu-content":"",dir:D.dir,...P,...S,ref:B,style:{outline:"none",...S.style},onKeyDown:(0,o.M)(S.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!t&&r&&J(e.key));let o=W.current;if(e.target!==o||!k.includes(e.key))return;e.preventDefault();let a=_().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let t of e)if(t===n||(t.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),Z.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,eT(e=>{let n=e.target,t=H.current!==e.clientX;if(e.currentTarget.contains(n)&&t){let n=e.clientX>H.current?"right":"left";Y.current=n,H.current=e.clientX}}))})})})})})})});q.displayName=G;var en=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e;return(0,E.jsx)(c.WV.div,{role:"group",...r,ref:n})});en.displayName="MenuGroup";var et=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e;return(0,E.jsx)(c.WV.div,{...r,ref:n})});et.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ea=r.forwardRef((e,n)=>{let{disabled:t=!1,onSelect:u,...i}=e,l=r.useRef(null),d=K(er,e.__scopeMenu),s=H(er,e.__scopeMenu),f=(0,a.e)(n,l),p=r.useRef(!1);return(0,E.jsx)(eu,{...i,ref:f,disabled:t,onClick:(0,o.M)(e.onClick,()=>{let e=l.current;if(!t&&e){let n=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,c.jH)(e,n),n.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:n=>{var t;null===(t=e.onPointerDown)||void 0===t||t.call(e,n),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var n;p.current||null===(n=e.currentTarget)||void 0===n||n.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let n=""!==s.searchRef.current;!t&&(!n||" "!==e.key)&&C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=er;var eu=r.forwardRef((e,n)=>{let{__scopeMenu:t,disabled:u=!1,textValue:i,...l}=e,d=H(er,t),s=A(t),f=r.useRef(null),p=(0,a.e)(n,f),[v,h]=r.useState(!1),[m,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var n;g((null!==(n=e.textContent)&&void 0!==n?n:"").trim())}},[l.children]),(0,E.jsx)(P.ItemSlot,{scope:t,disabled:u,textValue:null!=i?i:m,children:(0,E.jsx)(w.ck,{asChild:!0,...s,focusable:!u,children:(0,E.jsx)(c.WV.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...l,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,eT(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eT(e=>d.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>h(!0)),onBlur:(0,o.M)(e.onBlur,()=>h(!1))})})})}),ei=r.forwardRef((e,n)=>{let{checked:t=!1,onCheckedChange:r,...a}=e;return(0,E.jsx)(eh,{scope:e.__scopeMenu,checked:t,children:(0,E.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eD(t)?"mixed":t,...a,ref:n,"data-state":eP(t),onSelect:(0,o.M)(a.onSelect,()=>null==r?void 0:r(!!eD(t)||!t),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var ec="MenuRadioGroup",[el,ed]=_(ec,{value:void 0,onValueChange:()=>{}}),es=r.forwardRef((e,n)=>{let{value:t,onValueChange:r,...o}=e,a=(0,b.W)(r);return(0,E.jsx)(el,{scope:e.__scopeMenu,value:t,onValueChange:a,children:(0,E.jsx)(en,{...o,ref:n})})});es.displayName=ec;var ef="MenuRadioItem",ep=r.forwardRef((e,n)=>{let{value:t,...r}=e,a=ed(ef,e.__scopeMenu),u=t===a.value;return(0,E.jsx)(eh,{scope:e.__scopeMenu,checked:u,children:(0,E.jsx)(ea,{role:"menuitemradio","aria-checked":u,...r,ref:n,"data-state":eP(u),onSelect:(0,o.M)(r.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,t)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var ev="MenuItemIndicator",[eh,em]=_(ev,{checked:!1}),eg=r.forwardRef((e,n)=>{let{__scopeMenu:t,forceMount:r,...o}=e,a=em(ev,t);return(0,E.jsx)(g.z,{present:r||eD(a.checked)||!0===a.checked,children:(0,E.jsx)(c.WV.span,{...o,ref:n,"data-state":eP(a.checked)})})});eg.displayName=ev;var ew=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e;return(0,E.jsx)(c.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:n})});ew.displayName="MenuSeparator";var ey=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e,o=L(t);return(0,E.jsx)(h.Eh,{...o,...r,ref:n})});ey.displayName="MenuArrow";var eb="MenuSub",[ex,eM]=_(eb),eE=e=>{let{__scopeMenu:n,children:t,open:o=!1,onOpenChange:a}=e,u=F(eb,n),i=L(n),[c,l]=r.useState(null),[d,s]=r.useState(null),f=(0,b.W)(a);return r.useEffect(()=>(!1===u.open&&f(!1),()=>f(!1)),[u.open,f]),(0,E.jsx)(h.fC,{...i,children:(0,E.jsx)(O,{scope:n,open:o,onOpenChange:f,content:d,onContentChange:s,children:(0,E.jsx)(ex,{scope:n,contentId:(0,v.M)(),triggerId:(0,v.M)(),trigger:c,onTriggerChange:l,children:t})})})};eE.displayName=eb;var eC="MenuSubTrigger",eR=r.forwardRef((e,n)=>{let t=F(eC,e.__scopeMenu),u=K(eC,e.__scopeMenu),i=eM(eC,e.__scopeMenu),c=H(eC,e.__scopeMenu),l=r.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:s}=c,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),s(null)}},[d,s]),(0,E.jsx)(V,{asChild:!0,...f,children:(0,E.jsx)(eu,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":i.contentId,"data-state":ej(t.open),...e,ref:(0,a.F)(n,i.onTriggerChange),onClick:n=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,eT(n=>{c.onItemEnter(n),n.defaultPrevented||e.disabled||t.open||l.current||(c.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{t.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,eT(e=>{var n,r;p();let o=null===(n=t.content)||void 0===n?void 0:n.getBoundingClientRect();if(o){let n=null===(r=t.content)||void 0===r?void 0:r.dataset.side,a="right"===n,u=o[a?"left":"right"],i=o[a?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,n=>{let r=""!==c.searchRef.current;if(!e.disabled&&(!r||" "!==n.key)&&S[u.dir].includes(n.key)){var o;t.onOpenChange(!0),null===(o=t.content)||void 0===o||o.focus(),n.preventDefault()}})})})});eR.displayName=eC;var ek="MenuSubContent",eS=r.forwardRef((e,n)=>{let t=U(G,e.__scopeMenu),{forceMount:u=t.forceMount,...i}=e,c=F(G,e.__scopeMenu),l=K(G,e.__scopeMenu),d=eM(ek,e.__scopeMenu),s=r.useRef(null),f=(0,a.e)(n,s);return(0,E.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,E.jsx)(g.z,{present:u||c.open,children:(0,E.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,E.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:f,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;l.isUsingKeyboardRef.current&&(null===(n=s.current)||void 0===n||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==d.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),t=j[l.dir].includes(e.key);if(n&&t){var r;c.onOpenChange(!1),null===(r=d.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function ej(e){return e?"open":"closed"}function eD(e){return"indeterminate"===e}function eP(e){return eD(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return n=>"mouse"===n.pointerType?e(n):void 0}eS.displayName=ek;var eN="DropdownMenu",[e_,eI]=(0,u.b)(eN,[I]),eL=I(),[eA,eO]=e_(eN),eF=e=>{let{__scopeDropdownMenu:n,children:t,dir:o,open:a,defaultOpen:u,onOpenChange:c,modal:l=!0}=e,d=eL(n),s=r.useRef(null),[f,p]=(0,i.T)({prop:a,defaultProp:null!=u&&u,onChange:c,caller:eN});return(0,E.jsx)(eA,{scope:n,triggerId:(0,v.M)(),triggerRef:s,contentId:(0,v.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:l,children:(0,E.jsx)(B,{...d,open:f,onOpenChange:p,dir:o,modal:l,children:t})})};eF.displayName=eN;var eW="DropdownMenuTrigger",eK=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,disabled:r=!1,...u}=e,i=eO(eW,t),l=eL(t);return(0,E.jsx)(V,{asChild:!0,...l,children:(0,E.jsx)(c.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...u,ref:(0,a.F)(n,i.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eW;var eB=e=>{let{__scopeDropdownMenu:n,...t}=e,r=eL(n);return(0,E.jsx)(Y,{...r,...t})};eB.displayName="DropdownMenuPortal";var eV="DropdownMenuContent",eZ=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...a}=e,u=eO(eV,t),i=eL(t),c=r.useRef(!1);return(0,E.jsx)(q,{id:u.contentId,"aria-labelledby":u.triggerId,...i,...a,ref:n,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;c.current||null===(n=u.triggerRef.current)||void 0===n||n.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey,r=2===n.button||t;(!u.modal||r)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eZ.displayName=eV;var eX=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(en,{...o,...r,ref:n})});eX.displayName="DropdownMenuGroup";var eU=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(et,{...o,...r,ref:n})});eU.displayName="DropdownMenuLabel";var eY=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(ea,{...o,...r,ref:n})});eY.displayName="DropdownMenuItem";var eG=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(ei,{...o,...r,ref:n})});eG.displayName="DropdownMenuCheckboxItem";var ez=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(es,{...o,...r,ref:n})});ez.displayName="DropdownMenuRadioGroup";var eH=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(ep,{...o,...r,ref:n})});eH.displayName="DropdownMenuRadioItem";var eq=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(eg,{...o,...r,ref:n})});eq.displayName="DropdownMenuItemIndicator";var eJ=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(ew,{...o,...r,ref:n})});eJ.displayName="DropdownMenuSeparator",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(ey,{...o,...r,ref:n})}).displayName="DropdownMenuArrow";var eQ=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(eR,{...o,...r,ref:n})});eQ.displayName="DropdownMenuSubTrigger";var e$=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eL(t);return(0,E.jsx)(eS,{...o,...r,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eF,e1=eK,e2=eB,e5=eZ,e8=eX,e3=eU,e7=eY,e6=eG,e9=ez,e4=eH,ne=eq,nn=eJ,nt=e=>{let{__scopeDropdownMenu:n,children:t,open:r,onOpenChange:o,defaultOpen:a}=e,u=eL(n),[c,l]=(0,i.T)({prop:r,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,E.jsx)(eE,{...u,open:c,onOpenChange:l,children:t})},nr=eQ,no=e$},20589:function(e,n,t){t.d(n,{EW:function(){return a}});var r=t(2265),o=0;function a(){r.useEffect(()=>{var e,n;let t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=t[0])&&void 0!==e?e:u()),document.body.insertAdjacentElement("beforeend",null!==(n=t[1])&&void 0!==n?n:u()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function u(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},80467:function(e,n,t){let r;t.d(n,{M:function(){return f}});var o=t(2265),a=t(1584),u=t(25171),i=t(75137),c=t(57437),l="focusScope.autoFocusOnMount",d="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=o.forwardRef((e,n)=>{let{loop:t=!1,trapped:r=!1,onMountAutoFocus:f,onUnmountAutoFocus:g,...w}=e,[y,b]=o.useState(null),x=(0,i.W)(f),M=(0,i.W)(g),E=o.useRef(null),C=(0,a.e)(n,e=>b(e)),R=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(R.paused||!y)return;let n=e.target;y.contains(n)?E.current=n:h(E.current,{select:!0})},n=function(e){if(R.paused||!y)return;let n=e.relatedTarget;null===n||y.contains(n)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",n);let t=new MutationObserver(function(e){if(document.activeElement===document.body)for(let n of e)n.removedNodes.length>0&&h(y)});return y&&t.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",n),t.disconnect()}}},[r,y,R.paused]),o.useEffect(()=>{if(y){m.add(R);let e=document.activeElement;if(!y.contains(e)){let n=new CustomEvent(l,s);y.addEventListener(l,x),y.dispatchEvent(n),n.defaultPrevented||(function(e){let{select:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=document.activeElement;for(let r of e)if(h(r,{select:n}),document.activeElement!==t)return}(p(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(y))}return()=>{y.removeEventListener(l,x),setTimeout(()=>{let n=new CustomEvent(d,s);y.addEventListener(d,M),y.dispatchEvent(n),n.defaultPrevented||h(null!=e?e:document.body,{select:!0}),y.removeEventListener(d,M),m.remove(R)},0)}}},[y,x,M,R]);let k=o.useCallback(e=>{if(!t&&!r||R.paused)return;let n="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(n&&o){let n=e.currentTarget,[r,a]=function(e){let n=p(e);return[v(n,e),v(n.reverse(),e)]}(n);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),t&&h(a,{select:!0})):(e.preventDefault(),t&&h(r,{select:!0})):o===n&&e.preventDefault()}},[t,r,R.paused]);return(0,c.jsx)(u.WV.div,{tabIndex:-1,...w,ref:C,onKeyDown:k})});function p(e){let n=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)n.push(t.currentNode);return n}function v(e,n){for(let t of e)if(!function(e,n){let{upTo:t}=n;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(t,{upTo:n}))return t}function h(e){let{select:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var t;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(t=e)instanceof HTMLInputElement&&"select"in t&&n&&e.select()}}f.displayName="FocusScope";var m=(r=[],{add(e){let n=r[0];e!==n&&(null==n||n.pause()),(r=g(r,e)).unshift(e)},remove(e){var n;null===(n=(r=g(r,e))[0])||void 0===n||n.resume()}});function g(e,n){let t=[...e],r=t.indexOf(n);return -1!==r&&t.splice(r,1),t}}}]);