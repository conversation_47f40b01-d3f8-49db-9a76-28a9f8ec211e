"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2518,4666],{84666:function(e,t,n){n.r(t),n.d(t,{CSRFProvider:function(){return c},useCSRF:function(){return o},useFetchWithCSRF:function(){return i}});var s=n(57437),r=n(2265);let a=(0,r.createContext)({csrfToken:null,isLoading:!0,refreshToken:async()=>null}),o=()=>(0,r.useContext)(a);function c(e){let{children:t}=e,[n,o]=(0,r.useState)(null),[c,i]=(0,r.useState)(!0),l=async()=>{try{i(!0);let e=0;for(;e<3;)try{e++;let t=await fetch("/api/csrf",{method:"GET",credentials:"include"});if(t.ok){let e=await t.json();return o(e.csrfToken),i(!1),e.csrfToken}if(console.warn("Tentativa ".concat(e,"/").concat(3," falhou ao obter token CSRF: ").concat(t.status)),e<3){await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e-1)));continue}throw Error("Falha ao obter token CSRF: ".concat(t.status))}catch(t){if(e>=3)throw t;await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e-1)))}throw Error("M\xe1ximo de tentativas excedido")}catch(e){return console.error("Erro ao obter token CSRF:",e),i(!1),null}};return(0,r.useEffect)(()=>{l()},[]),(0,s.jsx)(a.Provider,{value:{csrfToken:n,isLoading:c,refreshToken:l},children:t})}function i(){let{csrfToken:e,isLoading:t,refreshToken:n}=o();return{fetchWithCSRF:async function(s){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t&&await new Promise(e=>setTimeout(e,100));let a=new Headers(r.headers||{});if(e)a.set("x-csrf-token",e);else{let e=await n();e&&a.set("x-csrf-token",e)}return fetch(s,{...r,credentials:"include",headers:a})},csrfToken:e,isLoading:t}}},2183:function(e,t,n){n.d(t,{O:function(){return a}});var s=n(57437),r=n(49354);function a(e){let{className:t,...n}=e;return(0,s.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",t),...n})}},82518:function(e,t,n){n.d(t,{WorkbookTemplatesServer:function(){return f}});var s=n(57437),r=n(36356),a=n(22023),o=n(34567),c=n(92222),i=n(58215),l=n(39127);n(2265),n(27776),n(84666),n(79055),n(89733);var u=n(48185),d=n(2183);function f(){return(0,s.jsxs)("div",{className:"space-y-6",id:"templates-section",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("h2",{className:"text-xl font-semibold",children:"Templates Prontos"})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Array(6).fill(0).map((e,t)=>(0,s.jsxs)(u.Zb,{className:"overflow-hidden",children:[(0,s.jsx)(u.Ol,{className:"pb-2",children:(0,s.jsx)(d.O,{className:"h-4 w-3/4"})}),(0,s.jsxs)(u.aY,{children:[(0,s.jsx)(d.O,{className:"h-3 w-full mb-2"}),(0,s.jsx)(d.O,{className:"h-3 w-2/3"})]}),(0,s.jsx)(u.eW,{children:(0,s.jsx)(d.O,{className:"h-8 w-full"})})]},t))})]})}r.Z,a.Z,o.Z,c.Z,i.Z,l.Z}}]);