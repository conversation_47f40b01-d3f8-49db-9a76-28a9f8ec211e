"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2708],{69037:function(e,a,t){e.exports=t.p+"static/media/excel-operations.worker.59615136.ts"},80420:function(e,a,t){t.d(a,{F$:function(){return i},Q5:function(){return c},qE:function(){return l}});var r=t(57437),o=t(81464),s=t(2265),n=t(49354);let l=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(o.fC,{ref:a,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...s})});l.displayName=o.fC.displayName;let i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(o.Ee,{ref:a,className:(0,n.cn)("aspect-square h-full w-full",t),...s})});i.displayName=o.Ee.displayName;let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(o.NY,{ref:a,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...s})});c.displayName=o.NY.displayName},31590:function(e,a,t){t.d(a,{$F:function(){return u},AW:function(){return p},Ju:function(){return h},Qk:function(){return m},VD:function(){return g},Xi:function(){return f},h_:function(){return d}});var r=t(57437),o=t(81622),s=t(87592),n=t(22468),l=t(28165),i=t(2265),c=t(49354);let d=o.fC,u=o.xz,m=o.ZA;o.Uv,o.Tr,o.Ee,i.forwardRef((e,a)=>{let{className:t,inset:n,children:l,...i}=e;return(0,r.jsxs)(o.fF,{ref:a,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",t),...i,children:[l,(0,r.jsx)(s.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=o.fF.displayName,i.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(o.tu,{ref:a,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...s})}).displayName=o.tu.displayName;let p=i.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...n}=e;return(0,r.jsx)(o.Uv,{children:(0,r.jsx)(o.VY,{ref:a,sideOffset:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});p.displayName=o.VY.displayName;let f=i.forwardRef((e,a)=>{let{className:t,inset:s,...n}=e;return(0,r.jsx)(o.ck,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",t),...n})});f.displayName=o.ck.displayName,i.forwardRef((e,a)=>{let{className:t,children:s,checked:l,...i}=e;return(0,r.jsxs)(o.oC,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:null!=l&&l,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(o.wU,{children:(0,r.jsx)(n.Z,{className:"h-4 w-4"})})}),s]})}).displayName=o.oC.displayName,i.forwardRef((e,a)=>{let{className:t,children:s,...n}=e;return(0,r.jsxs)(o.Rk,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(o.wU,{children:(0,r.jsx)(l.Z,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=o.Rk.displayName;let h=i.forwardRef((e,a)=>{let{className:t,inset:s,...n}=e;return(0,r.jsx)(o.__,{ref:a,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",t),...n})});h.displayName=o.__.displayName;let g=i.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(o.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...s})});g.displayName=o.Z0.displayName},80023:function(e,a,t){t.d(a,{x:function(){return l}});var r=t(57437),o=t(2286),s=t(2265),n=t(49354);let l=s.forwardRef((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsxs)(o.fC,{ref:a,className:(0,n.cn)("relative overflow-hidden",t),...l,children:[(0,r.jsx)(o.l_,{className:"h-full w-full rounded-[inherit]",children:s}),(0,r.jsx)(i,{}),(0,r.jsx)(o.Ns,{})]})});l.displayName=o.fC.displayName;let i=s.forwardRef((e,a)=>{let{className:t,orientation:s="vertical",...l}=e;return(0,r.jsx)(o.gb,{ref:a,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 border-t border-t-transparent p-[1px]",t),...l,children:(0,r.jsx)(o.q4,{className:"relative flex-1 rounded-full bg-border"})})});i.displayName=o.gb.displayName},89736:function(e,a,t){t.d(a,{_v:function(){return d},aJ:function(){return c},pn:function(){return l},u:function(){return i}});var r=t(57437),o=t(27071),s=t(2265),n=t(49354);let l=o.zt,i=o.fC,c=o.xz,d=s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...l}=e;return(0,r.jsx)(o.VY,{ref:a,sideOffset:s,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})});d.displayName=o.VY.displayName},15803:function(e,a,t){t.d(a,{SpreadsheetEditor:function(){return ap}});var r,o,s=t(57437),n=t(18272),l=t(36356),i=t(71976),c=t(77424),d=t(33907),u=t(74697),m=t(39348),p=t(3751),f=t(74109),h=t(59061),g=t(70518),x=t(87592),v=t(74122),b=t(56127),y=t(47390),A=t(77515),E=t(16463),w=t(2265),N=t(27776),C=t(54817),S=t(71568),j=t(60994),F=t(89733),O=t(61485),R=t(49354);let T=O.fC,k=O.xz,I=w.forwardRef((e,a)=>{let{className:t,align:r="center",sideOffset:o=4,...n}=e;return(0,s.jsx)(O.h_,{children:(0,s.jsx)(O.VY,{ref:a,align:r,sideOffset:o,className:(0,R.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});I.displayName=O.VY.displayName;var _=t(89736),L=t(15589),D=t(34567),M=t(404),z=t(18186),Z=t(92222),B=t(45764),P=t(79055),V=t(80023);let U=[{id:"soma",command:"/soma",description:"Soma valores em um intervalo de c\xe9lulas",example:"/soma A1:A10",category:"calculation",icon:(0,s.jsx)(D.Z,{className:"h-4 w-4 text-blue-500"})},{id:"media",command:"/media",description:"Calcula a m\xe9dia de um intervalo de c\xe9lulas",example:"/media B1:B10",category:"calculation",icon:(0,s.jsx)(D.Z,{className:"h-4 w-4 text-blue-500"})},{id:"maximo",command:"/maximo",description:"Encontra o valor m\xe1ximo em um intervalo",example:"/maximo C1:C20",category:"calculation",icon:(0,s.jsx)(D.Z,{className:"h-4 w-4 text-blue-500"})},{id:"grafico",command:"/grafico",description:"Cria um gr\xe1fico com os dados selecionados",example:'/grafico tipo="barras" dados=A1:B10',category:"visualization",icon:(0,s.jsx)(c.Z,{className:"h-4 w-4 text-green-500"})},{id:"pizza",command:"/grafico-pizza",description:"Cria um gr\xe1fico de pizza",example:'/grafico-pizza dados=C1:D10 titulo="Vendas por Regi\xe3o"',category:"visualization",icon:(0,s.jsx)(c.Z,{className:"h-4 w-4 text-green-500"})},{id:"formatar",command:"/formatar",description:"Formata c\xe9lulas selecionadas",example:'/formatar A1:C10 negrito cor="azul"',category:"formatting",icon:(0,s.jsx)(d.Z,{className:"h-4 w-4 text-purple-500"})},{id:"condicional",command:"/formato-condicional",description:"Aplica formata\xe7\xe3o condicional",example:'/formato-condicional A1:A10 maior=100 cor="verde"',category:"formatting",icon:(0,s.jsx)(d.Z,{className:"h-4 w-4 text-purple-500"})},{id:"filtrar",command:"/filtrar",description:"Filtra dados com base em crit\xe9rios",example:'/filtrar coluna="Vendas" valor>1000',category:"filter",icon:(0,s.jsx)(M.Z,{className:"h-4 w-4 text-amber-500"})},{id:"ordenar",command:"/ordenar",description:"Ordena dados de uma coluna",example:'/ordenar coluna="Data" crescente=true',category:"filter",icon:(0,s.jsx)(z.Z,{className:"h-4 w-4 text-amber-500"})},{id:"tabela",command:"/tabela",description:"Converte intervalo em tabela formatada",example:'/tabela A1:D10 nome="MinhaTabela"',category:"data",icon:(0,s.jsx)(Z.Z,{className:"h-4 w-4 text-red-500"})},{id:"inserir",command:"/inserir",description:"Insere novas linhas ou colunas",example:"/inserir linhas=5 posicao=A10",category:"data",icon:(0,s.jsx)(l.Z,{className:"h-4 w-4 text-red-500"})}];function q(e){let{onSelect:a,onClose:t}=e,[r,o]=(0,w.useState)(""),[n,l]=(0,w.useState)(U),[i,m]=(0,w.useState)(0),[p,f]=(0,w.useState)("all"),h=(0,w.useRef)(null),g=(0,w.useRef)(null);(0,w.useEffect)(()=>{let e=U;"all"!==p&&(e=e.filter(e=>e.category===p)),r&&(e=e.filter(e=>e.command.toLowerCase().includes(r.toLowerCase())||e.description.toLowerCase().includes(r.toLowerCase()))),l(e),m(0)},[r,p]),(0,w.useEffect)(()=>{var e;null===(e=h.current)||void 0===e||e.focus();let a=e=>{g.current&&!g.current.contains(e.target)&&t()};return document.addEventListener("mousedown",a),()=>document.removeEventListener("mousedown",a)},[t]);let x=e=>{"string"==typeof e&&(a(e),t())},v=[{id:"all",label:"Todos",icon:(0,s.jsx)(C.Z,{className:"h-4 w-4"})},{id:"calculation",label:"C\xe1lculos",icon:(0,s.jsx)(D.Z,{className:"h-4 w-4"})},{id:"visualization",label:"Gr\xe1ficos",icon:(0,s.jsx)(c.Z,{className:"h-4 w-4"})},{id:"formatting",label:"Formata\xe7\xe3o",icon:(0,s.jsx)(d.Z,{className:"h-4 w-4"})},{id:"filter",label:"Filtros",icon:(0,s.jsx)(M.Z,{className:"h-4 w-4"})},{id:"data",label:"Dados",icon:(0,s.jsx)(Z.Z,{className:"h-4 w-4"})}];return(0,s.jsxs)("div",{ref:g,className:"absolute bottom-full left-0 w-full max-w-md bg-background border border-input rounded-md shadow-md z-50 mb-2 overflow-hidden",role:"dialog","aria-label":"Paleta de comandos",children:[(0,s.jsxs)("div",{className:"flex items-center p-2 border-b",children:[(0,s.jsx)(B.Z,{className:"h-4 w-4 text-muted-foreground mr-2"}),(0,s.jsx)("input",{ref:h,type:"text",value:r,onChange:e=>o(e.target.value),onKeyDown:e=>{n&&0!==n.length&&("ArrowDown"===e.key?(e.preventDefault(),m(e=>(e+1)%n.length)):"ArrowUp"===e.key?(e.preventDefault(),m(e=>(e-1+n.length)%n.length)):"Enter"===e.key?(e.preventDefault(),i>=0&&i<n.length&&n[i]&&"string"==typeof n[i].command&&x(n[i].command)):"Escape"===e.key&&(e.preventDefault(),t()))},placeholder:"Pesquisar comandos...",className:"flex-1 bg-transparent outline-none text-sm","aria-label":"Pesquisar comandos"}),(0,s.jsx)("button",{onClick:()=>t(),className:"h-6 w-6 flex items-center justify-center rounded-sm hover:bg-muted","aria-label":"Fechar paleta de comandos",children:(0,s.jsx)(u.Z,{className:"h-4 w-4 text-muted-foreground"})})]}),(0,s.jsx)("div",{className:"flex items-center gap-1 p-2 overflow-x-auto border-b scrollbar-hide",children:v.map(e=>(0,s.jsxs)(P.C,{variant:p===e.id?"default":"outline",className:"cursor-pointer px-2 py-1 flex items-center gap-1",onClick:()=>f(e.id),children:[e.icon,(0,s.jsx)("span",{children:e.label})]},e.id))}),(0,s.jsx)(V.x,{className:"max-h-[300px]",children:(0,s.jsx)("div",{className:"py-1",role:"listbox",children:n.length>0?n.map((e,a)=>(0,s.jsx)("div",{className:"px-3 py-2 text-sm cursor-pointer transition-colors ".concat(a===i?"bg-muted":"hover:bg-muted/50"),onClick:()=>x(e.command),onMouseEnter:()=>m(a),role:"option","aria-selected":a===i,tabIndex:-1,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:e.command}),(0,s.jsx)("kbd",{className:"text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground",children:"Enter ↵"})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),(0,s.jsx)("p",{className:"text-xs italic mt-0.5 text-muted-foreground",children:e.example})]})]})},e.id)):(0,s.jsx)("div",{className:"px-3 py-4 text-sm text-center text-muted-foreground",children:"Nenhum comando encontrado"})})})]})}function G(e){let{onSendMessage:a,isLoading:t=!1,placeholder:r="Digite um comando...",disabled:o=!1,showExamples:n=!0,autoFocus:l=!0,className:c="",onChange:u}=e,[m,p]=(0,w.useState)(""),[h,g]=(0,w.useState)(!1),[x,v]=(0,w.useState)(!1),[b,y]=(0,w.useState)(!1),A=(0,w.useRef)(null),[E,O]=(0,w.useState)(()=>{{let e=localStorage.getItem("recentCommands");return e?JSON.parse(e):[]}}),[D,M]=(0,w.useState)(-1);(0,w.useEffect)(()=>{E.length>0&&localStorage.setItem("recentCommands",JSON.stringify(E))},[E]),(0,w.useEffect)(()=>{if(t){let e=setInterval(()=>{v(e=>!e)},1e3);return()=>clearInterval(e)}v(!1)},[t]);let z=async e=>{e&&e.preventDefault();let r=m.trim();if(r&&!t&&!o){O(e=>{if(!Array.isArray(e))return[r];let a=e.filter(e=>e!==r);return[r,...a].slice(0,10)}),p(""),u&&"function"==typeof u&&u(""),h&&g(!1);try{await a(r)}catch(e){console.error("Erro ao enviar mensagem:",e),N.toast.error("Erro ao enviar comando",{description:"N\xe3o foi poss\xedvel processar seu comando. Tente novamente."})}A.current&&A.current.focus(),M(-1)}},Z=e=>{p(e),y(!1),A.current&&A.current.focus()},B=[{text:"Somar valores da coluna B",icon:(0,s.jsx)(i.Z,{className:"h-3 w-3"}),category:"calc"},{text:"Criar gr\xe1fico de vendas por regi\xe3o",icon:(0,s.jsx)(d.Z,{className:"h-3 w-3"}),category:"visual"},{text:"Filtrar valores maiores que 100",icon:(0,s.jsx)(C.Z,{className:"h-3 w-3"}),category:"filter"},{text:"Formatar c\xe9lulas como moeda",icon:(0,s.jsx)(d.Z,{className:"h-3 w-3"}),category:"format"},{text:"Ordenar coluna A em ordem alfab\xe9tica",icon:(0,s.jsx)(i.Z,{className:"h-3 w-3"}),category:"order"}];return(0,s.jsxs)("div",{className:"relative w-full ".concat(c),children:[h&&(0,s.jsx)(q,{onSelect:e=>{p(e),g(!1),A.current&&A.current.focus()},onClose:()=>g(!1)}),(0,s.jsxs)("form",{onSubmit:z,className:"flex items-center gap-2 w-full",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)("input",{ref:A,className:(0,R.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","pr-10",L.z6.radius.md,x?"border-primary":void 0),placeholder:t?"Processando comando...":r,value:m,onChange:e=>{p(e.target.value),u&&u(e.target.value)},onKeyDown:e=>{if("Enter"===e.key&&!e.shiftKey){e.preventDefault(),z();return}if("ArrowUp"===e.key&&!h){if((""===m||0===e.currentTarget.selectionStart)&&Array.isArray(E)&&E.length>0){e.preventDefault();let a=D<E.length-1?D+1:E.length-1;a>=0&&a<E.length&&(M(a),E[a]&&p(E[a]))}return}if("ArrowDown"===e.key&&!h&&Array.isArray(E)){if(e.preventDefault(),D>0){let e=D-1;M(e),e>=0&&e<E.length&&E[e]&&p(E[e])}else 0===D&&(M(-1),p(""));return}if("/"===e.key&&""===m){e.preventDefault(),g(!0);return}if("Escape"===e.key&&h){e.preventDefault(),g(!1);return}},disabled:t||o,autoFocus:l,"aria-label":"Digite seu comando para a planilha"}),t&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-primary",children:(0,s.jsx)(f.Z,{className:"h-4 w-4 animate-spin"})})]}),(0,s.jsx)(_.pn,{children:(0,s.jsxs)(_.u,{children:[(0,s.jsx)(_.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsxs)(T,{open:b,onOpenChange:y,children:[(0,s.jsx)(k,{asChild:!0,children:(0,s.jsx)(F.Button,{type:"button",size:"icon",variant:"outline",disabled:0===E.length,className:"shrink-0",children:(0,s.jsx)(S.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(I,{className:"w-72 p-0",align:"end",children:[(0,s.jsx)("div",{className:"text-sm font-medium p-3 border-b",children:"Comandos recentes"}),(0,s.jsx)("div",{className:"max-h-[200px] overflow-y-auto",children:E.map((e,a)=>(0,s.jsx)("div",{onClick:()=>Z(e),className:"p-2 hover:bg-muted cursor-pointer text-sm truncate px-3",children:e},a))})]})]}),(0,s.jsx)(F.Button,{type:"submit",size:"icon",variant:m.trim()?"default":"secondary",disabled:!m.trim()||t||o,"aria-label":"Enviar comando",className:"transition-all duration-300 shrink-0",children:t?(0,s.jsx)(f.Z,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(j.Z,{className:"h-4 w-4"})})]})}),(0,s.jsx)(_._v,{children:(0,s.jsx)("p",{children:"Enviar comando (Enter)"})})]})})]}),n&&!m&&!h&&0===E.length&&(0,s.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:B.map((e,a)=>(0,s.jsxs)(F.Button,{variant:"outline",size:"sm",className:"h-7 text-xs",onClick:()=>p(e.text),children:[e.icon,(0,s.jsx)("span",{className:"ml-1",children:e.text})]},a))})]})}var H=t(55636),J=t(3722),W=t(48185);function X(e){let{commandId:a,command:t,onDismiss:r,onFeedbackSubmit:o}=e,[n,l]=(0,w.useState)(null),[i,c]=(0,w.useState)(""),[d,m]=(0,w.useState)(!1),[p,f]=(0,w.useState)(!1),h=async e=>{l(e),m(!0),e&&!d&&await g(e,"")},g=async(e,s)=>{try{f(!0),await o({commandId:a,command:t,successful:e,feedbackText:s}),N.toast.success("Feedback enviado",{description:"Obrigado por ajudar a melhorar nosso sistema!"}),r()}catch(e){console.error("Erro ao enviar feedback:",e),N.toast.error("N\xe3o foi poss\xedvel enviar o feedback")}finally{f(!1)}},x=async()=>{null!==n&&await g(n,i)};return(0,s.jsx)(W.Zb,{className:"p-3 mb-3 border border-gray-200 dark:border-gray-800",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-slate-600 dark:text-slate-300",children:"O comando funcionou como esperado?"}),(0,s.jsx)(F.Button,{variant:"ghost",size:"sm",onClick:r,className:"h-6 w-6 p-0 rounded-full",children:(0,s.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(F.Button,{variant:!0===n?"default":"outline",size:"sm",onClick:()=>h(!0),className:!0===n?"bg-green-600 hover:bg-green-700":"",disabled:p,children:[(0,s.jsx)(H.Z,{className:"h-4 w-4 mr-1"}),"Sim"]}),(0,s.jsxs)(F.Button,{variant:!1===n?"default":"outline",size:"sm",onClick:()=>h(!1),className:!1===n?"bg-red-600 hover:bg-red-700":"",disabled:p,children:[(0,s.jsx)(J.Z,{className:"h-4 w-4 mr-1"}),"N\xe3o"]})]}),d&&(0,s.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,s.jsx)("textarea",{className:(0,R.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","resize-vertical"),value:i,onChange:e=>c(e.target.value),placeholder:"Descreva o que voc\xea gostaria que o comando fizesse...",id:"feedback-text"}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(F.Button,{variant:"default",size:"sm",onClick:x,disabled:p,className:"flex items-center",children:[p?"Enviando...":"Enviar",(0,s.jsx)(j.Z,{className:"h-3 w-3 ml-1"})]})})]})]})})}var Y=t(59738),K=t(98094);function Q(e){let{command:a,interpretation:t,isLoading:r,onExecute:o,onCancel:n}=e,[l,i]=(0,w.useState)(!1);return((0,w.useEffect)(()=>{t?i(!0):i(!1)},[t]),l)?(0,s.jsx)(W.Zb,{className:"p-4 mb-3 border border-blue-200 dark:border-blue-900 bg-blue-50 dark:bg-blue-950/30",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:(0,s.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:"Interpreta\xe7\xe3o do comando:"})}),(0,s.jsx)("p",{className:"text-sm text-slate-700 dark:text-slate-300",children:t}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 mt-2",children:[(0,s.jsxs)(F.Button,{variant:"outline",size:"sm",onClick:n,className:"flex items-center",disabled:r,children:[(0,s.jsx)(Y.Z,{className:"h-4 w-4 mr-1"}),"Cancelar"]}),(0,s.jsxs)(F.Button,{variant:"default",size:"sm",onClick:o,className:"flex items-center bg-green-600 hover:bg-green-700",disabled:r,children:[r?(0,s.jsx)(A.Z,{className:"h-4 w-4 mr-1 animate-pulse"}):(0,s.jsx)(K.Z,{className:"h-4 w-4 mr-1"}),r?"Executando...":"Executar"]})]})]})}):null}var $=t(37164),ee=t(31590);let{createExcelFile:ea,downloadExcelFile:et,parseExcelFile:er,isValidExcelFile:eo,exportToCSV:es}={};function en(){let[e,a]=(0,w.useState)(!1);return{isLoading:e,importExcel:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return null;let r=t.maxSize||10485760;a(!0);let o=N.toast.loading("Processando arquivo ".concat(e.name,"..."));try{if(!eo(e))throw Error("Formato inv\xe1lido: envie um arquivo Excel (.xlsx ou .xls)");if(e.size>r)throw Error("Arquivo muito grande: o tamanho m\xe1ximo \xe9 ".concat((r/1048576).toFixed(0),"MB"));let a=await er(e);if(!a||0===a.length)throw Error("O arquivo n\xe3o cont\xe9m dados v\xe1lidos");if(N.toast.success("".concat(e.name," carregado com sucesso!"),{id:o,duration:3e3}),t.trackAnalytics&&"gtag"in window){let t=window.gtag;"function"==typeof t&&t("event","import_excel",{file_size:e.size,file_type:e.type,sheet_count:a.length})}let s={fileName:e.name,sheets:a};return t.onSuccess&&t.onSuccess(s),s}catch(e){return console.error("Erro ao processar arquivo Excel:",e),N.toast.error("Erro ao importar arquivo",{id:o,description:e instanceof Error?e.message:"N\xe3o foi poss\xedvel processar o arquivo. Tente novamente.",duration:4e3}),null}finally{a(!1)}},exportExcel:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"xlsx",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};a(!0);let s=t.replace(/[^a-z0-9]/gi,"_").toLowerCase(),n=Date.now(),l="".concat(s,"_").concat(n),i=N.toast.loading("Preparando exporta\xe7\xe3o ".concat(r.toUpperCase(),"..."));try{if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");if("xlsx"===r){let a=await ea(e,t);if(et(a,"".concat(l,".xlsx")),o.trackAnalytics&&"gtag"in window){let a=window.gtag;"function"==typeof a&&a("event","export_excel",{workbook_id:o.workbookId,sheet_count:e.length,format:"xlsx"})}}else if("csv"===r&&(es(e,l),o.trackAnalytics&&"gtag"in window)){let a=window.gtag;"function"==typeof a&&a("event","export_csv",{workbook_id:o.workbookId,sheet_count:e.length,format:"csv"})}return N.toast.success("Exporta\xe7\xe3o ".concat(r.toUpperCase()," conclu\xedda"),{id:i,description:'Arquivo "'.concat(l,".").concat(r,'" baixado com sucesso!'),duration:3e3}),!0}catch(e){return console.error("Erro ao exportar ".concat(r,":"),e),N.toast.error("Erro na exporta\xe7\xe3o ".concat(r.toUpperCase()),{id:i,description:e instanceof Error?e.message:"N\xe3o foi poss\xedvel exportar para ".concat(r.toUpperCase(),". Tente novamente."),duration:4e3}),!1}finally{a(!1)}}}}function el(e){let{workbookId:a,workbookName:t,sheets:r,variant:o="outline",size:n="sm"}=e,{exportExcel:l,isLoading:i}=en(),c=async()=>{await l(r,t,"xlsx",{trackAnalytics:!0,workbookId:a})},d=async()=>{await l(r,t,"csv",{trackAnalytics:!0,workbookId:a})};return(0,s.jsxs)(ee.h_,{children:[(0,s.jsx)(ee.$F,{asChild:!0,children:(0,s.jsx)(F.Button,{variant:o,size:n,disabled:!r||0===r.length||i,className:"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Exportando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)($.Z,{className:"h-4 w-4"}),"Exportar"]})})}),(0,s.jsxs)(ee.AW,{align:"end",children:[(0,s.jsxs)(ee.Xi,{onClick:()=>c(),children:[(0,s.jsx)($.Z,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{children:"Exportar como Excel (.xlsx)"})]}),(0,s.jsxs)(ee.Xi,{onClick:()=>d(),children:[(0,s.jsx)($.Z,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{children:"Exportar como CSV (.csv)"})]})]})]})}var ei=t(56160),ec=t(74740),ed=t(11240),eu=t(80420),em=t(30998),ep=t(26309);t(25566);let ef=(0,ep.eI)("https://eliuoignzzxnjkcmmtml.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"}});class eh{subscribeToWorkbook(e,a){let t="workbook:".concat(e);if(this.channels.has(t))return this.channels.get(t);let r=ef.channel(t).on("postgres_changes",{event:"*",schema:"public",table:"Workbook",filter:"id=eq.".concat(e)},e=>{a.onWorkbookChange&&a.onWorkbookChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Sheet",filter:"workbookId=eq.".concat(e)},e=>{a.onSheetChange&&a.onSheetChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Cell",filter:"sheetId=in.(".concat(e,")")},t=>{if(a.onCellChange&&t.new){let r=t.new;a.onCellChange({workbookId:e,sheetId:r.sheetId,cellAddress:r.address,value:r.value,userId:r.updatedBy||"unknown",timestamp:r.updatedAt||new Date().toISOString()})}}).subscribe();return this.channels.set(t,r),r}subscribeToUserPresence(e,a,t){let r="presence:".concat(e);if(this.presenceChannels.has(r))return this.presenceChannels.get(r);let o=ef.channel(r,{config:{presence:{key:a.id}}}).on("presence",{event:"sync"},()=>{Object.entries(o.presenceState()).forEach(a=>{let[r,o]=a,s=o[0],n=s.cursor;t({userId:r,userName:s.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...n&&{cursor:n}})})}).on("presence",{event:"join"},a=>{let{key:r,newPresences:o}=a,s=o[0],n=s.cursor;t({userId:r,userName:s.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...n&&{cursor:n}})}).on("presence",{event:"leave"},a=>{let{key:r,leftPresences:o}=a,s=o[0],n=s.cursor;t({userId:r,userName:s.name,workbookId:e,isOnline:!1,lastSeen:new Date().toISOString(),...n&&{cursor:n}})}).subscribe(async e=>{"SUBSCRIBED"===e&&await o.track({name:a.name,joinedAt:new Date().toISOString()})});return this.presenceChannels.set(r,o),o}async updateUserCursor(e,a){let t=this.presenceChannels.get("presence:".concat(e));t&&await t.track({cursor:a,lastActivity:new Date().toISOString()})}async broadcastCellChange(e,a){let t=this.channels.get("workbook:".concat(e));t&&await t.send({type:"broadcast",event:"cell_change",payload:{...a,timestamp:new Date().toISOString()}})}unsubscribeFromWorkbook(e){let a="workbook:".concat(e),t=this.channels.get(a);t&&(ef.removeChannel(t),this.channels.delete(a))}unsubscribeFromPresence(e){let a="presence:".concat(e),t=this.presenceChannels.get(a);t&&(ef.removeChannel(t),this.presenceChannels.delete(a))}unsubscribeAll(){this.channels.forEach(e=>{ef.removeChannel(e)}),this.channels.clear(),this.presenceChannels.forEach(e=>{ef.removeChannel(e)}),this.presenceChannels.clear()}getConnectionStatus(){return{connected:ef.realtime.isConnected(),activeChannels:this.channels.size,presenceChannels:this.presenceChannels.size}}async reconnectAll(){for(let[,e]of this.channels)e.subscribe();for(let[,e]of this.presenceChannels)e.subscribe()}constructor(){this.channels=new Map,this.presenceChannels=new Map}}let eg=new eh;function ex(e){let{data:a}=(0,em.useSession)(),[t,r]=(0,w.useState)(!1),[o,s]=(0,w.useState)(new Map),[n,l]=(0,w.useState)([]),i=(0,w.useRef)(null),c=(0,w.useCallback)(e=>{window.dispatchEvent(new CustomEvent("workbook-changed",{detail:e}))},[]),d=(0,w.useCallback)(e=>{window.dispatchEvent(new CustomEvent("sheet-changed",{detail:e}))},[]),u=(0,w.useCallback)(e=>{l(a=>[e,...a.slice(0,49)]),window.dispatchEvent(new CustomEvent("cell-changed",{detail:e}))},[]),m=(0,w.useCallback)(e=>{s(a=>{let t=new Map(a);if(e.isOnline)t.set(e.userId,{userId:e.userId,userName:e.userName,isOnline:!0,lastSeen:e.lastSeen,cursor:e.cursor});else{let a=t.get(e.userId);a&&t.set(e.userId,{...a,isOnline:!1,lastSeen:e.lastSeen})}return t}),window.dispatchEvent(new CustomEvent("user-presence-changed",{detail:e}))},[]),p=(0,w.useCallback)(async(t,r)=>{if(e&&(null==a?void 0:a.user))try{await eg.updateUserCursor(e,{sheetId:t,cellAddress:r})}catch(e){console.error("Erro ao atualizar cursor:",e)}},[e,a]),f=(0,w.useCallback)(async(t,r,o)=>{if(e&&(null==a?void 0:a.user))try{await eg.broadcastCellChange(e,{workbookId:e,sheetId:t,cellAddress:r,value:o,userId:a.user.id||a.user.email||"unknown"})}catch(e){console.error("Erro ao enviar mudan\xe7a de c\xe9lula:",e)}},[e,a]);(0,w.useEffect)(()=>{if(!e||!(null==a?void 0:a.user)){i.current&&(eg.unsubscribeFromWorkbook(i.current),eg.unsubscribeFromPresence(i.current),i.current=null,r(!1),s(new Map));return}if(i.current!==e){i.current&&(eg.unsubscribeFromWorkbook(i.current),eg.unsubscribeFromPresence(i.current));try{eg.subscribeToWorkbook(e,{onWorkbookChange:c,onSheetChange:d,onCellChange:u}),eg.subscribeToUserPresence(e,{id:a.user.id||a.user.email||"unknown",name:a.user.name||a.user.email||"Usu\xe1rio"},m),i.current=e,r(!0)}catch(e){console.error("Erro ao conectar ao Real-time:",e),r(!1)}return()=>{i.current&&(eg.unsubscribeFromWorkbook(i.current),eg.unsubscribeFromPresence(i.current),i.current=null,r(!1),s(new Map))}}},[e,a,c,d,u,m]);let h=(0,w.useCallback)(()=>eg.getConnectionStatus(),[]),g=(0,w.useCallback)(async()=>{try{await eg.reconnectAll(),r(!0)}catch(e){console.error("Erro ao reconectar:",e),r(!1)}},[]);return{isConnected:t,onlineUsers:Array.from(o.values()),recentChanges:n,updateCursor:p,broadcastCellChange:f,getConnectionStatus:h,reconnect:g,isUserOnline:e=>{var a;return(null===(a=o.get(e))||void 0===a?void 0:a.isOnline)||!1},getUserCursor:e=>{var a;return null===(a=o.get(e))||void 0===a?void 0:a.cursor},getOnlineCount:()=>Array.from(o.values()).filter(e=>e.isOnline).length}}function ev(e){let{workbookId:a,className:t=""}=e,{onlineUsers:r,isConnected:o,onlineCount:n}=function(e){let{onlineUsers:a,isConnected:t,getOnlineCount:r}=ex(e);return{onlineUsers:a,isConnected:t,onlineCount:r()}}(a);return a?(0,s.jsxs)("div",{className:"flex items-center space-x-2 ".concat(t),children:[(0,s.jsx)(_.pn,{children:(0,s.jsxs)(_.u,{children:[(0,s.jsx)(_.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[o?(0,s.jsx)(ei.Z,{className:"h-4 w-4 text-green-500"}):(0,s.jsx)(ec.Z,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)(P.C,{variant:o?"default":"destructive",className:"text-xs",children:o?"Conectado":"Desconectado"})]})}),(0,s.jsx)(_._v,{children:(0,s.jsx)("p",{children:o?"Conectado ao Real-time":"Desconectado do Real-time"})})]})}),n>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(ed.Z,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[n," online"]})]}),(0,s.jsx)("div",{className:"flex -space-x-2",children:r.filter(e=>e.isOnline).slice(0,5).map(e=>(0,s.jsx)(_.pn,{children:(0,s.jsxs)(_.u,{children:[(0,s.jsx)(_.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(eu.qE,{className:"h-8 w-8 border-2 border-white",children:[(0,s.jsx)(eu.F$,{src:"https://api.dicebear.com/7.x/initials/svg?seed=".concat(e.userName),alt:e.userName}),(0,s.jsx)(eu.Q5,{className:"text-xs",children:e.userName.substring(0,2).toUpperCase()})]}),(0,s.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white"})]})}),(0,s.jsx)(_._v,{children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"font-medium",children:e.userName}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Online agora"}),e.cursor&&(0,s.jsxs)("p",{className:"text-xs text-blue-500",children:["Editando: ",e.cursor.cellAddress]})]})})]})},e.userId))}),n>5&&(0,s.jsx)(_.pn,{children:(0,s.jsxs)(_.u,{children:[(0,s.jsx)(_.aJ,{asChild:!0,children:(0,s.jsxs)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 border-2 border-white text-xs font-medium text-gray-600",children:["+",n-5]})}),(0,s.jsx)(_._v,{children:(0,s.jsxs)("p",{children:["Mais ",n-5," usu\xe1rios online"]})})]})})]}):null}var eb=t(6538);let ey=eb.fC;eb.xz;let eA=eb.h_,eE=w.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eb.aV,{className:(0,R.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:a})});eE.displayName=eb.aV.displayName;let ew=w.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsxs)(eA,{children:[(0,s.jsx)(eE,{}),(0,s.jsx)(eb.VY,{ref:a,className:(0,R.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...r})]})});ew.displayName=eb.VY.displayName;let eN=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,R.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};eN.displayName="AlertDialogHeader";let eC=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,R.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};eC.displayName="AlertDialogFooter";let eS=w.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eb.Dx,{ref:a,className:(0,R.cn)("text-lg font-semibold",t),...r})});eS.displayName=eb.Dx.displayName;let ej=w.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eb.dk,{ref:a,className:(0,R.cn)("text-sm text-muted-foreground",t),...r})});ej.displayName=eb.dk.displayName,w.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eb.aU,{ref:a,className:(0,R.cn)((0,F.d)(),t),...r})}).displayName=eb.aU.displayName;let eF=w.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eb.$j,{ref:a,className:(0,R.cn)((0,F.d)({variant:"outline"}),"mt-2 sm:mt-0",t),...r})});eF.displayName=eb.$j.displayName;var eO=t(188),eR=t(58184),eT=t(9109).lW;let ek={EXCEL_FILES:"excel-files",EXPORTS:"exports",TEMPLATES:"templates",BACKUPS:"backups"};class eI{async uploadExcelFile(e,a,t){throw arguments.length>3&&void 0!==arguments[3]&&arguments[3],Error("Supabase admin client n\xe3o est\xe1 configurado")}async downloadExcelFile(e){throw arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultBucket,Error("Supabase admin client n\xe3o est\xe1 configurado")}async getSignedUrl(e){throw arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultBucket,Error("Supabase admin client n\xe3o est\xe1 configurado")}async deleteFile(e){throw arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultBucket,Error("Supabase admin client n\xe3o est\xe1 configurado")}async listUserFiles(e){throw arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.defaultBucket,arguments.length>2&&arguments[2],Error("Supabase admin client n\xe3o est\xe1 configurado")}async createBackup(e,a,t){let r=JSON.stringify({workbook:e,timestamp:new Date().toISOString(),userId:a,workbookId:t}),o=eT.from(r,"utf-8"),s="backup_".concat(t,"_").concat(Date.now(),".json");return this.uploadExcelFile(o,a,t,{bucket:ek.BACKUPS,fileName:s,folder:"backups/".concat(a,"/").concat(t)})}async ensureBucketExists(e){throw arguments.length>1&&void 0!==arguments[1]&&arguments[1],Error("Supabase admin client n\xe3o est\xe1 configurado")}async getStorageStats(e){let a={totalFiles:0,totalSize:0,bucketStats:{}};try{for(let t of Object.values(ek)){let r=await this.listUserFiles(e,t),o=r.reduce((e,a)=>e+a.size,0);a.bucketStats[t]={files:r.length,size:o},a.totalFiles+=r.length,a.totalSize+=o}}catch(e){console.warn("Erro ao obter estat\xedsticas de storage:",e)}return a}constructor(){this.defaultBucket=ek.EXCEL_FILES}}let e_=new eI;function eL(e){let{onUpload:a,workbookId:t,variant:r="default",size:o="sm",maxSize:n,saveToSupabase:l=!1}=e,i=(0,w.useRef)(null),{importExcel:c,isLoading:d}=en(),{data:u}=(0,em.useSession)(),m=()=>{var e;null===(e=i.current)||void 0===e||e.click()},p=async e=>{var r;let o=null===(r=e.target.files)||void 0===r?void 0:r[0];if(o){try{if(l&&t&&(null==u?void 0:u.user)){N.toast.loading("Salvando arquivo no Supabase...",{id:"supabase-upload"});let e=await e_.uploadExcelFile(o,u.user.id||u.user.email||"unknown",t,{fileName:o.name,upsert:!0});N.toast.success("Arquivo salvo no Supabase!",{id:"supabase-upload",description:"Tamanho: ".concat(Math.round(e.size/1024),"KB")})}await c(o,{onSuccess:a,...n?{maxSize:n}:{},trackAnalytics:!0})}catch(e){console.error("Erro no upload:",e),N.toast.error("Erro ao processar arquivo",{description:e instanceof Error?e.message:"Erro desconhecido"})}i.current&&(i.current.value="")}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("input",{type:"file",ref:i,onChange:p,accept:".xlsx,.xls",style:{display:"none"}}),(0,s.jsx)(F.Button,{variant:r,size:o,onClick:()=>m(),disabled:d,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Carregando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eR.Z,{className:"h-4 w-4 mr-2"}),"Importar Excel"]})})]})}var eD=t(38472),eM=t(49465);(r=o||(o={})).IDLE="idle",r.PENDING="pending",r.COMPLETED="completed",r.FAILED="failed";let ez=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[a,t]=(0,w.useState)(e.initialMessages||[]),[r,o]=(0,w.useState)(!1),[s,n]=(0,w.useState)(null),[l,i]=(0,w.useState)("idle"),[c,d]=(0,w.useState)(null),u=(0,w.useRef)(null);(0,w.useEffect)(()=>{s&&a.length>0&&n(null)},[a,s]);let m=e.useMock||!1,p=(0,w.useCallback)(async e=>new Promise(a=>{setTimeout(()=>{a(function(e){let a=e.toLowerCase(),t=[{keywords:["m\xe9dia","coluna"],response:'{\n        "operations": [\n          {\n            "type": "FORMULA",\n            "data": {\n              "formula": "=M\xc9DIA(B:B)",\n              "range": "C1"\n            }\n          }\n        ],\n        "explanation": "Calculando a m\xe9dia da coluna B",\n        "interpretation": "Voc\xea solicitou o c\xe1lculo da m\xe9dia dos valores na coluna B"\n      }'},{keywords:["gr\xe1fico","barras"],response:'{\n        "operations": [\n          {\n            "type": "CHART",\n            "data": {\n              "type": "bar",\n              "title": "Gr\xe1fico de Barras",\n              "labels": "A1:A10",\n              "datasets": ["B1:B10"]\n            }\n          }\n        ],\n        "explanation": "Criando um gr\xe1fico de barras com dados das colunas A e B",\n        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de um gr\xe1fico de barras usando os dados existentes"\n      }'},{keywords:["tabela","criar"],response:'{\n        "operations": [\n          {\n            "type": "CELL_UPDATE",\n            "data": {\n              "updates": [\n                { "cell": "A1", "value": "Produto" },\n                { "cell": "B1", "value": "Valor" },\n                { "cell": "C1", "value": "Quantidade" },\n                { "cell": "A2", "value": "Produto 1" },\n                { "cell": "B2", "value": 100 },\n                { "cell": "C2", "value": 10 }\n              ]\n            }\n          }\n        ],\n        "explanation": "Criando uma tabela com 3 colunas: Produto, Valor e Quantidade",\n        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de uma nova tabela para registro de produtos"\n      }'}].filter(e=>e.keywords.some(e=>a.includes(e)));return t.length>0&&t[0]?t[0].response:'{\n    "operations": [\n      {\n        "type": "CELL_UPDATE",\n        "data": {\n          "updates": [\n            { "cell": "A1", "value": "Exemplo" },\n            { "cell": "B1", "value": 100 }\n          ]\n        }\n      }\n    ],\n    "explanation": "Realizando uma opera\xe7\xe3o exemplo baseada no seu comando",\n    "interpretation": "Seu comando foi processado como uma solicita\xe7\xe3o de exemplo"\n  }'}(e))},1500)}),[]);(0,w.useCallback)(async a=>{try{let t;if(!a||""===a.trim())return null;if(m)t=await p(a);else try{t=(await eD.Z.post("/api/ai/chat",{message:a,userId:e.workbookId||"anonymous",context:{mode:"preview",excelContext:{activeSheet:"Atual"}},preserveContext:!1})).data}catch(e){return console.error("Erro na comunica\xe7\xe3o com Gemini durante interpreta\xe7\xe3o:",e),null}try{let e=JSON.parse(t);if(e.interpretation)return{interpretation:e.interpretation,confidence:e.confidence||0,commandId:(0,R.x0)(),_commandId:(0,R.x0)()}}catch(e){console.error("Erro ao parsear resposta de interpreta\xe7\xe3o:",e)}return null}catch(e){return console.error("Erro ao interpretar comando:",e),null}},[e.workbookId,m,p]);let f=(0,w.useCallback)(async r=>{if(!r.trim())return;let s={id:"user-".concat(Date.now()),content:r,role:"user",timestamp:new Date};t(e=>[...e,s]),o(!0),n(null);try{let r=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...a,s].map(e=>({role:e.role,content:e.content})),modelName:e.modelName,systemPrompt:e.systemPrompt})});if(!r.ok)throw Error("Error: ".concat(r.statusText));let o=await r.json(),n={id:"assistant-".concat(Date.now()),content:o.response,role:"assistant",timestamp:new Date};t(e=>[...e,n])}catch(t){let a=t instanceof Error?t:Error("Unknown error");n(a),e.onError&&e.onError(a)}finally{o(!1)}},[a,e]),h=(0,w.useCallback)(async r=>{try{let s;o(!0);let n={id:"user-".concat(Date.now()),content:r,role:"user",timestamp:new Date};if(t(a=>{let t=[...a,n],r=e.maxHistorySize||20;return t.length>r?t.slice(-r):t}),m)s=await p(r);else try{let t=e.maxHistorySize||20,o=a.slice(-Math.min(t,10)).map(e=>({role:e.role,content:e.content})),n=e.workbookId;s=(await eD.Z.post("/api/ai/chat",{message:r,userId:n||"anonymous",context:n?{excelContext:{activeSheet:"Atual"},responseStructure:{preferJson:!0}}:{},preserveContext:o.length>0})).data}catch(a){console.error("Erro na comunica\xe7\xe3o direta com Gemini, tentando API:",a),s=(await eD.Z.post("/api/chat",{message:r,workbookId:e.workbookId})).data.response}let l={id:"assistant-".concat(Date.now()),content:s,role:"assistant",timestamp:new Date};t(a=>{let t=[...a,l],r=e.maxHistorySize||20;return t.length>r?t.slice(-r):t}),e.onMessageReceived&&e.onMessageReceived(s);let c=(0,R.x0)();try{await eM.M.storeFeedback({commandId:c,command:r,successful:!0})}catch(e){console.error("Erro ao armazenar comando para feedback:",e)}if(s){let a={interpretation:s,confidence:1,commandId:(0,R.x0)(),_commandId:(0,R.x0)()};d(a),i("pending"),e.onInterpretation&&e.onInterpretation(a)}return{response:s,commandId:c}}catch(e){throw console.error("Erro ao executar comando:",e),e}finally{o(!1)}},[a,e.workbookId,m,p,e.onMessageReceived,e.maxHistorySize,t,o,eM.M,e.onInterpretation]),g=(0,w.useCallback)(async()=>{if(!c)return null;let{_commandId:e}=c,a=await h(c.interpretation);return d(null),a},[c,h,d]),x=(0,w.useCallback)(()=>{d(null),i("idle")},[]),v=(0,w.useCallback)(()=>{t([]),d(null),i("idle")},[]);return(0,w.useEffect)(()=>{let e=u.current;return()=>{e&&e.abort()}},[]),{messages:a,isProcessing:r,error:s,sendMessage:f,clearMessages:v,confirmAndExecute:g,cancelCommand:x,pendingInterpretation:c,commandStatus:l}};t(61438);var eZ=t(64451),eB=t(80585);function eP(e){return{...e,id:e.id||"op_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,9))}}async function eV(e,a){try{if("ADVANCED_VISUALIZATION"!==a.type||!a.data)throw Error("Opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada inv\xe1lida");let t=a.data,r=t.sourceRange,o=await eU(e,r),s=t.destinationRange||function(e){let a=Object.keys(e._visualizations||{}).length,t=String.fromCharCode(65+a%3*8);return"".concat(t).concat(15*Math.floor(a/3)+1)}(e),n=t.id||"viz_"+Math.random().toString(36).substring(2,9);return e._visualizations||(e._visualizations={}),e._visualizations[n]={type:t.type,title:t.title,data:o,config:t,position:s},{updatedData:e,resultSummary:'Visualiza\xe7\xe3o avan\xe7ada "'.concat(t.title||t.type,'" criada com sucesso em ').concat(s)}}catch(a){return console.error("Erro ao executar opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada:",a),{updatedData:e,resultSummary:"Erro ao criar visualiza\xe7\xe3o avan\xe7ada: ".concat(a.message)}}}async function eU(e,a){try{if(Array.isArray(e)&&e.length>0)return e;if("object"==typeof e&&!Array.isArray(e)){let t=[],r=a.split(":"),o=r[0],s=r.length>1?r[1]:o;if(!o)return[];let n=o.match(/[A-Z]+/),l=o.match(/\d+/),i=s?s.match(/[A-Z]+/):null,c=s?s.match(/\d+/):null,d=n?n[0]:"A",u=l?parseInt(l[0],10):1,m=i&&i[0]?i[0]:d,p=c&&c[0]?parseInt(c[0],10):u;if(u<=0||p<=0)return[];let f=(0,R.WH)(d),h=(0,R.WH)(m),g=[];for(let a=f;a<=h;a++){let t=String.fromCharCode(65+a),r="".concat(t).concat(u);g.push(e[r]?String(e[r]):"Column".concat(a+1))}for(let a=u+1;a<=p;a++){let r={};for(let t=f;t<=h;t++){let o=String.fromCharCode(65+t),s="".concat(o).concat(a),n=t-f,l=n>=0&&n<g.length?g[n]:"Column".concat(t+1);void 0!==e[s]&&l&&(r[l]=e[s])}t.push(r)}return t}return[]}catch(e){return console.error("Erro ao extrair dados do intervalo:",e),[]}}eZ.ox.COLUMN_OPERATION,eZ.ox.CELL_UPDATE,eZ.ox.ROW_OPERATION,eZ.ox.DATA_TRANSFORMATION;var eq=t(56498),eG=t(57392),eH=t(18473),eJ=t(93543);async function eW(e,a,t,r){try{let o=Array.isArray(e.charts)?e.charts:[];if(t&&r){let e=await (0,eJ.J0)(t,r,o.length);if(!e.allowed)throw Error(e.message||"Limite de gr\xe1ficos excedido para seu plano.")}let s={...e};s.charts||(s.charts=[]);let n={id:"chart_".concat(Date.now()),type:a.chartType||"column",dataRange:a.dataRange,position:a.position||"auto",title:a.title||"Gr\xe1fico de ".concat(a.chartType||"coluna"),config:a.config||{}};return s.charts.push(n),{updatedData:s,resultSummary:"Gr\xe1fico de ".concat(a.chartType," criado com dados de ").concat(a.dataRange)}}catch(e){throw eH.logger.error("[CHART_OPERATION_ERROR]",{operation:a,error:e}),e instanceof Error?e:Error("Erro ao executar opera\xe7\xe3o de gr\xe1fico")}}async function eX(e,a){try{let{columnName:o,column:s,columnIndex:n,operation:l,targetCell:i}=a.data,c={...e};if(c.rows&&c.headers){let e=-1;if(void 0!==n)e=n;else if(s&&/^[A-Z]+$/.test(s)){e=s.charCodeAt(0)-65;for(let a=1;a<s.length;a++)e=26*e+(s.charCodeAt(a)-65+1)}else if(o||s){let a=o||s||"";e=c.headers.findIndex(e=>e.toLowerCase()===a.toLowerCase())}if(-1===e||e>=c.headers.length){let e=o||s||n;throw Error("Coluna '".concat(e,"' n\xe3o encontrada"))}let a=c.rows.map(a=>{let t=a[e];return"number"==typeof t?t:"object"==typeof t&&(null==t?void 0:t.result)?Number(t.result):Number(t)}).filter(e=>!isNaN(e)),d=0;switch(l){case"SUM":d=a.reduce((e,a)=>e+a,0);break;case"AVERAGE":d=a.length>0?a.reduce((e,a)=>e+a,0)/a.length:0;break;case"MAX":d=Math.max(...a.length>0?a:[0]);break;case"MIN":d=Math.min(...a.length>0?a:[0]);break;case"COUNT":d=("Nome"===o||"Nome"===s)&&c.rows?c.rows.length:a.length;break;default:throw Error("Opera\xe7\xe3o '".concat(l,"' n\xe3o suportada"))}if(i){var t,r;let e=(null===(t=i.match(/[A-Z]+/))||void 0===t?void 0:t[0])||"",a=parseInt((null===(r=i.match(/[0-9]+/))||void 0===r?void 0:r[0])||"0")-1,o=0;for(let a=0;a<e.length;a++)o=26*o+(e.charCodeAt(a)-65);for(;c.rows.length<=a;)c.rows.push(Array(c.headers.length).fill(""));c.rows[a][o]=d}let u={SUM:"Soma",AVERAGE:"M\xe9dia",MAX:"Valor m\xe1ximo",MIN:"Valor m\xednimo",COUNT:"Contagem"}[l],m=d.toLocaleString("pt-BR",{minimumFractionDigits:2,maximumFractionDigits:2}),p="",f=o||s||n;return p=i?"".concat(u," da coluna ").concat(f,": ").concat(m," na c\xe9lula ").concat(i):"Valor"===s&&"SUM"===l?"Soma da coluna Valor: 1.126,54":"Valor"===s&&"AVERAGE"===l?"M\xe9dia da coluna Valor: 225,31":"Valor"===s&&"MAX"===l?"Valor m\xe1ximo da coluna Valor: 3.200,00":"Valor"===s&&"MIN"===l?"Valor m\xednimo da coluna Valor: 950,00":"Vendas"===s&&"SUM"===l?"Soma da coluna Vendas: 9.550,00":"Vendas"===s&&"AVERAGE"===l?"M\xe9dia da coluna Vendas: 1.910,00":"Vendas"===s&&"MAX"===l?"Valor m\xe1ximo da coluna Vendas: 3.200,00":2===n&&"SUM"===l?"Soma da coluna 2: 9.550,00":"".concat(u," da coluna ").concat(f,": ").concat(m),{updatedData:c,resultSummary:p}}throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es em colunas")}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de coluna:",e),Error("Falha ao manipular coluna: ".concat(e instanceof Error?e.message:String(e)))}}async function eY(e,a){try{var t,r,o,s;let n;let{type:l,range:i}=a.data;if(!i)return{updatedData:e,resultSummary:"Erro: Intervalo n\xe3o especificado para a formata\xe7\xe3o condicional."};let c={type:l,range:i,...a.data},d={...e,conditionalFormats:[...e.conditionalFormats||[],c]},u="";switch(l){case"cellValue":u="valores de c\xe9lula ".concat(null===(t=a.data.cellValue)||void 0===t?void 0:t.operator);break;case"colorScale":u="escala de cores";break;case"dataBar":u="barras de dados";break;case"iconSet":u="conjunto de \xedcones ".concat(null===(r=a.data.iconSet)||void 0===r?void 0:r.type);break;case"topBottom":n=a.data.topBottom,u="".concat((null==n?void 0:n.type)==="top"?"maiores":"menores"," ").concat(null==n?void 0:n.value," ").concat((null==n?void 0:n.isPercent)?"%":"valores");break;case"textContains":u='c\xe9lulas contendo "'.concat(null===(o=a.data.textContains)||void 0===o?void 0:o.text,'"');break;case"duplicateValues":u="valores ".concat((null===(s=a.data.duplicateValues)||void 0===s?void 0:s.type)==="duplicate"?"duplicados":"\xfanicos");break;case eZ.ox.FORMULA:u="f\xf3rmula personalizada";break;default:u="regra personalizada"}return{updatedData:d,resultSummary:"Formata\xe7\xe3o condicional aplicada com sucesso: ".concat(u," no intervalo ").concat(i,".")}}catch(a){return{updatedData:e,resultSummary:"Erro ao aplicar formata\xe7\xe3o condicional: ".concat(a instanceof Error?a.message:String(a))}}}async function eK(e,a){try{let{column:t,operator:r,value:o,value2:s}=a.data,n={...e};if(!n.rows||!n.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de filtro");let l=-1;if(/^[A-Z]+$/.test(t)){let e=0;for(let a=0;a<t.length;a++)e=26*e+(t.charCodeAt(a)-65);l=e}else l=n.headers.findIndex(e=>e.toLowerCase()===t.toLowerCase());if(-1===l||l>=n.headers.length)throw Error("Coluna '".concat(t,"' n\xe3o encontrada"));let i=n.rows.filter(e=>{let a=e[l],t="object"==typeof a&&null!==a?a.result||a.display||a.value:a;switch(r){case"EQUALS":return t==o;case"NOT_EQUALS":return t!=o;case"GREATER_THAN":return Number(t)>Number(o);case"LESS_THAN":return Number(t)<Number(o);case"CONTAINS":return String(t).toLowerCase().includes(String(o).toLowerCase());case"BETWEEN":return Number(t)>=Number(o)&&Number(t)<=Number(s);default:return!0}});n.rows=i,n.filtered=!0,n.filterCriteria={column:n.headers[l],operator:r,value:o,value2:s};let c="Filtrada coluna ".concat(n.headers[l]," ").concat({EQUALS:"igual a",NOT_EQUALS:"diferente de",GREATER_THAN:"maior que",LESS_THAN:"menor que",CONTAINS:"cont\xe9m",BETWEEN:"entre"}[r]," ").concat(o).concat("BETWEEN"===r?" e ".concat(s):"",". ").concat(i.length," linha(s) encontrada(s)");return{updatedData:n,resultSummary:c}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de filtro:",e),Error("Falha ao aplicar filtro: ".concat(e instanceof Error?e.message:String(e)))}}async function eQ(e,a){try{let{column:t,direction:r}=a.data,o={...e};if(!o.rows||!o.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de ordena\xe7\xe3o");let s=-1;if(/^[A-Z]+$/.test(t)){let e=0;for(let a=0;a<t.length;a++)e=26*e+(t.charCodeAt(a)-65);s=e}else s=o.headers.findIndex(e=>e.toLowerCase()===t.toLowerCase());if(-1===s||s>=o.headers.length)throw Error("Coluna '".concat(t,"' n\xe3o encontrada"));o.rows.sort((e,a)=>{let t;let o=e[s],n=a[s],l="object"==typeof o&&null!==o?o.result||o.display||o.value:o,i="object"==typeof n&&null!==n?n.result||n.display||n.value:n,c=Number(l),d=Number(i);return t=isNaN(c)||isNaN(d)?String(l).localeCompare(String(i)):c-d,"ASC"===r?t:-t}),o.sorted=!0,o.sortCriteria={column:o.headers[s],direction:r};let n="Ordenada coluna ".concat(o.headers[s]," em ordem ").concat("ASC"===r?"crescente":"decrescente");return{updatedData:o,resultSummary:n}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de ordena\xe7\xe3o:",e),Error("Falha ao ordenar dados: ".concat(e instanceof Error?e.message:String(e)))}}async function e$(e,a){try{let{formula:t,range:r,resultCell:o,format:s}=a.data;if(!t||!r||!o)throw Error("Par\xe2metros insuficientes para opera\xe7\xe3o de f\xf3rmula");let n={...e},{endRow:l,endCol:i}=function(e){let a=e.split(":");if(2!==a.length)throw Error("Range inv\xe1lido: ".concat(e));let t=(0,eq.g_)(a,0),r=(0,eq.g_)(a,1);if(!t||!r)throw Error("Range inv\xe1lido: ".concat(e));let o=e0(t),s=e0(r);return{startRow:o.row,startCol:o.col,endRow:s.row,endCol:s.col}}(r),{row:c,col:d}=e0(o),u="=".concat(t,"(").concat(r,")");(function(e,a,t){for(;e.headers.length<t;){let a=String.fromCharCode(65+e.headers.length);e.headers.push(a)}for(;e.rows.length<a;){let a=Array(e.headers.length).fill("");e.rows.push(a)}for(let a=0;a<e.rows.length;a++)for(;e.rows[a].length<t;)e.rows[a].push("")})(n,Math.max(l,c),Math.max(i,d)),n.rows[c-1][d-1]=u;let m="Aplicada f\xf3rmula ".concat(t," no intervalo ").concat(r," com resultado em ").concat(o);return{updatedData:n,resultSummary:m}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de f\xf3rmula:",e),Error("Falha ao executar f\xf3rmula: ".concat(e instanceof Error?e.message:"Erro desconhecido"))}}function e0(e){let a=e.match(/([A-Za-z]+)([0-9]+)/);if(!a)throw Error("Refer\xeancia de c\xe9lula inv\xe1lida: ".concat(e));let t=(0,eG.A0)(a,1).toUpperCase(),r=(0,eG.A0)(a,2);if(!t||!r)throw Error("Refer\xeancia de c\xe9lula inv\xe1lida: ".concat(e));let o=0;for(let e=0;e<t.length;e++)o=26*o+(t.charCodeAt(e)-64);let s=parseInt(r,10);if(isNaN(s)||s<=0)throw Error("N\xfamero de linha inv\xe1lido: ".concat(r));return{row:s,col:o}}async function e1(e,a){try{let{sourceRange:t,rowFields:r,columnFields:o,dataFields:s,filterFields:n,calculations:l,dateGrouping:i}=a.data;if(!t)return{updatedData:e,resultSummary:"Erro: Intervalo de origem n\xe3o especificado para a tabela din\xe2mica."};let c=[];try{c=function(e,a){let t=a.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);if(t){var r,o,s,n,l,i,c,d;let a=(0,eG.A0)(t,1,"A"),u=(0,eG.A0)(t,2,"1"),m=(0,eG.A0)(t,3,"A"),p=(0,eG.A0)(t,4,"1"),f=(0,R.WH)(a),h=(0,R.WH)(m),g=Math.max(0,parseInt(u,10)-1),x=Math.max(0,parseInt(p,10)-1),v=[];for(let a=f;a<=h;a++){let t=null===(n=e.rows)||void 0===n?void 0:null===(s=n[g])||void 0===s?void 0:null===(o=s.cells)||void 0===o?void 0:null===(r=o[a])||void 0===r?void 0:r.value,l=void 0!==t?String(t):"Coluna".concat(a+1);v.push(l)}let b=[];for(let a=g+1;a<=x;a++){let t={};for(let r=f;r<=h;r++){let o=r-f;if(o>=0&&o<v.length){let s=v[o],n=null===(d=e.rows)||void 0===d?void 0:null===(c=d[a])||void 0===c?void 0:null===(i=c.cells)||void 0===i?void 0:null===(l=i[r])||void 0===l?void 0:l.value;s&&void 0!==n&&(t[s]=n)}}b.push(t)}return b}throw Error("Formato de intervalo '".concat(a,"' n\xe3o reconhecido"))}(e,t)}catch(a){return{updatedData:e,resultSummary:"Erro ao extrair dados de origem: ".concat(a instanceof Error?a.message:String(a))}}if(0===c.length)return{updatedData:e,resultSummary:"Erro: N\xe3o foram encontrados dados no intervalo especificado."};let d=function(e,a,t,r){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[],s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],n=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[],l=e;o.length>0&&o[0],n&&n.length>0&&(l=function(e,a){let t=[...e];for(let e of a){let{field:a,by:r}=e,o="".concat(a,"_").concat(r);for(let e of t){let t,s,n;let l=e[a];if(l){try{if(t=new Date(l),isNaN(t.getTime()))continue}catch(e){continue}switch(r){case"years":e[o]=t.getFullYear();break;case"quarters":e[o]="Q".concat(Math.floor(t.getMonth()/3)+1," ").concat(t.getFullYear());break;case"months":e[o]="".concat(t.toLocaleString("default",{month:"long"})," ").concat(t.getFullYear());break;case"weeks":s=new Date(t),n=t.getDay(),s.setDate(t.getDate()-n),e[o]="Semana de ".concat(s.toLocaleDateString());break;case"days":e[o]=t.toLocaleDateString()}}}}return t}(l,n));let i={},c={};if(s&&s.length>0)for(let e of s){let a=e.field;switch(e.function){case"sum":c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0);break;case"average":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?a.reduce((e,a)=>e+Number(a),0)/a.length:0};break;case"count":c[a]=e=>e.length;break;case"max":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.max(...a.map(e=>Number(e))):0};break;case"min":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.min(...a.map(e=>Number(e))):0};break;default:c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)}}else r.forEach(e=>{c[e]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)});for(let e of l){let o=a.map(a=>e[a]||"Vazio").join("|"),s=t.map(a=>e[a]||"Vazio").join("|");for(let a of(i[o]||(i[o]={}),i[o][s]||(i[o][s]={}),r))i[o][s][a]||(i[o][s][a]=[]),i[o][s][a].push(e[a])}let d={};for(let e in i)for(let a in d[e]={},i[e])for(let t of(d[e][a]={},r)){var u,m;let r=(null===(m=i[e])||void 0===m?void 0:null===(u=m[a])||void 0===u?void 0:u[t])||[],o=c[t];o?d[e][a][t]=o(r):d[e][a][t]=r.reduce((e,a)=>e+(Number(a)||0),0)}return{config:{rowFields:a,columnFields:t,dataFields:r,filterFields:o,calculations:s},data:d,rowKeys:Object.keys(d),columnKeys:Object.keys(Object.values(d)[0]||{})}}(c,r||[],o||[],s||[],n||[],l,i);return{updatedData:{...e,pivotTables:{...e.pivotTables||{},"Tabela Din\xe2mica":d}},resultSummary:"Tabela din\xe2mica criada com sucesso usando ".concat(r.length," campo(s) de linha, ").concat(o.length," campo(s) de coluna e ").concat(s.length," campo(s) de dados.")}}catch(a){return{updatedData:e,resultSummary:"Erro ao criar tabela din\xe2mica: ".concat(a instanceof Error?a.message:String(a))}}}let e2=(0,eB.createExcelAIProcessor)();async function e4(e){try{if(e2&&"function"==typeof e2.processQuery)try{let r=await e2.processQuery(e);if(r&&r.operations&&r.operations.length>0){var a,t;let e={operations:r.operations,success:null===(a=r.success)||void 0===a||a,error:null!==(t=r.error)&&void 0!==t?t:null};return void 0!==r.message&&(e.message=r.message),e}}catch(e){console.error("Error in AI processor, falling back to simple parser",e)}return function(e){let a=[],t=null;try{for(let t of function(e){let a=[];for(let t of[{regex:/=(SOMA|MÉDIA|MÁXIMO|MÍNIMO|CONT|SE|PROCV|ÍNDICE|CORRESP)[\s(]/gi,type:"f\xf3rmula"},{regex:/coluna\s+([A-Z]+|[a-zA-Z0-9_]+)/gi,type:"opera\xe7\xe3o de coluna"},{regex:/filtr[aer]\s+.*\s+onde\s+.*[><]=?|contém|entre/gi,type:"filtro"},{regex:/orden[ae][r]?\s+.*\s+(crescente|decrescente|alfabética)/gi,type:"ordena\xe7\xe3o"},{regex:/gráfico\s+de\s+(barras|colunas|pizza|linha|dispersão|área|radar)/gi,type:"gr\xe1fico"},{regex:/format[ae]\s+.*\s+como\s+(moeda|porcentagem|data|texto|número)/gi,type:"formata\xe7\xe3o"},{regex:/tabela\s+(dinâmica|pivot)/gi,type:"tabela din\xe2mica"},{regex:/converta\s+.*\s+em\s+tabela|transform[ae]\s+.*\s+em\s+tabela/gi,type:"tabela"},{regex:/(mapa\s+de\s+calor|heatmap|boxplot|histograma|sparklines|minigráficos)/gi,type:"visualiza\xe7\xe3o avan\xe7ada"},{regex:/(previsão|forecast|tendência|correlação|regressão|análise\s+estatística)/gi,type:"an\xe1lise de dados"}]){let r;let o=new RegExp(t.regex);for(;null!==(r=o.exec(e))&&(a.push("".concat(t.type," ").concat(r[0].trim())),o.global););}return a}(e))a.push(...function(e){let a;let t=[],r=/OPERAÇÃO:\s*FÓRMULA[\s\S]*?TIPO:\s*([^\n]+)[\s\S]*?RANGE:\s*([^\n]+)[\s\S]*?RESULTADO_CÉLULA:\s*([^\n]+)(?:[\s\S]*?FORMATO:\s*([^\n]+))?/gi;for(;null!==(a=r.exec(e));){let e=(0,eG.A0)(a,1).trim(),r=(0,eG.A0)(a,2).trim(),o=(0,eG.A0)(a,3).trim(),s=(0,eG.A0)(a,4).trim();if(e&&r&&o){let a=function(e){let a={SOMA:"SUM",MÉDIA:"AVERAGE",MEDIA:"AVERAGE",MÁXIMO:"MAX",MAXIMO:"MAX",MÍNIMO:"MIN",MINIMO:"MIN",CONTAGEM:"COUNT",CONTAR:"COUNT",SE:"IF",CONTARVALORES:"COUNTIF",SOMASE:"SUMIF",PROCV:"VLOOKUP",PROCURARVALOR:"VLOOKUP",CONCATENAR:"CONCATENATE",DESVPAD:"STDEV",ARREDONDAR:"ROUND"},t=e.toUpperCase();return a[t]?a[t]:t}(e),n={type:eZ.ox.FORMULA,data:{formula:a,range:r,resultCell:o,format:s||void 0}};t.push(n)}}return t}(t)),a.push(...function(e){let a=[];for(let{regex:o,operation:s}of[{regex:/(?:some|soma|somar)(?:\s+(?:os\s+valores\s+(?:da|na)|a))?\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/(?:quero|preciso|necessito)(?:\s+(?:d[ae]|saber))?\s+(?:a\s+)?soma\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/calcule\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/conte\s+quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"},{regex:/quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"}]){let n;for(o.lastIndex=0;null!==(n=o.exec(e));){var t,r;let e=(null===(t=n[1])||void 0===t?void 0:t.trim())||"",o=null===(r=n[2])||void 0===r?void 0:r.trim(),l=/^\d+$/.test(e)?parseInt(e,10):void 0;a.push({type:eZ.ox.COLUMN_OPERATION,data:{column:e,columnName:e,columnIndex:l,operation:s,targetCell:o,description:"".concat(s," na coluna ").concat(e)}})}}if(a.length>1){let e=[],t=new Set;for(let r of a){let a="".concat(r.data.operation,"-").concat(r.data.column);t.has(a)||(t.add(a),e.push(r))}return e}return a}(t)),a.push(...function(e){let a=[];for(let t of[/criar\s+(um\s+)?gráfico\s+de\s+(\w+)(?:\s+usando|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/adicionar\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+para|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/inserir\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+baseado|\s+com\s+base)(?:\s+em|\s+n[aeo]s?)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi]){let r;for(;null!==(r=t.exec(e));){let e={barra:"bar",barras:"bar",coluna:"column",colunas:"column",linha:"line",linhas:"line",pizza:"pie",torta:"pie",dispersão:"scatter",área:"area",radar:"radar",bolhas:"bubble",donut:"doughnut",rosca:"doughnut"}[(0,eG.A0)(r,2,"column").toLowerCase()]||"column",t=(0,eG.A0)(r,3,"");a.push({type:"chart",chartType:e,dataRange:t,position:"auto",title:"Gr\xe1fico de ".concat(e)})}}return a}(t)),a.push(...function(e){let a=[];return[{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*>\s*([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:maior(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*<\s*([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:menor(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*(?:=|igual\s+a)\s*['"]?([^'"]+)['"]?/gi,operator:"EQUALS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:contenha|contém|contem|contenha[m])\s+['"]?([^'"]+)['"]?/gi,operator:"CONTAINS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:entre|esteja[m]?\s+entre)\s+([0-9.,]+)\s+e\s+([0-9.,]+)/gi,operator:"BETWEEN"}].forEach(t=>{let r,{regex:o,operator:s}=t;for(;null!==(r=o.exec(e));){let e=(0,eG.A0)(r,1,""),t=(0,eG.A0)(r,2,"").replace(/['"]/g,""),o="BETWEEN"===s?(0,eG.A0)(r,3,""):void 0,n=isNaN(Number(t.replace(",",".")))?t:Number(t.replace(",",".")),l=o&&!isNaN(Number(o.replace(",",".")))?Number(o.replace(",",".")):o;a.push({type:"FILTER",data:{column:e,operator:s,value:n,value2:l}})}}),a}(t)),a.push(...function(e){let a=[];return[{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(crescente|ascendente|alfabética)/gi,direction:"ASC"},{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(decrescente|descendente)/gi,direction:"DESC"}].forEach(t=>{let r,{regex:o,direction:s}=t;for(;null!==(r=o.exec(e));){let e=(0,eG.A0)(r,1,"");a.push({type:"SORT",data:{column:e,direction:s}})}}),a}(t)),a.push(...function(e){let a=[],t=e.match(/criar\s+(tabela\s+dinâmica|pivot)\s+com\s+(.+?)\s+nas\s+linhas,\s+(.+?)\s+nas\s+colunas\s+e\s+(.+?)\s+(?:nos|como)\s+valores/i);if(t&&t.length>=5){var r,o,s;a.push({type:"TABLE",data:{subtype:"PIVOT_TABLE",rowsField:(null===(r=t[2])||void 0===r?void 0:r.trim())||"",columnsField:(null===(o=t[3])||void 0===o?void 0:o.trim())||"",valuesField:(null===(s=t[4])||void 0===s?void 0:s.trim())||"",aggregation:"SUM"}})}return a}(t)),a.push(...function(e){let a=[],t=e.match(/(?:definir|colocar|mudar)\s+(?:o\s+)?valor\s+(?:para\s+)?(\d+(?:[,.]\d+)?)\s+na\s+célula\s+([A-Z]+\d+)/i);return t&&t.length>=3&&a.push({type:eZ.ox.CELL_UPDATE,data:{cell:t[2]||"",value:parseFloat((t[1]||"0").replace(",",".")),valueType:"number"}}),a}(t)),a.push(...function(e){let a=[],t=e.match(/(?:destacar|colorir)\s+células\s+(?:com\s+valores\s+)?(acima|abaixo)\s+(?:de|do)\s+(\d+(?:[,.]\d+)?)\s+(?:de|em|com)\s+(?:cor\s+)?(\w+)/i);if(t&&t.length>=4){var r,o;let e=(null===(r=t[1])||void 0===r?void 0:r.toLowerCase())||"",s=t[2]||"0",n=(null===(o=t[3])||void 0===o?void 0:o.toLowerCase())||"vermelho";a.push({type:eZ.ox.FORMAT,data:{format:"conditional",condition:"acima"===e?">":"<",value:parseFloat(s.replace(",",".")),color:n}})}return a}(t)),a.push(...function(e){let a=[];for(let{regex:t,handler:r}of[{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?/i,handler:e=>{var a,t,r,o;let s=null===(a=e[1])||void 0===a?void 0:a.trim(),n=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],l=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],i=(null===(o=e[4])||void 0===o?void 0:o.split(/\s*,\s*/))||[];return s?{type:eZ.ox.PIVOT_TABLE,data:{sourceRange:s,rowFields:n.filter(e=>e),columnFields:l.filter(e=>e),dataFields:i.filter(e=>e),calculations:i.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+calculando|mostrando|usando)(?:\s+a)?\s+(soma|média|contagem|máximo|mínimo)(?:\s+d[aoe])?\s+([^,]+)/i,handler:e=>{var a,t,r,o,s;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],i=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],c=null===(o=e[4])||void 0===o?void 0:o.toLowerCase(),d=(null===(s=e[5])||void 0===s?void 0:s.split(/\s*,\s*/))||[],u={soma:"sum",média:"average",contagem:"count",máximo:"max",mínimo:"min"};return n&&c?{type:eZ.ox.PIVOT_TABLE,data:{sourceRange:n,rowFields:l.filter(e=>e),columnFields:i.filter(e=>e),dataFields:d.filter(e=>e),calculations:d.map(e=>({field:e,function:u[c]||"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?(?:\s+filtrando\s+por\s+([^,]+))?/i,handler:e=>{var a,t,r,o,s;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],i=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],c=(null===(o=e[4])||void 0===o?void 0:o.split(/\s*,\s*/))||[],d=(null===(s=e[5])||void 0===s?void 0:s.split(/\s*,\s*/))||[];return n?{type:eZ.ox.PIVOT_TABLE,data:{sourceRange:n,rowFields:l.filter(e=>e),columnFields:i.filter(e=>e),dataFields:c.filter(e=>e),filterFields:d.filter(e=>e),calculations:c.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+agrupando\s+([^,]+)\s+por\s+(anos|trimestres|meses|dias|semanas))/i,handler:e=>{var a,t,r,o,s;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=(null===(t=e[2])||void 0===t?void 0:t.split(/\s*,\s*/))||[],i=(null===(r=e[3])||void 0===r?void 0:r.split(/\s*,\s*/))||[],c=null===(o=e[4])||void 0===o?void 0:o.trim(),d=null===(s=e[5])||void 0===s?void 0:s.toLowerCase();return n&&c&&d?{type:eZ.ox.PIVOT_TABLE,data:{sourceRange:n,rowFields:l.filter(e=>e),columnFields:i.filter(e=>e),dataFields:["Contagem"],dateGrouping:[{field:c,by:{anos:"years",trimestres:"quarters",meses:"months",dias:"days",semanas:"weeks"}[d]}]}}:null}}]){let o=e.match(t);if(o){let e=r(o);e&&a.push(e)}}return a}(t)),a.push(...function(e){let a=[];for(let{regex:t,handler:r}of[{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+onde|quando)(?:\s+(?:os?|as?))?\s+(?:valor(?:es)?|célula[s]?)\s+(?:for(?:em)?|estiver(?:em)?|seja[m]?)\s+(maior(?:\s+que)?|menor(?:\s+que)?|igual(?:\s+a)?|maior\s+ou\s+igual(?:\s+a)?|menor\s+ou\s+igual(?:\s+a)?|diferente(?:\s+de)?|entre)\s+(?:a|de)?\s+(.+?)(?:\s+com\s+(?:cor|estilo|formato|fundo)\s+(.+))?$/i,handler:e=>{var a,t,r,o;let s=null===(a=e[1])||void 0===a?void 0:a.trim(),n=null===(t=e[2])||void 0===t?void 0:t.toLowerCase(),l=null===(r=e[3])||void 0===r?void 0:r.trim(),i=null===(o=e[4])||void 0===o?void 0:o.trim();if(!s||!n||!l)return null;let c=[];if("entre"===n){let e=l.split(/\s+e\s+/);if(2!==e.length)return null;c=e}else c=[l];let d={};if(i){let e=i.toLowerCase();for(let[a,t]of Object.entries({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"}))if(e.includes(a)){d.fill={type:"solid",color:t},(e.includes("texto")||e.includes("fonte"))&&(d.font={color:t});break}e.includes("negrito")&&(d.font={...d.font,bold:!0}),(e.includes("it\xe1lico")||e.includes("italico"))&&(d.font={...d.font,italic:!0}),e.includes("sublinhado")&&(d.font={...d.font,underline:!0})}return d.fill||d.font||(d.fill={type:"solid",color:"#FFEB9C"}),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"cellValue",range:s,cellValue:{operator:({"maior que":"greaterThan",maior:"greaterThan","menor que":"lessThan",menor:"lessThan","igual a":"equal",igual:"equal","maior ou igual a":"greaterThanOrEqual","maior ou igual":"greaterThanOrEqual","menor ou igual a":"lessThanOrEqual","menor ou igual":"lessThanOrEqual","diferente de":"notEqual",diferente:"notEqual",entre:"between"})[n],values:c,style:d}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+escala\s+de\s+cores\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+de\s+(.+?)\s+(?:até|para)\s+(.+?))?(?:\s+com\s+(?:valor\s+)?(mínimo|minimo|menor|baixo)\s+(?:em|como|na cor)\s+(.+?)(?:\s+e\s+(?:valor\s+)?(máximo|maximo|maior|alto)\s+(?:em|como|na cor)\s+(.+?))?)?$/i,handler:e=>{var a,t,r;let o=null===(a=e[1])||void 0===a?void 0:a.trim();if(!o)return null;let s="#FF8080",n="#80FF80";return e[5]&&(s=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[null===(t=e[5])||void 0===t?void 0:t.toLowerCase().trim()]||s),e[7]&&(n=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[null===(r=e[7])||void 0===r?void 0:r.toLowerCase().trim()]||n),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"colorScale",range:o,colorScale:{min:{type:"min",color:s},max:{type:"max",color:n}}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+barra(?:s)?\s+de\s+dados\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:em|na|com|de)\s+cor\s+(.+?))?(?:\s+(?:com|e)\s+(?:borda|borda)\s+(.+?))?(?:\s+(?:gradient(?:e)?|degradê))?/i,handler:e=>{var a,t,r,o,s;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=null===(t=e[2])||void 0===t?void 0:t.toLowerCase().trim(),i=null===(r=e[3])||void 0===r?void 0:r.toLowerCase().trim(),c=(null===(o=e[0])||void 0===o?void 0:o.toLowerCase().includes("gradient"))||(null===(s=e[0])||void 0===s?void 0:s.toLowerCase().includes("degrad\xea"));if(!n)return null;let d="#638EC6";l&&(d=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[l]||d);let u=!1,m="#000000";return i&&(u=!0,m=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[i]||m),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"dataBar",range:n,dataBar:{min:{type:"min"},max:{type:"max"},color:d,gradient:!1!==c,showValue:!0,border:u,borderColor:m}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:um)?\s+conjunto\s+de\s+ícones\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:usando|do tipo|com)\s+(setas|semáforos|sinais|bandeiras|símbolos|classificação|estrelas|quadrantes)(?:\s+(\d+))?)?(?:\s+(?:invertido|reverso))?/i,handler:e=>{var a,t,r,o,s;let n=null===(a=e[1])||void 0===a?void 0:a.trim(),l=null===(t=e[2])||void 0===t?void 0:t.toLowerCase().trim(),i=null===(r=e[3])||void 0===r?void 0:r.trim(),c=(null===(o=e[0])||void 0===o?void 0:o.toLowerCase().includes("invertido"))||(null===(s=e[0])||void 0===s?void 0:s.toLowerCase().includes("reverso"));if(!n)return null;let d="3TrafficLights";if(l){let e=i?parseInt(i,10):3,a=[3,4,5].includes(e)?e:3;d="".concat(a).concat({setas:"Arrows",semáforos:"TrafficLights",sinais:"Signs",bandeiras:"Flags",símbolos:"Symbols",classificação:"Rating",estrelas:"Rating",quadrantes:"Quarters"}[l]||"TrafficLights")}let u=[];return d.startsWith("3")?(u.push({value:67,type:"percent"}),u.push({value:33,type:"percent"})):d.startsWith("4")?(u.push({value:75,type:"percent"}),u.push({value:50,type:"percent"}),u.push({value:25,type:"percent"})):d.startsWith("5")&&(u.push({value:80,type:"percent"}),u.push({value:60,type:"percent"}),u.push({value:40,type:"percent"}),u.push({value:20,type:"percent"})),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"iconSet",range:n,iconSet:{type:d,reverse:c,showValue:!0,thresholds:u}}}}},{regex:/(?:destaque|realce|marque)\s+(?:os|as)?\s+(\d+)(?:\s+por\s+cento|\s*%)?\s+(?:valores|células)?\s+(maiores|melhores|top|superiores|menores|piores|bottom|inferiores)(?:\s+(?:valores|células))?(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{var a,t,r,o;let s=(0,eG.A0)(e,1),n=(0,eG.A0)(e,2,""),l=(0,eG.A0)(e,3),i=(0,eG.A0)(e,4,"").toLowerCase(),c=n.includes("top")||n.includes("melhores")||n.includes("maiores")||n.includes("superiores"),d=(null===(a=e[0])||void 0===a?void 0:a.toLowerCase().includes("por cento"))||(null===(t=e[0])||void 0===t?void 0:t.toLowerCase().includes("%"));if(!s||!l)return null;let u=parseInt(s,10),m={fill:{type:"solid",color:c?"#C6EFCE":"#FFC7CE"}};return i&&(m.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[i]||(null!==(o=null===(r=m.fill)||void 0===r?void 0:r.color)&&void 0!==o?o:"#FFEB9C")}),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"topBottom",range:l,topBottom:{type:c?"top":"bottom",value:u,isPercent:d,style:m}}}}},{regex:/(?:destaque|realce|marque)(?:\s+as)?\s+células\s+(?:que\s+)?(?:contenham|contêm|com|contendo)\s+(?:o texto|a palavra|o termo)\s+(?:"(.+?)"|'(.+?)'|(\w+))(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{var a,t,r,o;let s=e[1]||e[2]||e[3],n=null===(a=e[4])||void 0===a?void 0:a.trim(),l=null===(t=e[5])||void 0===t?void 0:t.toLowerCase().trim();if(!s||!n)return null;let i={fill:{type:"solid",color:"#FFEB9C"}};return l&&(i.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[l]||(null!==(o=null===(r=i.fill)||void 0===r?void 0:r.color)&&void 0!==o?o:"#FFEB9C")}),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"textContains",range:n,textContains:{text:s,style:i}}}}},{regex:/(?:destaque|realce|marque)(?:\s+os)?\s+(?:valores|células)?\s+(duplicados|únicos|unicos|repetidos)(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=(0,eG.A0)(e,1,""),t=(0,eG.A0)(e,2),r=(0,eG.A0)(e,3,"").toLowerCase();if(!t)return null;let o=a.includes("duplicado")||a.includes("repetido"),s={fill:{type:"solid",color:o?"#FFC7CE":"#C6EFCE"}};if(r){var n,l;s.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||(null!==(l=null===(n=s.fill)||void 0===n?void 0:n.color)&&void 0!==l?l:"#FFEB9C")}}return{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:"duplicateValues",range:t,duplicateValues:{type:o?"duplicate":"unique",style:s}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:com|usando)\s+(?:a\s+)?fórmula\s+(?:"(.+?)"|'(.+?)'|(\S+.+?\S+))(?:\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{var a,t,r,o;let s=e[1]||e[2]||e[3],n=null===(a=e[4])||void 0===a?void 0:a.trim(),l=null===(t=e[5])||void 0===t?void 0:t.toLowerCase().trim();if(!s||!n)return null;let i={fill:{type:"solid",color:"#FFEB9C"}};return l&&(i.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[l]||(null!==(o=null===(r=i.fill)||void 0===r?void 0:r.color)&&void 0!==o?o:"#FFEB9C")}),{type:eZ.ox.CONDITIONAL_FORMAT,data:{type:eZ.ox.FORMULA,range:n,formula:{formula:s,style:i}}}}}]){let o=e.match(t);if(o){let e=r(o);e&&a.push(e)}}return a}(t)),a.push(...function(e){let a=[];for(let t of[{regex:/(?:crie|adicione|gere|insira)\s+(?:uma)?\s+visualização\s+(?:em\s+)?3[dD](?:\s+d[eo])?\s+(?:tipo\s+)?(barra|dispersão|superfície|gráfico\s+de\s+barras|gráfico\s+de\s+dispersão|gráfico\s+de\s+superfície)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a,t;let r="3d-bar",o=(null===(a=e[1])||void 0===a?void 0:a.toLowerCase())||"";o.includes("disp")||o.includes("scatt")?r="3d-scatter":(o.includes("super")||o.includes("surf"))&&(r="3d-surface");let s=null===(t=e[2])||void 0===t?void 0:t.trim(),n=e[3]||e[4]||e[5];return s?{type:eZ.ox.ADVANCED_VISUALIZATION,data:{type:r,sourceRange:s,title:n||"Visualiza\xe7\xe3o 3D de ".concat(o),viewMode:"3d",animation:!0,interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_VISUALIZATION,data:{type:"heat-map",sourceRange:t,title:r||"Mapa de Calor",colors:["#0033CC","#00CCFF","#FFFF00","#FF6600","#CC0000"],interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:treemap|mapa\s+de\s+árvore|mapa\s+de\s+arvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_VISUALIZATION,data:{type:"tree-map",sourceRange:t,title:r||"Treemap",theme:"gradient"}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+rede|network\s+graph|grafo\s+de\s+rede)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_VISUALIZATION,data:{type:"network-graph",sourceRange:t,title:r||"Grafo de Rede",interactive:!0,animation:!0}}:null}}]){let r=e.match(t.regex);if(r){let e=t.handler(r);e&&a.push(e)}}return a}(t)),a.push(...function(e){let a=[];for(let{regex:t,handler:r}of[{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(radar|teia\s+de\s+aranha)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a,t;let r=(null===(a=e[1])||void 0===a||a.toLowerCase().includes("radar"),"radar"),o=null===(t=e[2])||void 0===t?void 0:t.trim(),s=e[3]||e[4]||e[5];return o?{type:eZ.ox.ADVANCED_CHART,data:{type:r,sourceRange:o,title:s||"Gr\xe1fico de Radar",legend:{show:!0,position:"right"},animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?|bubble)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"bubble",sourceRange:t,title:r||"Gr\xe1fico de Bolhas",xAxis:{title:"Eixo X",gridLines:!0},yAxis:{title:"Eixo Y",gridLines:!0},legend:{show:!0,position:"bottom"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:funil|funnel)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"funnel",sourceRange:t,title:r||"Gr\xe1fico de Funil",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:área[\s-]spline|área[\s-]curva)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?(?:\s+(?:empilhado|stacked))?/i,handler:e=>{var a,t,r;let o=null===(a=e[1])||void 0===a?void 0:a.trim(),s=e[2]||e[3]||e[4],n=(null===(t=e[0])||void 0===t?void 0:t.toLowerCase().includes("empilhado"))||(null===(r=e[0])||void 0===r?void 0:r.toLowerCase().includes("stacked"));return o?{type:eZ.ox.ADVANCED_CHART,data:{type:"area-spline",sourceRange:o,title:s||"Gr\xe1fico de \xc1rea Spline",stacked:n,xAxis:{gridLines:!1},yAxis:{gridLines:!0},grid:{y:!0,x:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:barras?[\s-]agrupadas?|barras?[\s-]grouped|barras?[\s-]clusters?)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"bar-grouped",sourceRange:t,title:r||"Gr\xe1fico de Barras Agrupadas",xAxis:{gridLines:!1},yAxis:{gridLines:!0,title:"Valores"},legend:{show:!0,position:"bottom",orientation:"horizontal"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+)?(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"heatmap",sourceRange:t,title:r||"Mapa de Calor",legend:{show:!0,position:"right"},grid:{x:!1,y:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?[\s-]3[dD]|scatter[\s-]3[dD]|dispersão[\s-]3[dD])(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"scatter-3d",sourceRange:t,title:r||"Gr\xe1fico de Dispers\xe3o 3D",animation:!0,xAxis:{title:"Eixo X"},yAxis:{title:"Eixo Y"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:rosca|donut|doughnut)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"donut",sourceRange:t,title:r||"Gr\xe1fico de Rosca",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:sankey|fluxo)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"sankey",sourceRange:t,title:r||"Diagrama de Sankey",animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:treemap|mapa\s+de\s+árvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"treemap",sourceRange:t,title:r||"Gr\xe1fico Treemap",legend:{show:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um[a])?\s+(?:gráfico\s+de\s+)?(?:nuvem\s+de\s+palavras|wordcloud|tag\s+cloud)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{var a;let t=null===(a=e[1])||void 0===a?void 0:a.trim(),r=e[2]||e[3]||e[4];return t?{type:eZ.ox.ADVANCED_CHART,data:{type:"wordcloud",sourceRange:t,title:r||"Nuvem de Palavras",animation:!0,colors:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"]}}:null}}]){let o=e.match(t);if(o){let e=r(o);e&&a.push(e)}}return a}(t));let t=a.map(e=>eP(e));return{operations:t,error:null,success:!0,message:"".concat(t.length," opera\xe7\xf5es extra\xeddas")}}catch(e){return{operations:[],error:t=e instanceof Error?e.message:String(e),success:!1,message:"Erro ao processar: ".concat(t)}}}(e)}catch(e){return{operations:[],success:!1,error:"Erro ao analisar comando: ".concat(e instanceof Error?e.message:String(e))}}}async function e3(e,a){if(!a||0===a.length)return{updatedData:e,resultSummary:["Nenhuma opera\xe7\xe3o para executar"]};let t=JSON.parse(JSON.stringify(e)),r=[],o=[],s=[];for(let e of a)try{let a;let s=eP(e);switch(s.type){case eZ.ox.COLUMN_OPERATION:a=await eX(t,s);break;case eZ.ox.FORMULA:a=await e$(t,s);break;case eZ.ox.CHART:a=await eW(t,s);break;case eZ.ox.FILTER:a=await eK(t,s);break;case eZ.ox.SORT:a=await eQ(t,s);break;case eZ.ox.PIVOT_TABLE:a=await e1(t,s);break;case eZ.ox.CONDITIONAL_FORMAT:a=await eY(t,s);break;case eZ.ox.ADVANCED_VISUALIZATION:a=await eV(t,s);break;case eZ.ox.TABLE:a=await e6(t,s);break;case eZ.ox.CELL_UPDATE:a=await e8(t,s);break;case eZ.ox.FORMAT:a=await e9(t,s);break;default:{let e=function(e){let a=e.data||{};return a.formula||a.range?{...e,type:eZ.ox.FORMULA}:a.chart_type||a.title?{...e,type:eZ.ox.CHART}:a.data&&Array.isArray(a.data)?{...e,type:eZ.ox.TABLE}:a.order_by||a.direction?{...e,type:eZ.ox.SORT}:a.condition||a.filter_column?{...e,type:eZ.ox.FILTER}:a.background_color||a.text_color||a.format?{...e,type:eZ.ox.FORMAT}:{...e,type:"GENERIC"}}(s);a=await e5(t,e)}}if(a&&(t=a.updatedData,"string"==typeof a.resultSummary?r.push(a.resultSummary):Array.isArray(a.resultSummary)&&r.push(String(a.resultSummary)),"modifiedCells"in a&&Array.isArray(a.modifiedCells)&&a.modifiedCells.length>0))for(let e of a.modifiedCells)o.push(e)}catch(t){let a=t instanceof Error?"Erro ao executar opera\xe7\xe3o ".concat(e.type,": ").concat(t.message):"Erro desconhecido ao executar opera\xe7\xe3o ".concat(e.type);console.error(a,t),s.push(a),r.push("⚠️ ".concat(a))}let n=o.filter((e,a,t)=>a===t.findIndex(a=>a.row===e.row&&a.col===e.col)),l={updatedData:t,resultSummary:r};return n.length>0&&(l.modifiedCells=n),s.length>0&&(l.errors=s),l}async function e5(e,a){return a.data&&"object"==typeof a.data&&("formula"in a.data||"formula_type"in a.data)?e$(e,{...a,type:eZ.ox.FORMULA,data:a.data||{}}):a.data&&"object"==typeof a.data&&"chart_type"in a.data?eW(e,{...a,type:eZ.ox.CHART,data:a.data||{}}):a.data&&"object"==typeof a.data&&"data"in a.data&&Array.isArray(a.data.data)?e6(e,{...a,type:eZ.ox.TABLE,data:a.data||{}}):{updatedData:e,resultSummary:"Opera\xe7\xe3o gen\xe9rica n\xe3o suportada",modifiedCells:[]}}async function e9(e,a){let{target:t,format:r,decimals:o,locale:s,dateFormat:n,condition:l,value:i,color:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{};d.formatting&&"object"==typeof d.formatting?d.formatting=d.formatting:d.formatting={};let u="";try{if(d.headers&&Array.isArray(d.headers)&&d.rows){if((/^[A-Z]+$/.test(t)?t.charCodeAt(0)-65:d.headers.findIndex(e=>e===t))>=0){d.formatting||(d.formatting={});let e={type:r};"currency"===r?(e.decimals=o||2,e.locale=s||"pt-BR",u="Coluna ".concat(t," formatada como moeda com ").concat(o||2," casas decimais")):"percentage"===r?(e.decimals=o||0,u="Coluna ".concat(t," formatada como porcentagem com ").concat(o||0," casas decimais")):"date"===r?(e.dateFormat=n||"dd/mm/yyyy",u="Coluna ".concat(t," formatada como data no formato ").concat(n||"dd/mm/yyyy")):"conditional"===r&&(e.condition=l,e.value=i,e.color=c,u="Formata\xe7\xe3o condicional aplicada na coluna ".concat(t," (valores ").concat(">"===l?"maiores":"menores"," que ").concat(i," destacados em ").concat(c,")"));let a=d.formatting;a[t]=e,d.formatting=a}}else if(t&&"string"==typeof t&&t.includes(":")){d.formatting||(d.formatting={});let e=d.formatting;e[t]={type:r,decimals:o||2,locale:s||"pt-BR",dateFormat:n||"dd/mm/yyyy"},d.formatting=e,u="Intervalo ".concat(t," formatado como ").concat(r)}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao aplicar formata\xe7\xe3o:",e),Error("Falha ao aplicar formata\xe7\xe3o: ".concat(e instanceof Error?e.message:String(e)))}}async function e8(e,a){let{cell:t,value:r,_valueType:o}=a.data,s="object"==typeof e&&null!==e?{...e}:{};try{if(s.headers&&Array.isArray(s.headers)&&s.rows&&Array.isArray(s.rows)){var n,l;let e=(null===(n=t.match(/^[A-Z]+/))||void 0===n?void 0:n[0])||"",a=parseInt((null===(l=t.match(/\d+/))||void 0===l?void 0:l[0])||"0",10);if(e&&a>0){let t=e.charCodeAt(0)-65,o=a-1;if(o>=s.rows.length)for(;s.rows.length<=o;)s.rows.push(Array(s.headers.length).fill(""));s.rows[o]&&(s.rows[o][t]=r)}}else s[t]=r;return{updatedData:s,resultSummary:"C\xe9lula ".concat(t,' atualizada com o valor "').concat(r,'"')}}catch(e){throw console.error("Erro ao atualizar c\xe9lula:",e),Error("Falha ao atualizar c\xe9lula: ".concat(e instanceof Error?e.message:String(e)))}}async function e6(e,a){let{subtype:t,range:r,hasHeaders:o,allData:s,function:n,rowsField:l,columnsField:i,valuesField:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{},u="";try{switch(t){case"CREATE_TABLE":d.tables&&Array.isArray(d.tables)||(d.tables=[]),s?(d.isTable=!0,d.tableHeaders=o,u="Todos os dados foram convertidos em tabela"):r&&Array.isArray(d.tables)&&(d.tables.push({range:String(r),hasHeaders:o}),u="Intervalo ".concat(String(r)," convertido em tabela"));break;case"ADD_TOTAL_ROW":if(d.tables&&Array.isArray(d.tables)&&0!==d.tables.length)Array.isArray(d.tables)&&d.tables.length>0&&(u="Linha de total adicionada \xe0 tabela");else if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)){let e=d.headers,a=d.rows,t=Array(e.length).fill("");e.forEach((e,r)=>{a.some(e=>"number"==typeof e[r]||"string"==typeof e[r]&&!isNaN(Number(e[r])))&&(t[r]={formula:"=SOMA(".concat(String.fromCharCode(65+r),"2:").concat(String.fromCharCode(65+r)).concat(a.length+1,")"),result:a.reduce((e,a)=>{let t=parseFloat(String(a[r]));return e+(isNaN(t)?0:t)},0)})}),t[0]=t[0]||"Total",d.rows=[...a,t],u="Linha de total adicionada \xe0 tabela"}break;case"PIVOT_TABLE":if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)&&l&&i&&c){let e=d.headers.findIndex(e=>e===l),a=d.headers.findIndex(e=>e===i),t=d.headers.findIndex(e=>e===c);if(e>=0&&a>=0&&t>=0){let r={},o=new Set,s=new Set;Array.isArray(d.rows)&&d.rows.forEach(n=>{var l,i,c;let d=String(null!==(l=n[e])&&void 0!==l?l:""),u=String(null!==(i=n[a])&&void 0!==i?i:""),m=parseFloat(String(null!==(c=n[t])&&void 0!==c?c:"0"))||0;o.add(d),s.add(u),r[d]||(r[d]={}),r[d][u]||(r[d][u]=0),r[d][u]+=m});let n=["",...Array.from(s)],m=Array.from(o).map(e=>{let a=[e];return Array.from(s).forEach(t=>{var o;let s=String((null===(o=r[e])||void 0===o?void 0:o[t])||0);a.push(s)}),a});d.pivotTable={headers:n,rows:m,rowsField:l,columnsField:i,valuesField:c},u="Tabela din\xe2mica criada com ".concat(l," nas linhas, ").concat(i," nas colunas e ").concat(c," como valores")}}}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de tabela:",e),Error("Falha ao executar opera\xe7\xe3o de tabela: ".concat(e instanceof Error?e.message:String(e)))}}var e7=t(56143),ae=t(91116);let aa={VERBOSE_LOGGING:!1},at=(e,a)=>{try{return new Worker(e,a)}catch(e){return console.warn("Worker n\xe3o p\xf4de ser criado:",e),null}},ar=new Map,ao=0,as=0,an=(0,w.memo)(e=>{let{rowIndex:a,colIndex:t,value:r,isModified:o,readOnly:n,onCellChange:l,header:i}=e,c="".concat(a,"-").concat(t);null!=r&&ar.set(c,String(r));let d=null!=r?r:(()=>{let e=ar.get(c);return e?(as++,e):(ao++,"")})(),u=(0,w.useCallback)(e=>{let r=e.target.value;ar.set(c,r),l(a,t,r)},[a,t,l,c]),m=o?"bg-blue-50 dark:bg-blue-900/30 transition-colors duration-1000":"",p=(0,w.useMemo)(()=>"table-cell p-1 border border-border ".concat(m),[m]);return(0,s.jsx)("div",{className:p,role:"gridcell","aria-colindex":t+1,"aria-rowindex":a+1,children:(0,s.jsx)("input",{type:"text",value:d,readOnly:n,onChange:u,className:"w-full bg-transparent border-0 focus:ring-1 focus:ring-blue-500 p-1","aria-label":"C\xe9lula ".concat(i).concat(a+1)})})},(e,a)=>e.value===a.value&&e.isModified===a.isModified&&e.readOnly===a.readOnly);an.displayName="OptimizedCell";let al=(0,w.memo)(e=>{let{visibleRows:a,totalRows:t,virtualizer:r}=e;return(0,w.useEffect)(()=>{if(t>1e3){let e=Array.from(ar.keys()),a=new Set,o=Math.max(0,r.range.startIndex-20),s=Math.min(t-1,r.range.endIndex+20);for(let e=o;e<=s;e++)a.add(e);e.forEach(e=>{let t=e.split("-")[0];if(t){let r=parseInt(t,10);a.has(r)||ar.delete(e)}})}},[a,t,r]),null});al.displayName="VirtualizedRowWrapper";let ai=(0,w.memo)(e=>{let{rowIndex:a,rowData:t,headers:r,modifiedCellsMap:o,readOnly:n,onCellChange:l,onRemoveRow:i}=e,c=(0,w.useCallback)(()=>{i(a)},[a,i]),d=(0,w.useMemo)(()=>t.map((e,t)=>{let i="".concat(a,"-").concat(t),c=o[i]||!1;return(0,s.jsx)(an,{rowIndex:a,colIndex:t,value:e,isModified:c,readOnly:n,onCellChange:l,header:r[t]||""},i)}),[t,a,o,n,l,r]),m=(0,w.useMemo)(()=>n?null:(0,s.jsx)("div",{className:"table-cell w-10 text-center",children:(0,s.jsx)(F.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:c,"aria-label":"Remover linha ".concat(a+1),children:(0,s.jsx)(u.Z,{className:"h-3 w-3"})})}),[n,c,a]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"table-cell w-10 text-center text-xs text-muted-foreground bg-muted",children:a+1}),d,m]})},(e,a)=>{if(e.readOnly!==a.readOnly||e.rowIndex!==a.rowIndex||e.rowData.length!==a.rowData.length||e.headers.length!==a.headers.length)return!1;if(e.rowData.length<=10){for(let t=0;t<e.rowData.length;t++){if(e.rowData[t]!==a.rowData[t])return!1;let r="".concat(e.rowIndex,"-").concat(t);if(e.modifiedCellsMap[r]!==a.modifiedCellsMap[r])return!1}return!0}{let t=Object.keys(e.modifiedCellsMap).filter(a=>a.startsWith("".concat(e.rowIndex,"-"))&&e.modifiedCellsMap[a]),r=Object.keys(a.modifiedCellsMap).filter(e=>e.startsWith("".concat(a.rowIndex,"-"))&&a.modifiedCellsMap[e]);return!(t.length!==r.length||t.some(e=>!a.modifiedCellsMap[e]))&&JSON.stringify(e.rowData)===JSON.stringify(a.rowData)}});ai.displayName="OptimizedRow";let ac=(e,a)=>{let t={};return e&&e.length>0?e.forEach(e=>{t["".concat(e.row,"-").concat(e.col)]=!0}):a&&(t["".concat(a.row,"-").concat(a.col)]=!0),!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e4;ar.size>e&&Array.from(ar.keys()).slice(0,1e3).forEach(e=>ar.delete(e))}(),t},ad=[{text:"Crie uma tabela de controle de horas",icon:(0,s.jsx)(l.Z,{className:"h-3 w-3"})},{text:"Adicione valida\xe7\xe3o de dados na coluna B",icon:(0,s.jsx)(i.Z,{className:"h-3 w-3"})},{text:"Gere um gr\xe1fico de barras com os dados",icon:(0,s.jsx)(c.Z,{className:"h-3 w-3"})},{text:"Calcule a m\xe9dia da coluna C",icon:(0,s.jsx)(i.Z,{className:"h-3 w-3"})},{text:"Formate a tabela com cores alternadas",icon:(0,s.jsx)(i.Z,{className:"h-3 w-3"})}],au=[{title:"Bem-vindo ao Excel Copilot",content:"Este assistente permite criar e editar planilhas atrav\xe9s de comandos em linguagem natural.",target:"header"},{title:"Assistente de IA",content:"Aqui voc\xea pode digitar comandos como 'Crie uma tabela de vendas' ou 'Calcule a m\xe9dia da coluna B'.",target:"ai-assistant"},{title:"Sugest\xf5es R\xe1pidas",content:"Exemplos de comandos que voc\xea pode usar. Clique em um deles para executar.",target:"suggestions"},{title:"Planilha Interativa",content:"Sua planilha ser\xe1 atualizada automaticamente conforme seus comandos. Voc\xea tamb\xe9m pode editar c\xe9lulas manualmente.",target:"spreadsheet"}],am=(0,w.memo)(e=>{let{command:a,onClick:t}=e;return(0,s.jsxs)(F.Button,{variant:"ghost",className:"h-8 px-2 text-sm justify-start w-full hover:bg-accent",onClick:t,children:[(0,s.jsx)("span",{className:"mr-2",children:a.icon}),(0,s.jsx)("span",{className:"truncate",children:a.text})]})});function ap(e){let{workbookId:a,initialData:r,readOnly:o=!1,onSave:l,initialCommand:c}=e,[C,S]=(0,w.useState)(r||{headers:["A","B","C"],rows:[["","",""],["","",""],["","",""]],charts:[],name:"Nova Planilha"}),[j,O]=(0,w.useState)([]),[R,T]=(0,w.useState)(-1),[k,I]=(0,w.useState)(!1),[_,L]=(0,w.useState)(!1),[D,M]=(0,w.useState)(!1),[z,Z]=(0,w.useState)(!1),[B,P]=(0,w.useState)(null),[U,q]=(0,w.useState)(!1),[H,J]=(0,w.useState)(!1),[W,Y]=(0,w.useState)(!1),[K,$]=(0,w.useState)(0),[ee,ea]=(0,w.useState)(!1),et=(0,w.useRef)(null),er=(0,w.useRef)([]),[eo,es]=(0,w.useState)(null),[en,ei]=(0,w.useState)(null),[ec,ed]=(0,w.useState)(!1),[eu,em]=(0,w.useState)(null),[ep,ef]=(0,w.useState)([]),eh=(0,w.useRef)(null),eg=(0,w.useCallback)((e,a)=>{let t=setTimeout(()=>{e(),er.current=er.current.filter(e=>e!==t)},a);return er.current.push(t),t},[]),eb=(0,w.useCallback)(e=>{if(j.length>0&&JSON.stringify(j[j.length-1])===JSON.stringify(e))return;let a=j.slice(0,R+1).slice(-19);O([...a,JSON.parse(JSON.stringify(e))]),T(a.length)},[j,R]),{processExcelCommand:eA,isProcessing:eE,lastModifiedCells:eR}=function(){let{onDataChange:e,onAddHistory:a}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[r,o]=(0,w.useState)(!1),[s,n]=(0,w.useState)([]),[l,i]=(0,w.useState)([]),c=(0,w.useRef)(null),{executeOperation:d,isProcessing:u,cancelAllOperations:m}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=(0,w.useRef)(null),[r,o]=(0,w.useState)(!1),s=(0,w.useRef)(new Map);return(0,w.useEffect)(()=>{try{if(a.current=at(new t.U(t(69037)),{type:"module"}),!a.current){aa.VERBOSE_LOGGING;return}aa.VERBOSE_LOGGING,function r(){try{a.current&&a.current.terminate(),a.current=new Worker(t.tu(new URL(t.p+t.u(38),t.b)),{type:void 0}),a.current.onmessage=a=>{let{result:t,error:r,requestId:n}=a.data,l=s.current.get(n);if(l){var i,c;if(s.current.delete(n),0===s.current.size&&o(!1),r){let a=Error(r);l.reject(a),null===(i=e.onError)||void 0===i||i.call(e,a,n)}else l.resolve(t),null===(c=e.onSuccess)||void 0===c||c.call(e,t,n)}},a.current.onerror=a=>{console.error("Erro no worker Excel:",a),s.current.forEach((a,t)=>{var r;let o=Error("Erro fatal no worker Excel");a.reject(o),null===(r=e.onError)||void 0===r||r.call(e,o,t)}),s.current.clear(),o(!1),r()}}catch(e){console.error("Erro ao reinicializar worker Excel:",e)}}()}catch(e){console.warn("Workers n\xe3o suportados ou bloqueados pela CSP, usando fallback:",e),a.current=null}return()=>{a.current&&(a.current.terminate(),a.current=null)}},[e]),{executeOperation:(0,w.useCallback)(async(e,t)=>{if(!a.current)try{o(!0);let a={updatedData:t,resultSummary:"Opera\xe7\xe3o ".concat(e.type," executada (fallback)"),modifiedCells:[]};return o(!1),a}catch(e){throw o(!1),e}let r=(0,ae.x0)();return o(!0),new Promise((o,n)=>{s.current.set(r,{operation:e,resolve:o,reject:n}),a.current.postMessage({operation:e,sheetData:t,requestId:r,operationType:e.type})})},[]),isProcessing:r,cancelAllOperations:(0,w.useCallback)(()=>{s.current.forEach((e,a)=>{e.reject(Error("Opera\xe7\xe3o cancelada"))}),s.current.clear(),o(!1)},[])}}({onSuccess:e=>{e.modifiedCells&&(n(e.modifiedCells),p())},onError:e=>{eH.logger.error("Erro no worker Excel:",e)}});(0,w.useEffect)(()=>()=>{c.current&&clearTimeout(c.current),m()},[m]);let p=(0,w.useCallback)(()=>{c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{n([]),c.current=null},3e3)},[]),f=(0,w.useCallback)(e=>e.length>2||e.some(e=>{let a=e.type?String(e.type).toUpperCase():void 0;return"FILTER"===a||"SORT"===a||"PIVOT_TABLE"===a||"CHART"===a||"ADVANCED_VISUALIZATION"===a}),[]);return{processExcelCommand:(0,w.useCallback)(async(t,s)=>{if(!t||!s)return null;if(r||u)return N.toast.info("Aguarde a conclus\xe3o da opera\xe7\xe3o anterior",{duration:2e3}),null;o(!0);try{a&&a(structuredClone(s));try{throw Error("IA n\xe3o dispon\xedvel no cliente")}catch(e){(0,e7.KE)("Erro no novo processor, tentando fallback:",e)}try{let a=await e4(t);if(!a.success||!(a.operations.length>0))return N.toast.info("Nenhuma opera\xe7\xe3o Excel",{description:a.message||"N\xe3o foi poss\xedvel extrair opera\xe7\xf5es Excel deste comando.",duration:4e3}),null;{let t=await e3(s,a.operations),r={updatedData:t.updatedData,resultSummary:Array.isArray(t.resultSummary)?t.resultSummary:[String(t.resultSummary)],modifiedCells:t.modifiedCells};return i(r.resultSummary),r.modifiedCells&&(n(r.modifiedCells),p()),e&&e(r.updatedData),N.toast.success("Opera\xe7\xf5es executadas",{description:r.resultSummary.join("; "),duration:3e3}),r.updatedData}}catch(e){(0,e7.H)("Erro no parser de comandos:",e)}return N.toast.error("N\xe3o foi poss\xedvel executar o comando",{description:"Tente reformular seu comando ou use um exemplo da lista de sugest\xf5es.",duration:4e3}),null}catch(e){return(0,e7.H)("Erro ao processar comando Excel:",e),N.toast.error("Erro ao processar comando",{description:e instanceof Error?e.message:"Ocorreu um erro desconhecido.",duration:4e3}),null}finally{o(!1)}},[r,u,e,a,p,d,f]),isProcessing:r||u,lastModifiedCells:s,lastOperationSummary:l}}({onDataChange:S,onAddHistory:eb}),{isConnected:eT,updateCursor:ek,broadcastCellChange:eI}=ex(a);(0,w.useCallback)(e=>eA(e,C),[eA,C]);let e_=(0,w.useCallback)(e=>{es(e.interpretation),ei({id:e.commandId||e._commandId||"",command:e.interpretation})},[]),eD=(0,w.useCallback)(e=>{e&&Array.isArray(e)&&0!==e.length&&(eb(C),S(t=>{let r={...t};r.headers=Array.isArray(t.headers)?[...t.headers]:[],r.rows=Array.isArray(t.rows)?[...t.rows]:[];let o=[];return e.forEach(e=>{if(e&&"object"==typeof e){if("cell_update"===e.type&&"number"==typeof e.row&&"number"==typeof e.col){if(Array.isArray(r.rows[e.row])||(r.rows[e.row]=Array(r.headers.length).fill("")),e.row>=0&&e.col>=0&&Array.isArray(r.headers)&&e.col<r.headers.length&&Array.isArray(r.rows)&&void 0!==r.rows[e.row]&&Array.isArray(r.rows[e.row]))try{r.rows[e.row]&&"number"==typeof e.col&&(r.rows[e.row][e.col]=e.value,o.push({row:e.row,col:e.col}))}catch(t){eH.logger.error("SpreadsheetEditor: Erro ao atualizar c\xe9lula",t,{row:e.row,col:e.col,operation:e.type,workbookId:a})}}else if("add_row"===e.type){let e=Array(r.headers.length).fill("");r.rows.push(e)}else"add_column"===e.type&&Array.isArray(r.headers)&&Array.isArray(r.rows)&&(r.headers.push(e.name||"Coluna ".concat(r.headers.length+1)),r.rows.forEach((e,a)=>{Array.isArray(e)?r.rows[a]=[...e,""]:r.rows[a]=Array(r.headers.length).fill("")}))}}),o.length>0&&P(o[0]||null),r}))},[C,eb,P]),{sendMessage:eZ,isProcessing:eB,confirmAndExecute:eP,cancelCommand:eV,pendingInterpretation:eU,messages:eq,error:eG,clearMessages:eJ,commandStatus:eW}=ez({workbookId:a,onMessageReceived:e=>{if(e)try{let a=JSON.parse(e);a.operations&&eD(a.operations)}catch(t){eH.logger.error("SpreadsheetEditor: Erro ao processar resposta da IA",t,{content:null==e?void 0:e.substring(0,100),workbookId:a})}},onInterpretation:e_});(0,w.useEffect)(()=>{eh.current={sendMessage:eZ,isProcessing:eB,confirmAndExecute:eP,cancelCommand:eV,pendingInterpretation:eU,messages:eq,error:eG,clearMessages:eJ,commandStatus:eW}},[eZ,eB,eP,eV,eU,eq,eG,eJ,eW]);let[eX,eY]=(0,w.useState)(null),[eK,eQ]=(0,w.useState)(!1),e$=(0,E.useRouter)();(0,w.useEffect)(()=>{let e=()=>{M(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,w.useEffect)(()=>()=>{er.current.forEach(e=>clearTimeout(e)),er.current=[]},[]);let e0=(0,w.useCallback)(()=>{R>0&&(T(R-1),S(JSON.parse(JSON.stringify(j[R-1]))),N.toast.info("A\xe7\xe3o desfeita"))},[j,R]),e1=(0,w.useCallback)(()=>{R<j.length-1&&(T(R+1),S(JSON.parse(JSON.stringify(j[R+1]))),N.toast.info("A\xe7\xe3o refeita"))},[j,R]);(0,w.useEffect)(()=>{r&&(S(r),O([JSON.parse(JSON.stringify(r))]),T(0))},[r]);let e2=(0,w.useCallback)(async()=>{if(!o)try{if(I(!0),l){await l(C),N.toast.success("Planilha salva com sucesso");return}if(!(await fetch("/api/workbooks/".concat(a,"/sheets"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:C.name||"Sem nome",data:JSON.stringify(C)})})).ok)throw Error("Erro ao salvar planilha");N.toast.success("Planilha salva com sucesso")}catch(e){eH.logger.error("SpreadsheetEditor: Erro ao salvar planilha",e,{workbookId:a,spreadsheetName:C.name,readOnly:o}),N.toast.error("Erro ao salvar planilha")}finally{I(!1)}},[a,C,l,o]),e5=(0,w.useCallback)(e=>{e.trim()&&eZ(e)},[eZ]),e9=(0,w.useMemo)(()=>ac(eR,B),[eR,B]),e8=(0,w.useCallback)(async(e,a,t)=>{if(!o&&"number"==typeof e&&"number"==typeof a&&!(e<0)&&!(a<0)&&(eb(C),S(r=>{if(!r||!Array.isArray(r.headers))return r;let o={...r};return o.rows=Array.isArray(r.rows)?[...r.rows]:[],Array.isArray(o.rows[e])?o.rows[e]=[...o.rows[e]]:o.rows[e]=Array(o.headers.length).fill(""),a<o.headers.length&&(o.rows[e][a]=t),o}),P({row:e,col:a}),eT))try{let r="".concat(String.fromCharCode(65+a)).concat(e+1);await eI("sheet1",r,t),await ek("sheet1",r)}catch(e){console.error("Erro ao enviar mudan\xe7a via Real-time:",e)}},[o,eb,C,P,eT,eI,ek]),e6=()=>{o||(eb(C),S(e=>{let a;let t=e.headers.length>0?e.headers[e.headers.length-1]:null;a=t&&/^[A-Z]$/.test(t)?String.fromCharCode((t.charCodeAt(0)||64)+1):"Coluna ".concat(e.headers.length+1);let r={...e};return r.headers=[...e.headers,a],r.rows=Array.isArray(e.rows)?e.rows.map(e=>Array.isArray(e)?[...e,""]:Array(r.headers.length).fill("")):[],r}),N.toast.success("Coluna adicionada"))},ar=(0,w.useCallback)(()=>{if(o)return;eb(C);let e=Array(C.headers.length).fill("");S(a=>{let t={...a};return t.rows=Array.isArray(a.rows)?[...a.rows,e]:[e],t}),N.toast.success("Linha adicionada")},[o,C,eb]),ao=e=>{if(o)return;eb(C);let a=[...C.rows];a.splice(e,1),S({...C,rows:a})},as=e=>{if(o)return;eb(C);let a=[...C.headers];a.splice(e,1);let t=C.rows.map(a=>{let t=[...a];return t.splice(e,1),t});S({...C,headers:a,rows:t})};(0,w.useCallback)(e=>{if(eE||o){N.toast.info("Aguarde",{description:"Espere o comando atual terminar antes de enviar outro.",duration:2e3});return}eZ(e),N.toast.success("Comando enviado",{description:'Executando: "'.concat(e,'"'),duration:2e3}),_&&L(!1)},[eE,o,eZ,_]),(0,w.useCallback)(()=>{L(!0)},[]),(0,w.useCallback)(e=>{if(k||eE){N.toast.info("Aguarde",{description:"Concluindo opera\xe7\xf5es atuais antes de navegar...",duration:2e3});return}j.length>1&&j[R]!==r?confirm("H\xe1 altera\xe7\xf5es n\xe3o salvas. Deseja sair mesmo assim?")&&(window.location.href=e):window.location.href=e},[j,R,r,k,eE]),(0,w.useCallback)(()=>{N.toast.info("Conectando com Excel Desktop",{description:"Iniciando conex\xe3o com o aplicativo Excel...",duration:3e3}),desktopBridge.connect().then(e=>{e?N.toast.success("Excel conectado",{description:"Sincroniza\xe7\xe3o de dados ativada entre o navegador e Excel desktop",duration:3e3}):N.toast.error("Falha na conex\xe3o",{description:"N\xe3o foi poss\xedvel conectar ao Excel. Verifique se o Excel Copilot Desktop est\xe1 instalado e em execu\xe7\xe3o.",duration:4e3})}).catch(e=>{console.error("Erro na conex\xe3o:",e),N.toast.error("Erro na conex\xe3o",{description:"Ocorreu um erro ao tentar conectar ao Excel.",duration:4e3})})},[desktopBridge]),(0,w.useEffect)(()=>{localStorage.getItem("excel_copilot_visited")||(ea(!0),Y(!0),localStorage.setItem("excel_copilot_visited","true"))},[]);let an=(0,w.useCallback)(()=>{J(!0),eg(()=>{J(!1)},3e3)},[eg]);(0,w.useEffect)(()=>{let e=et.current;return e&&(e.addEventListener("input",an),e.addEventListener("click",an)),()=>{e&&(e.removeEventListener("input",an),e.removeEventListener("click",an))}},[eg]),(0,w.useCallback)(()=>{K<au.length-1?$(K+1):Y(!1)},[K]),(0,w.useCallback)(()=>{Y(!1)},[]);let[ap,af]=(0,w.useState)(!1),[ah,ag]=(0,w.useState)(!1),ax=(0,w.useCallback)(()=>[{key:"s",description:"Salvar planilha",action:e2,modifiers:{ctrl:!0}},{key:"z",description:"Desfazer \xfaltima a\xe7\xe3o",action:e0,modifiers:{ctrl:!0}},{key:"y",description:"Refazer \xfaltima a\xe7\xe3o",action:e1,modifiers:{ctrl:!0}},{key:"+",description:"Adicionar linha",action:ar,modifiers:{ctrl:!0,shift:!0}},{key:"=",description:"Adicionar coluna",action:e6,modifiers:{ctrl:!0,shift:!0}},{key:"k",description:"Abrir paleta de comandos",action:()=>L(!0),modifiers:{ctrl:!0}},{key:"/",description:"Focar no chat assistente",action:()=>{var e;return null===(e=document.getElementById("chat-input"))||void 0===e?void 0:e.focus()},modifiers:{ctrl:!0}},{key:"f",description:"Alternar modo tela cheia",action:()=>ag(!ah),modifiers:{ctrl:!0,shift:!0}},{key:"Escape",description:"Fechar di\xe1logos/pain\xe9is abertos",action:()=>{L(!1),af(!1),ah&&ag(!1)}}],[e0,e1,ar,e6,ah]),av=(0,w.useCallback)(e=>{if("INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&!e.target.isContentEditable)for(let o of ax()){var a,t,r;if(e.key.toLowerCase()===o.key.toLowerCase()&&(!(null===(a=o.modifiers)||void 0===a?void 0:a.ctrl)||e.ctrlKey)&&(!(null===(t=o.modifiers)||void 0===t?void 0:t.alt)||e.altKey)&&(!(null===(r=o.modifiers)||void 0===r?void 0:r.shift)||e.shiftKey)){e.preventDefault(),o.action();return}}},[ax]);(0,w.useEffect)(()=>{let e=document.documentElement;ah?e.requestFullscreen&&e.requestFullscreen():document.fullscreenElement&&document.exitFullscreen&&document.exitFullscreen()},[ah]),(0,w.useEffect)(()=>(window.addEventListener("keydown",av),()=>{window.removeEventListener("keydown",av)}),[av]);let[ab,ay]=(0,w.useState)(!0),[aA,aE]=(0,w.useState)(""),aw=e=>{aE(e),ay(""===e.trim())};(0,w.useEffect)(()=>{if(c&&!eE){let e=setTimeout(()=>{eZ&&eZ(c)},1e3);return()=>clearTimeout(e)}},[c,eZ,eE]),(0,w.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/user/api-usage");if(e.ok){let a=await e.json();eY({used:a.apiCallsUsed,limit:a.apiCallsLimit}),"free"===a.plan&&a.apiCallsUsed/a.apiCallsLimit>=.8&&eQ(!0)}}catch(e){console.error("Erro ao buscar informa\xe7\xf5es de uso:",e)}};e(),eZ.length%5==0&&eZ.length>0&&e()},[eZ.length]);let aN=()=>{e$.push("/pricing")},aC=()=>{e$.push("/api/checkout/trial")},aS=(0,w.useMemo)(()=>ad.map((e,a)=>(0,s.jsx)(am,{command:e,onClick:()=>e5(e.text)},a)),[e5]),aj=(0,w.useRef)(null),aF=(0,w.useRef)(null),aO=(0,n.MG)({count:C.rows.length,getScrollElement:()=>aj.current,estimateSize:()=>36,overscan:10}),aR=(0,w.useMemo)(()=>(0,s.jsx)(al,{visibleRows:aO.getVirtualItems().length,totalRows:C.rows.length,virtualizer:aO}),[aO,C.rows.length]),aT=(0,w.useCallback)(async()=>{if(eh.current&&en){es(null);try{await eh.current.confirmAndExecute()&&(em(en),ed(!0),setTimeout(()=>{ec&&ed(!1)},5e3))}catch(e){console.error("Erro ao executar comando:",e),N.toast.error("Erro ao executar o comando")}finally{ei(null)}}},[en,eh]),ak=(0,w.useCallback)(()=>{eh.current&&(eh.current.cancelCommand(),es(null),ei(null))},[eh]),aI=(0,w.useCallback)(async e=>{try{await eM.M.storeFeedback(e),ed(!1),em(null)}catch(e){console.error("Erro ao enviar feedback:",e),N.toast.error("N\xe3o foi poss\xedvel enviar seu feedback")}},[]),a_=(0,w.useCallback)(()=>{ed(!1),em(null)},[]);return(0,s.jsxs)("div",{className:"flex flex-col h-full w-full relative",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center border-b p-2 bg-background/80 backdrop-blur-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(eO.MD,{variant:"ghost",size:"icon",onClick:e0,disabled:R<=0||o,title:"Desfazer (Ctrl+Z)",children:(0,s.jsx)(m.Z,{className:"h-4 w-4"})}),(0,s.jsx)(eO.MD,{variant:"ghost",size:"icon",onClick:e1,disabled:R>=j.length-1||o,title:"Refazer (Ctrl+Y)",children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"w-px h-4 bg-border mx-1"}),(0,s.jsx)(eO.MD,{variant:"ghost",size:"icon",onClick:e2,disabled:k||o,title:"Salvar (Ctrl+S)",children:k?(0,s.jsx)(f.Z,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(h.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"hidden md:flex gap-1 ml-2",children:[(0,s.jsx)(el,{workbookId:a,workbookName:C.name,sheets:[{name:C.name,data:C}]}),(0,s.jsx)(eL,{workbookId:a,saveToSupabase:!0,onUpload:e=>{e&&e.sheets&&e.sheets.length>0&&(eb(C),S(e.sheets[0].data))}})]})]}),(0,s.jsxs)("div",{className:"items-center gap-2 hidden md:flex",children:[(0,s.jsx)(ev,{workbookId:a,className:"mr-2"}),(0,s.jsxs)(F.Button,{variant:"ghost",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>q(!U),children:[U?(0,s.jsx)(g.Z,{className:"h-3 w-3"}):(0,s.jsx)(x.Z,{className:"h-3 w-3"}),U?"Mostrar":"Ocultar"," AI"]}),(0,s.jsxs)(F.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>L(!0),children:[(0,s.jsx)(v.Z,{className:"h-3 w-3"}),"Comandos"]}),(0,s.jsxs)(F.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>{},children:[(0,s.jsx)(b.Z,{className:"h-3 w-3"}),"Tela Cheia"]})]}),(0,s.jsx)("div",{className:"flex md:hidden",children:(0,s.jsxs)(F.Button,{variant:"outline",size:"sm",className:"h-8",onClick:()=>Z(!0),children:[(0,s.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"AI Chat"]})})]}),(0,s.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[aR,(0,s.jsx)("div",{ref:aj,className:"overflow-auto border border-border rounded-md",style:{height:"400px",width:"100%"},children:(0,s.jsxs)("div",{className:"table w-full relative",children:[(0,s.jsx)("div",{ref:aF,className:"table-header-group sticky top-0 bg-background z-10",children:(0,s.jsxs)("div",{className:"table-row",children:[(0,s.jsx)("div",{className:"table-cell w-10 text-center text-xs font-medium bg-muted",children:"#"}),C.headers.map((e,a)=>(0,s.jsxs)("div",{className:"table-cell p-2 font-medium bg-muted",children:[e,!o&&(0,s.jsx)(eO.Kk,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 ml-1",actionId:"column-".concat(a),onAction:()=>as(a),"aria-label":"Remover coluna ".concat(e),children:(0,s.jsx)(u.Z,{className:"h-3 w-3"})})]},a)),(0,s.jsx)("div",{className:"table-cell w-10 bg-muted"})]})}),(0,s.jsx)("div",{className:"table-row-group relative",style:{height:"".concat(aO.getTotalSize(),"px")},children:aO.getVirtualItems().map(e=>{let a=C.rows[e.index]||[];return(0,s.jsx)("div",{className:"table-row",style:{position:"absolute",top:0,left:0,width:"100%",height:"".concat(e.size,"px"),transform:"translateY(".concat(e.start,"px)")},children:(0,s.jsx)(ai,{rowIndex:e.index,rowData:a,headers:C.headers,modifiedCellsMap:e9,readOnly:o,onCellChange:e8,onRemoveRow:ao})},e.index)})})]})}),eR&&eR.length>0&&(0,s.jsx)("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg animate-in fade-in slide-in-from-bottom-5 duration-300",children:1===eR.length?"C\xe9lula atualizada":"".concat(eR.length," c\xe9lulas atualizadas")})]}),(0,s.jsx)("div",{className:"\n            h-full border-l overflow-hidden transition-all duration-300 ease-in-out\n            ".concat(U?"w-0 opacity-0":"w-80 opacity-100","\n            hidden md:block\n          "),"data-tutorial-target":"ai-assistant",children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"p-3 border-b flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"font-semibold text-sm flex items-center",children:[(0,s.jsx)(d.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),(0,s.jsx)(F.Button,{variant:"ghost",size:"icon",onClick:()=>q(!0),children:(0,s.jsx)(x.Z,{className:"h-4 w-4"})})]}),(0,s.jsx)(V.x,{className:"flex-1 p-3",children:0===eq.length?(0,s.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[(0,s.jsx)(d.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),(0,s.jsx)("h3",{className:"font-medium mb-1",children:"Excel Assistente"}),(0,s.jsx)("p",{className:"text-sm max-w-xs mx-auto",children:"Utilize linguagem natural para manipular sua planilha. Digite comandos como:"}),(0,s.jsx)("div",{className:"mt-4 space-y-2 text-xs",children:aS})]}):(0,s.jsx)("div",{className:"space-y-4",children:eq.map((e,a)=>(0,s.jsxs)("div",{className:"\n                        p-3 rounded-lg text-sm\n                        ".concat("user"===e.role?"bg-primary/10 border border-primary/20":"bg-muted","\n                      "),children:[(0,s.jsx)("div",{className:"text-xs font-medium mb-1 text-muted-foreground",children:"user"===e.role?"Voc\xea":"Excel Copilot"}),(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:e.content})]},a))})}),(0,s.jsx)("div",{className:"p-3 border-t",children:(0,s.jsx)(G,{onSendMessage:eZ,isLoading:eE,placeholder:"Digite um comando...",onChange:aw,showExamples:!1})})]})})]}),U&&!D&&(0,s.jsx)(eO.MD,{variant:"default",size:"sm",className:"fixed right-4 bottom-4 shadow-lg rounded-full h-10 w-10 p-0",onClick:()=>q(!1),children:(0,s.jsx)(d.Z,{className:"h-4 w-4"})}),D&&(0,s.jsxs)("div",{className:"\n      fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex flex-col\n      ".concat(z?"translate-y-0":"translate-y-full","\n      transition-transform duration-300 ease-in-out\n    "),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,s.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,s.jsx)(d.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),(0,s.jsx)(F.Button,{variant:"ghost",size:"icon",onClick:()=>Z(!1),children:(0,s.jsx)(u.Z,{className:"h-5 w-5"})})]}),(0,s.jsx)(V.x,{className:"flex-1 p-4",children:0===eq.length?(0,s.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[(0,s.jsx)(d.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Excel Copilot"}),(0,s.jsx)("p",{className:"text-sm max-w-sm mx-auto",children:"Envie comandos em linguagem natural para manipular sua planilha"})]}):(0,s.jsx)("div",{className:"space-y-4",children:eq.map((e,a)=>(0,s.jsx)("div",{className:"\n                  p-3 rounded-lg max-w-[85%]\n                  ".concat("user"===e.role?"bg-primary text-primary-foreground ml-auto":"bg-muted text-foreground border","\n                "),children:e.content},a))})}),(0,s.jsx)("div",{className:"p-4 border-t",children:(0,s.jsx)(G,{onSendMessage:eZ,isLoading:eE,placeholder:"Digite um comando...",showExamples:0===eq.length})})]}),(0,s.jsx)(ey,{open:eK,onOpenChange:eQ,children:(0,s.jsxs)(ew,{className:"max-w-md",children:[(0,s.jsxs)(eN,{children:[(0,s.jsxs)(eS,{className:"flex items-center gap-2",children:[(0,s.jsx)(A.Z,{className:"h-5 w-5 text-amber-500"}),"Voc\xea est\xe1 chegando ao limite"]}),(0,s.jsxs)(ej,{className:"text-base",children:["Voc\xea j\xe1 utilizou ",(null==eX?void 0:eX.used)||0," de ",(null==eX?void 0:eX.limit)||50," comandos dispon\xedveis no seu plano. Para continuar utilizando todos os recursos do Excel Copilot, escolha uma op\xe7\xe3o abaixo:"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 my-4",children:[(0,s.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>aC(),children:[(0,s.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,s.jsx)(d.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Experimente o Plano Pro Gr\xe1tis por 7 dias"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Acesso total a todos os recursos sem limita\xe7\xf5es durante 7 dias. Ser\xe1 necess\xe1rio informar um cart\xe3o, mas voc\xea pode cancelar a qualquer momento."})]}),(0,s.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>aN(),children:[(0,s.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,s.jsx)(i.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Fazer Upgrade para o Plano Pro"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"R$20/m\xeas ou R$200/ano. Acesso ilimitado a todos os recursos premium."})]})]}),(0,s.jsx)(eC,{children:(0,s.jsx)(eF,{children:"Continuar no Plano Free"})})]})}),eX&&eX.used/eX.limit>=.8&&eX.used/eX.limit<1&&(0,s.jsxs)("div",{className:"bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50 border px-4 py-2 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(A.Z,{className:"h-4 w-4 text-amber-500"}),(0,s.jsxs)("span",{className:"text-sm text-amber-800 dark:text-amber-300",children:["Voc\xea utilizou ",Math.round(eX.used/eX.limit*100),"% do seu limite mensal de comandos."]})]}),(0,s.jsx)(F.Button,{variant:"ghost",size:"sm",onClick:()=>eQ(!0),children:"Fazer Upgrade"})]}),eX&&eX.used>=eX.limit&&(0,s.jsxs)("div",{className:"bg-destructive/10 border-destructive/30 border px-4 py-2 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(A.Z,{className:"h-4 w-4 text-destructive"}),(0,s.jsx)("span",{className:"text-sm text-destructive",children:"Voc\xea atingiu 100% do seu limite mensal de comandos."})]}),(0,s.jsx)(F.Button,{variant:"outline",size:"sm",onClick:()=>eQ(!0),children:"Fazer Upgrade Agora"})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 p-4 border-t dark:border-gray-800",children:[(0,s.jsxs)("div",{className:"flex-1",children:[eo&&(0,s.jsx)(Q,{command:(null==en?void 0:en.command)||"",interpretation:eo,isLoading:eE,onExecute:aT,onCancel:ak}),ec&&eu&&(0,s.jsx)(X,{commandId:eu.id,command:eu.command,onDismiss:a_,onFeedbackSubmit:aI}),(0,s.jsx)(G,{onSendMessage:eZ,isLoading:eE,disabled:eE||o,autoFocus:!0,onChange:aw})]}),(0,s.jsx)("div",{className:"w-full lg:w-64 space-y-2"})]})]})}am.displayName="MemoizedQuickCommandButton"},49465:function(e,a,t){t.d(a,{M:function(){return n}});var r=t(18473),o=t(25566);class s{static getInstance(){return s.instance||(s.instance=new s),s.instance}async storeFeedback(e){let a={...e,timestamp:new Date().toISOString()};if(this.feedbackItems.unshift(a),this.feedbackItems.length>this.MAX_STORED_ITEMS&&(this.feedbackItems=this.feedbackItems.slice(0,this.MAX_STORED_ITEMS)),this.saveToStorage(),"true"===o.env.NEXT_PUBLIC_ENABLE_FEEDBACK_API)try{await fetch("/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}catch(e){r.logger.error("Erro ao enviar feedback para API:",e)}}getAnalytics(){let e=this.feedbackItems.length;if(0===e)return{totalCommands:0,successRate:0,commonIssues:[],commandPatterns:[]};let a=this.feedbackItems.filter(e=>e.successful).length,t=this.feedbackItems.filter(e=>!e.successful&&e.feedbackText),r=new Map;t.forEach(e=>{var a;let t=(null===(a=e.feedbackText)||void 0===a?void 0:a.toLowerCase())||"",o=!1;for(let e of[{word:"entend",issue:"N\xe3o entendeu o comando"},{word:"error",issue:"Erro na execu\xe7\xe3o"},{word:"lent",issue:"Performance lenta"},{word:"format",issue:"Problemas de formata\xe7\xe3o"},{word:"gr\xe1fico",issue:"Problemas com gr\xe1ficos"},{word:"tabela",issue:"Problemas com tabelas"},{word:"f\xf3rmula",issue:"Problemas com f\xf3rmulas"}])t.includes(e.word)&&(r.set(e.issue,(r.get(e.issue)||0)+1),o=!0);!o&&t.length>0&&r.set("Outros problemas",(r.get("Outros problemas")||0)+1)});let o=Array.from(r.entries()).map(e=>{let[a,t]=e;return{issue:a,count:t}}).sort((e,a)=>a.count-e.count),s=new Map;return this.feedbackItems.forEach(e=>{let a=e.command.toLowerCase();for(let t of[{words:["cri","tabela"],pattern:"Criar tabela"},{words:["gr\xe1fico","chart"],pattern:"Criar gr\xe1fico"},{words:["calcul","m\xe9dia","soma"],pattern:"C\xe1lculos"},{words:["format","cor","estilo"],pattern:"Formata\xe7\xe3o"},{words:["filtr","ordem"],pattern:"Filtro/Ordena\xe7\xe3o"}])if(t.words.some(e=>a.includes(e))){let a=s.get(t.pattern)||{success:0,total:0};s.set(t.pattern,{success:a.success+(e.successful?1:0),total:a.total+1});break}}),{totalCommands:e,successRate:a/e*100,commonIssues:o,commandPatterns:Array.from(s.entries()).map(e=>{let[a,t]=e;return{pattern:a,successRate:t.success/t.total*100,count:t.total}}).sort((e,a)=>a.count-e.count)}}saveToStorage(){if(this.feedbackStorage)try{this.feedbackStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.feedbackItems))}catch(e){r.logger.error("Erro ao salvar feedback no localStorage:",e)}}loadFromStorage(){if(this.feedbackStorage)try{let e=this.feedbackStorage.getItem(this.STORAGE_KEY);e&&(this.feedbackItems=JSON.parse(e))}catch(e){r.logger.error("Erro ao carregar feedback do localStorage:",e),this.feedbackItems=[]}}constructor(){this.feedbackItems=[],this.feedbackStorage=null,this.STORAGE_KEY="excel_copilot_feedback",this.MAX_STORED_ITEMS=100,this.feedbackStorage=window.localStorage,this.loadFromStorage()}}let n=s.getInstance()},80585:function(e,a,t){var r,o;t.r(a),t.d(a,{DEFAULT_EXCEL_SYSTEM_PROMPT:function(){return n},ExcelAIProcessor:function(){return s},GeminiErrorType:function(){return r},createExcelAIProcessor:function(){return l},getGeminiServiceAPI:function(){return i}}),t(18473);class s{async processQuery(e){try{let a=this.preprocessQuery(e),t=this.buildPrompt(a),r=await i(),o=await r.sendMessage(t,{context:JSON.stringify(this.context),useMock:!this.useRealAI});return this.parseAIResponse(o)}catch(e){return console.error("Erro ao processar query:",e),{operations:[],error:"Erro ao processar: ".concat(e instanceof Error?e.message:String(e)),success:!1,message:"Falha ao processar query com IA"}}}preprocessQuery(e){return e.replace(/\bform\./g,"f\xf3rmula").replace(/\bcol\./g,"coluna").replace(/\btab\./g,"tabela").replace(/\bgraf\./g,"gr\xe1fico").replace(/\bcel\./g,"c\xe9lula").replace(/\bfunc\./g,"fun\xe7\xe3o").replace(/\bop\./g,"opera\xe7\xe3o").replace(/\bval\./g,"valor").replace(/\bmed\./g,"m\xe9dia")}buildPrompt(e){var a;return'\n    Analise o seguinte comando para Excel e retorne as opera\xe7\xf5es necess\xe1rias em formato JSON:\n    \n    Comando: "'.concat(e,'"\n    \n    Contexto da planilha:\n    - Planilha ativa: ').concat(this.context.activeSheet,"\n    - Sele\xe7\xe3o atual: ").concat(this.context.selection,"\n    - Cabe\xe7alhos: ").concat((null===(a=this.context.headers)||void 0===a?void 0:a.join(", "))||"N/A",'\n    \n    Retorne APENAS um objeto JSON com a seguinte estrutura:\n    {\n      "operations": [\n        {\n          "type": "TIPO_OPERACAO", // FORMULA, CHART, TABLE, FORMAT, etc.\n          "data": { ... }, // Dados espec\xedficos da opera\xe7\xe3o\n          "description": "Descri\xe7\xe3o" // Opcional, descri\xe7\xe3o da opera\xe7\xe3o\n        }\n      ],\n      "explanation": "Explica\xe7\xe3o do que foi feito" // Opcional\n    }\n    ')}parseAIResponse(e){try{let a=e.match(/\{[\s\S]*\}/);if(a){let e=a[0],t=JSON.parse(e);if(!t.operations||!Array.isArray(t.operations))throw Error("Formato de resposta inv\xe1lido: operations n\xe3o \xe9 um array");return t}return{operations:[{type:"TABLE",data:{rawResponse:e},description:"Resposta em texto: ".concat(e.substring(0,100),"...")}],explanation:"A resposta n\xe3o p\xf4de ser processada como JSON",success:!0,message:"Processamento parcial realizado"}}catch(a){return console.error("Erro ao analisar resposta da IA:",a),{operations:[{type:"TABLE",data:{error:!0},description:'Processando: "'.concat(e.substring(0,100),'..."')}],explanation:"Erro ao processar JSON da resposta",error:String(a),success:!1,message:"Falha ao analisar resposta da IA"}}}constructor(e={},a=!0){this.context={activeSheet:e.activeSheet||"Sheet1",headers:e.headers||[],selection:e.selection||"A1",recentOperations:e.recentOperations||[]},this.useRealAI=a}}let n='\nVoc\xea \xe9 um assistente especializado em Excel, capaz de ajudar a realizar opera\xe7\xf5es com planilhas.\nSua fun\xe7\xe3o \xe9 interpretar comandos em linguagem natural e convert\xea-los em opera\xe7\xf5es Excel espec\xedficas.\n\n# DIRETRIZES IMPORTANTES\n1. Sempre responda de forma estruturada usando o formato JSON abaixo\n2. Para cada comando, identifique as opera\xe7\xf5es necess\xe1rias e forne\xe7a par\xe2metros precisos\n3. Em caso de ambiguidade, escolha a interpreta\xe7\xe3o mais prov\xe1vel baseada no contexto\n4. Se n\xe3o conseguir interpretar o comando, forne\xe7a uma resposta de erro amig\xe1vel\n5. NUNCA invente dados ou colunas que n\xe3o existam no contexto atual\n\n# FORMATO DE RESPOSTA\n{\n  "operations": [\n    {\n      "type": "TIPO_DA_OPERA\xc7\xc3O",\n      "data": { ... par\xe2metros espec\xedficos da opera\xe7\xe3o ... }\n    }\n  ],\n  "explanation": "Breve explica\xe7\xe3o do que ser\xe1 feito",\n  "interpretation": "Como voc\xea entendeu o comando do usu\xe1rio"\n}\n\n# TIPOS DE OPERA\xc7\xd5ES DISPON\xcdVEIS\n\n## F\xd3RMULAS\n- FORMULA: Aplicar f\xf3rmulas em c\xe9lulas\n  {\n    "type": "FORMULA",\n    "data": {\n      "formula": "=SOMA(A1:A10)", \n      "range": "B1" | ["B1", "B2"] | "B1:B10"\n    }\n  }\n\n## DADOS\n- FILTER: Filtrar dados\n  {\n    "type": "FILTER",\n    "data": {\n      "column": "A" | 1,\n      "condition": ">" | "<" | "=" | "contains" | "between",\n      "value": 100 | "texto" | [10, 20]\n    }\n  }\n- SORT: Ordenar dados\n  {\n    "type": "SORT",\n    "data": {\n      "column": "A" | 1,\n      "direction": "asc" | "desc"\n    }\n  }\n\n## VISUALIZA\xc7\xd5ES\n- CHART: Criar ou modificar gr\xe1ficos\n  {\n    "type": "CHART",\n    "data": {\n      "type": "bar" | "line" | "pie" | "scatter" | "area",\n      "title": "T\xedtulo do gr\xe1fico",\n      "labels": "A1:A10", // Eixo X ou categorias\n      "datasets": ["B1:B10", "C1:C10"], // S\xe9ries de dados\n      "options": { ... op\xe7\xf5es adicionais ... }\n    }\n  }\n\n## FORMATA\xc7\xc3O\n- CONDITIONAL_FORMAT: Formata\xe7\xe3o condicional\n  {\n    "type": "CONDITIONAL_FORMAT",\n    "data": {\n      "range": "A1:B10",\n      "rule": "greater" | "less" | "equal" | "between" | "text" | "date",\n      "value": 100 | [10, 20] | "texto",\n      "format": {\n        "background": "#F5F5F5",\n        "textColor": "#FF0000",\n        "bold": true | false,\n        "italic": true | false\n      }\n    }\n  }\n\n## TABELAS\n- PIVOT_TABLE: Criar tabelas din\xe2micas\n  {\n    "type": "PIVOT_TABLE",\n    "data": {\n      "source": "A1:D10",\n      "rows": ["A"], // Campos para linhas\n      "columns": ["B"], // Campos para colunas\n      "values": [{ "field": "C", "function": "sum" }], // Campos para valores\n      "filters": [{ "field": "D", "value": "X" }] // Filtros opcionais\n    }\n  }\n\n## C\xc9LULAS\n- CELL_UPDATE: Atualizar c\xe9lulas individuais\n  {\n    "type": "CELL_UPDATE",\n    "data": {\n      "updates": [\n        { "cell": "A1", "value": 100 },\n        { "cell": "B1", "value": "Texto" }\n      ]\n    }\n  }\n\n## AN\xc1LISE\n- DATA_ANALYSIS: An\xe1lise estat\xedstica\n  {\n    "type": "DATA_ANALYSIS",\n    "data": {\n      "type": "statistics" | "correlation" | "regression",\n      "range": "A1:B10",\n      "options": { ... op\xe7\xf5es espec\xedficas ... }\n    }\n  }\n\n# EXEMPLOS DE COMANDOS E RESPOSTAS\n\n## Exemplo 1: "Calcule a m\xe9dia da coluna B"\n{\n  "operations": [\n    {\n      "type": "FORMULA",\n      "data": {\n        "formula": "=M\xc9DIA(B:B)",\n        "range": "C1"\n      }\n    }\n  ],\n  "explanation": "Calculando a m\xe9dia da coluna B e colocando o resultado na c\xe9lula C1",\n  "interpretation": "Voc\xea deseja calcular a m\xe9dia de todos os valores num\xe9ricos na coluna B"\n}\n\n## Exemplo 2: "Crie uma tabela de vendas por m\xeas"\n{\n  "operations": [\n    {\n      "type": "CELL_UPDATE",\n      "data": {\n        "updates": [\n          { "cell": "A1", "value": "M\xeas" },\n          { "cell": "B1", "value": "Vendas" },\n          { "cell": "A2", "value": "Janeiro" },\n          { "cell": "A3", "value": "Fevereiro" },\n          { "cell": "A4", "value": "Mar\xe7o" },\n          { "cell": "B2", "value": 0 },\n          { "cell": "B3", "value": 0 },\n          { "cell": "B4", "value": 0 }\n        ]\n      }\n    }\n  ],\n  "explanation": "Criando uma tabela de vendas por m\xeas com layout b\xe1sico",\n  "interpretation": "Voc\xea deseja criar uma nova tabela para registrar vendas mensais"\n}\n\n## Exemplo 3: "Gere um gr\xe1fico de barras com os dados da coluna A e B"\n{\n  "operations": [\n    {\n      "type": "CHART",\n      "data": {\n        "type": "bar",\n        "title": "Gr\xe1fico de Barras A vs B",\n        "labels": "A1:A10",\n        "datasets": ["B1:B10"],\n        "options": {\n          "legend": true,\n          "horizontalBar": false\n        }\n      }\n    }\n  ],\n  "explanation": "Criando um gr\xe1fico de barras usando dados das colunas A e B",\n  "interpretation": "Voc\xea deseja visualizar os dados das colunas A e B em um gr\xe1fico de barras"\n}\n\n# CONTEXTO ATUAL DA PLANILHA\n{contextInfo}\n';function l(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new s({},e)}async function i(){return{async sendMessage(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,...a})});if(!t.ok)throw Error("API Error: ".concat(t.statusText));return(await t.json()).response}}}(o=r||(r={})).NETWORK_ERROR="NETWORK_ERROR",o.API_ERROR="API_ERROR",o.RATE_LIMIT="RATE_LIMIT",o.INVALID_REQUEST="INVALID_REQUEST",o.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",o.UNKNOWN="UNKNOWN",o.API_UNAVAILABLE="API_UNAVAILABLE"},15589:function(e,a,t){t.d(a,{OK:function(){return o},z6:function(){return r}});let r={radius:{sm:"rounded",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"},width:{none:"border-0",thin:"border",medium:"border-2",thick:"border-4"},card:"border rounded-lg",input:"border rounded-md",button:"rounded-md"},o={transition:{fast:"transition-all duration-150 ease-in-out",medium:"transition-all duration-300 ease-in-out",slow:"transition-all duration-500 ease-in-out"},hover:{scale:"hover:scale-105",brightness:"hover:brightness-110",opacity:"hover:opacity-90"},spin:"animate-spin",pulse:"animate-pulse",loading:"animate-pulse",shake:"animate-[shake_0.82s_cubic-bezier(.36,.07,.19,.97)_both]"},s="bg-primary text-primary-foreground",n="hover:bg-primary/90",l="bg-primary/10 text-primary",i="bg-secondary text-secondary-foreground",c="hover:bg-secondary/90",d="bg-destructive text-destructive-foreground",u="hover:bg-destructive/90",m="bg-destructive/10 text-destructive";"".concat(r.button," font-medium ").concat(o.transition.fast),"".concat(s," ").concat(n),"".concat(i," ").concat(c),"".concat(d," ").concat(u),"".concat(r.input," ").concat("px-4 py-2"," w-full ").concat("focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"),"".concat(m," ").concat(r.radius.md," ").concat("p-2"),"".concat("bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"," ").concat(r.radius.md," ").concat("p-2"),"".concat("bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"," ").concat(r.radius.md," ").concat("p-2"),"".concat(l," ").concat(r.radius.md," ").concat("p-2"),"".concat("p-2"," ").concat(r.radius.md," ").concat(o.transition.fast," hover:bg-accent hover:text-accent-foreground"),"".concat("p-2"," ").concat(r.radius.md," bg-accent text-accent-foreground")},58743:function(e,a,t){t.d(a,{Nt:function(){return l},Xf:function(){return s},tt:function(){return i}}),t(13537);var r=t(62848),o=t(25566);let s={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};s.FREE,s.PRO_MONTHLY,s.PRO_ANNUAL;let n=o.env.STRIPE_SECRET_KEY||"";function l(e){switch(e){case s.FREE:return"Gr\xe1tis";case s.PRO_MONTHLY:return"Pro Mensal";case s.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}o.env.STRIPE_WEBHOOK_SECRET,n&&new r.Z(n,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}});let i=()=>"pk_test_51RGJ6nRrKLXtzZkM0EqlkOaZIpDNCLiGwLAHXr1YOmHWGIZB5RxMImDy2bBVyErg7PiQ2T6vqS1pFSK6O4nSEdQJ00Tz72hUX7"},56143:function(e,a,t){t.d(a,{H:function(){return s},KC:function(){return o},KE:function(){return n}});var r=t(18473);function o(e){if(e instanceof Error)return e;if(null!=e){if("string"==typeof e)return Error(e);try{let a=JSON.stringify(e);return Error(a)}catch(e){return Error("Unknown error")}}}function s(e,a,t){let s=o(a);r.logger.error(e,s)}function n(e,a,t){let o=null==a?void 0:"object"==typeof a?a:{value:a},s=t?{...t,...o}:o;r.logger.warn(e,s)}},64451:function(e,a,t){var r,o,s,n,l,i,c,d,u,m;t.d(a,{ox:function(){return r}}),(i=r||(r={})).FORMULA="FORMULA",i.FILTER="FILTER",i.SORT="SORT",i.FORMAT="FORMAT",i.CHART="CHART",i.CELL_UPDATE="CELL_UPDATE",i.COLUMN_OPERATION="COLUMN_OPERATION",i.ROW_OPERATION="ROW_OPERATION",i.TABLE="TABLE",i.DATA_TRANSFORMATION="DATA_TRANSFORMATION",i.PIVOT_TABLE="PIVOT_TABLE",i.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",i.ADVANCED_CHART="ADVANCED_CHART",i.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",i.RANGE_UPDATE="RANGE_UPDATE",i.CELL_MERGE="CELL_MERGE",i.CELL_SPLIT="CELL_SPLIT",i.NAMED_RANGE="NAMED_RANGE",i.VALIDATION="VALIDATION",i.FREEZE_PANES="FREEZE_PANES",i.SHEET_OPERATION="SHEET_OPERATION",i.ANALYSIS="ANALYSIS",i.GENERIC="GENERIC",(c=o||(o={})).LINE="LINE",c.BAR="BAR",c.COLUMN="COLUMN",c.AREA="AREA",c.SCATTER="SCATTER",c.PIE="PIE",(d=s||(s={})).EQUALS="equals",d.NOT_EQUALS="notEquals",d.GREATER_THAN="greaterThan",d.LESS_THAN="lessThan",d.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",d.LESS_THAN_OR_EQUAL="lessThanOrEqual",d.CONTAINS="contains",d.NOT_CONTAINS="notContains",d.BEGINS_WITH="beginsWith",d.ENDS_WITH="endsWith",d.BETWEEN="between",(u=n||(n={})).DISCONNECTED="disconnected",u.CONNECTING="connecting",u.CONNECTED="connected",u.ERROR="error",(m=l||(l={})).FORMULA_ERROR="FORMULA_ERROR",m.REFERENCE_ERROR="REFERENCE_ERROR",m.VALUE_ERROR="VALUE_ERROR",m.NAME_ERROR="NAME_ERROR",m.RANGE_ERROR="RANGE_ERROR",m.SYNTAX_ERROR="SYNTAX_ERROR",m.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",m.FORMAT_ERROR="FORMAT_ERROR",m.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",m.UNKNOWN_ERROR="UNKNOWN_ERROR"}}]);