"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3526],{78149:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},38620:function(e,t,n){n.d(t,{B:function(){return c}});var r=n(2265),o=n(98324),u=n(1584),i=n(71538),l=n(57437);function c(e){let t=e+"CollectionProvider",[n,c]=(0,o.b)(t),[a,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,l.jsx)(a,{scope:t,itemMap:u,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",m=(0,i.Z8)(d),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),i=(0,u.e)(t,o.collectionRef);return(0,l.jsx)(m,{ref:i,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",w="data-radix-collection-item",b=(0,i.Z8)(v),g=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,c=r.useRef(null),a=(0,u.e)(t,c),f=s(v,n);return r.useEffect(()=>(f.itemMap.set(c,{ref:c,...i}),()=>void f.itemMap.delete(c))),(0,l.jsx)(b,{[w]:"",ref:a,children:o})});return g.displayName=v,[{Provider:f,Slot:p,ItemSlot:g},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(w,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},c]}},98324:function(e,t,n){n.d(t,{b:function(){return i},k:function(){return u}});var r=n(2265),o=n(57437);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),l=n.length;n=[...n,u];let c=t=>{let{scope:n,children:u,...c}=t,a=n?.[e]?.[l]||i,s=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(a.Provider,{value:s,children:u})};return c.displayName=t+"Provider",[c,function(n,o){let c=o?.[e]?.[l]||i,a=r.useContext(c);if(a)return a;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},87513:function(e,t,n){n.d(t,{gm:function(){return u}});var r=n(2265);n(57437);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},53201:function(e,t,n){n.d(t,{M:function(){return c}});var r,o=n(2265),u=n(1336),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function c(e){let[t,n]=o.useState(i());return(0,u.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},31383:function(e,t,n){n.d(t,{z:function(){return i}});var r=n(2265),o=n(1584),u=n(1336),i=e=>{var t,n;let i,c;let{present:a,children:s}=e,f=function(e){var t,n;let[o,i]=r.useState(),c=r.useRef(null),a=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(c.current);s.current="mounted"===f?e:"none"},[f]),(0,u.b)(()=>{let t=c.current,n=a.current;if(n!==e){let r=s.current,o=l(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,u.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(c.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=l(c.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{c.current=e?getComputedStyle(e):null,i(e)},[])}}(a),d="function"==typeof s?s({present:f.isPresent}):r.Children.only(s),m=(0,o.e)(f.ref,(i=null===(t=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?d.ref:(i=null===(n=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof s||f.isPresent?r.cloneElement(d,{ref:m}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},25171:function(e,t,n){n.d(t,{WV:function(){return l},jH:function(){return c}});var r=n(2265),o=n(54887),u=n(71538),i=n(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},53398:function(e,t,n){n.d(t,{Pc:function(){return M},ck:function(){return O},fC:function(){return I}});var r=n(2265),o=n(78149),u=n(38620),i=n(1584),l=n(98324),c=n(53201),a=n(25171),s=n(75137),f=n(91715),d=n(87513),m=n(57437),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[b,g,y]=(0,u.B)(w),[N,M]=(0,l.b)(w,[y]),[h,R]=N(w),E=r.forwardRef((e,t)=>(0,m.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(x,{...e,ref:t})})}));E.displayName=w;var x=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:u,loop:l=!1,dir:c,currentTabStopId:b,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:N,onEntryFocus:M,preventScrollOnEntryFocus:R=!1,...E}=e,x=r.useRef(null),T=(0,i.e)(t,x),S=(0,d.gm)(c),[A,I]=(0,f.T)({prop:b,defaultProp:null!=y?y:null,onChange:N,caller:w}),[O,P]=r.useState(!1),_=(0,s.W)(M),D=g(n),F=r.useRef(!1),[j,k]=r.useState(0);return r.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(p,_),()=>e.removeEventListener(p,_)},[_]),(0,m.jsx)(h,{scope:n,orientation:u,dir:S,loop:l,currentTabStopId:A,onItemFocus:r.useCallback(e=>I(e),[I]),onItemShiftTab:r.useCallback(()=>P(!0),[]),onFocusableItemAdd:r.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>k(e=>e-1),[]),children:(0,m.jsx)(a.WV.div,{tabIndex:O||0===j?-1:0,"data-orientation":u,...E,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!O){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);C([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),R)}}F.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>P(!1))})})}),T="RovingFocusGroupItem",S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:u=!0,active:i=!1,tabStopId:l,children:s,...f}=e,d=(0,c.M)(),p=l||d,v=R(T,n),w=v.currentTabStopId===p,y=g(n),{onFocusableItemAdd:N,onFocusableItemRemove:M,currentTabStopId:h}=v;return r.useEffect(()=>{if(u)return N(),()=>M()},[u,N,M]),(0,m.jsx)(b.ItemSlot,{scope:n,id:p,focusable:u,active:i,children:(0,m.jsx)(a.WV.span,{tabIndex:w?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let u=o.indexOf(e.currentTarget);o=v.loop?(n=o,r=u+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(u+1)}setTimeout(()=>C(o))}}),children:"function"==typeof s?s({isCurrentTabStop:w,hasTabStop:null!=h}):s})})});S.displayName=T;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=E,O=S},75137:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},91715:function(e,t,n){n.d(t,{T:function(){return l}});var r,o=n(2265),u=n(1336),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,l,c]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==n&&(l.current?.(n),u.current=n)},[n,u]),[n,r,l]}({defaultProp:t,onChange:n}),a=void 0!==e,s=a?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[s,o.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&c.current?.(n)}else l(t)},[a,e,l,c])]}Symbol("RADIX:SYNC_STATE")},1336:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);