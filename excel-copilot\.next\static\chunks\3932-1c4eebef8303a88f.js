"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3932],{71976:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},77424:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},34567:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},74109:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},33907:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},92222:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},59738:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2286:function(e,t,r){r.d(t,{Ns:function(){return q},fC:function(){return U},gb:function(){return C},q4:function(){return D},l_:function(){return F}});var n=r(2265),o=r(25171),l=r(31383),i=r(98324),a=r(1584),s=r(75137),c=r(87513),u=r(1336),d=r(78149),p=r(57437),f="ScrollArea",[h,v]=(0,i.b)(f),[w,y]=h(f),g=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:i,scrollHideDelay:s=600,...u}=e,[d,f]=n.useState(null),[h,v]=n.useState(null),[y,g]=n.useState(null),[b,x]=n.useState(null),[m,C]=n.useState(null),[T,E]=n.useState(0),[S,R]=n.useState(0),[k,L]=n.useState(!1),[P,j]=n.useState(!1),M=(0,a.e)(t,e=>f(e)),_=(0,c.gm)(i);return(0,p.jsx)(w,{scope:r,type:l,dir:_,scrollHideDelay:s,scrollArea:d,viewport:h,onViewportChange:v,content:y,onContentChange:g,scrollbarX:b,onScrollbarXChange:x,scrollbarXEnabled:k,onScrollbarXEnabledChange:L,scrollbarY:m,onScrollbarYChange:C,scrollbarYEnabled:P,onScrollbarYEnabledChange:j,onCornerWidthChange:E,onCornerHeightChange:R,children:(0,p.jsx)(o.WV.div,{dir:_,...u,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":T+"px","--radix-scroll-area-corner-height":S+"px",...e.style}})})});g.displayName=f;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:i,...s}=e,c=y(b,r),u=n.useRef(null),d=(0,a.e)(t,u,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,p.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});x.displayName=b;var m="ScrollAreaScrollbar",C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(m,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,p.jsx)(T,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,p.jsx)(E,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,p.jsx)(S,{...o,ref:t,forceMount:r}):"always"===l.type?(0,p.jsx)(R,{...o,ref:t}):null});C.displayName=m;var T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=y(m,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,p.jsx)(l.z,{present:r||a,children:(0,p.jsx)(S,{"data-state":a?"visible":"hidden",...o,ref:t})})}),E=n.forwardRef((e,t)=>{var r,o;let{forceMount:i,...a}=e,s=y(m,e.__scopeScrollArea),c="horizontal"===e.orientation,u=Z(()=>h("SCROLL_END"),100),[f,h]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=o[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===f){let e=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[f,s.scrollHideDelay,h]),n.useEffect(()=>{let e=s.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(h("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,c,h,u]),(0,p.jsx)(l.z,{present:i||"hidden"!==f,children:(0,p.jsx)(R,{"data-state":"hidden"===f?"hidden":"visible",...a,ref:t,onPointerEnter:(0,d.M)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,d.M)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),S=n.forwardRef((e,t)=>{let r=y(m,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,s]=n.useState(!1),c="horizontal"===e.orientation,u=Z(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(c?e:t)}},10);return B(r.viewport,u),B(r.content,u),(0,p.jsx)(l.z,{present:o||a,children:(0,p.jsx)(R,{"data-state":a?"visible":"hidden",...i,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=y(m,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=H(s.viewport,s.content),d={...o,sizes:s,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=I(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return V([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,p.jsx)(k,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===r?(0,p.jsx)(L,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),k=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=y(m,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,a.e)(t,u,i.onScrollbarXChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(M,{"data-orientation":"horizontal",...l,ref:d,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":I(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:O(s.paddingLeft),paddingEnd:O(s.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=y(m,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,a.e)(t,u,i.onScrollbarYChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(M,{"data-orientation":"vertical",...l,ref:d,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":I(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:O(s.paddingTop),paddingEnd:O(s.paddingBottom)}})}})}),[P,j]=h(m),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:i,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:f,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:g,...b}=e,x=y(m,r),[C,T]=n.useState(null),E=(0,a.e)(t,e=>T(e)),S=n.useRef(null),R=n.useRef(""),k=x.viewport,L=l.content-l.viewport,j=(0,s.W)(w),M=(0,s.W)(h),_=Z(g,10);function D(e){S.current&&v({x:e.clientX-S.current.left,y:e.clientY-S.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==C?void 0:C.contains(t))&&j(e,L)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[k,C,L,j]),n.useEffect(M,[l,M]),B(C,_),B(x.content,_),(0,p.jsx)(P,{scope:r,scrollbar:C,hasThumb:i,onThumbChange:(0,s.W)(c),onThumbPointerUp:(0,s.W)(u),onThumbPositionChange:M,onThumbPointerDown:(0,s.W)(f),children:(0,p.jsx)(o.WV.div,{...b,ref:E,style:{position:"absolute",...b.style},onPointerDown:(0,d.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),S.current=C.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:(0,d.M)(e.onPointerMove,D),onPointerUp:(0,d.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,x.viewport&&(x.viewport.style.scrollBehavior=""),S.current=null})})})}),_="ScrollAreaThumb",D=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=j(_,e.__scopeScrollArea);return(0,p.jsx)(l.z,{present:r||o.hasThumb,children:(0,p.jsx)(z,{ref:t,...n})})}),z=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...i}=e,s=y(_,r),c=j(_,r),{onThumbPositionChange:u}=c,f=(0,a.e)(t,e=>c.onThumbChange(e)),h=n.useRef(void 0),v=Z(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(v(),!h.current){let t=Y(e,u);h.current=t,u()}};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,u]),(0,p.jsx)(o.WV.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,d.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,d.M)(e.onPointerUp,c.onThumbPointerUp)})});D.displayName=_;var W="ScrollAreaCorner",A=n.forwardRef((e,t)=>{let r=y(W,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(N,{...e,ref:t}):null});A.displayName=W;var N=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=y(W,r),[a,s]=n.useState(0),[c,u]=n.useState(0),d=!!(a&&c);return B(i.scrollbarX,()=>{var e;let t=(null===(e=i.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),u(t)}),B(i.scrollbarY,()=>{var e;let t=(null===(e=i.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),d?(0,p.jsx)(o.WV.div,{...l,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function O(e){return e?parseInt(e,10):0}function H(e,t){let r=e/t;return isNaN(r)?0:r}function I(e){let t=H(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function X(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=I(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,i]:[-1*i,0]);return V([0,i],[0,l-n])(a)}function V(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var Y=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function Z(e,t){let r=(0,s.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function B(e,t){let r=(0,s.W)(t);(0,u.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var U=g,F=x,q=A},27071:function(e,t,r){r.d(t,{VY:function(){return Y},fC:function(){return X},xz:function(){return V},zt:function(){return I}});var n=r(2265),o=r(78149),l=r(1584),i=r(98324),a=r(53938),s=r(53201),c=r(77683),u=(r(56935),r(31383)),d=r(25171),p=r(71538),f=r(91715),h=r(31725),v=r(57437),[w,y]=(0,i.b)("Tooltip",[c.D7]),g=(0,c.D7)(),b="TooltipProvider",x="tooltip.open",[m,C]=w(b),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),c=n.useRef(0);return n.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(m,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(c.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};T.displayName=b;var E="Tooltip",[S,R]=w(E),k=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:u}=e,d=C(E,e.__scopeTooltip),p=g(t),[h,w]=n.useState(null),y=(0,s.M)(),b=n.useRef(0),m=null!=a?a:d.disableHoverableContent,T=null!=u?u:d.delayDuration,R=n.useRef(!1),[k,L]=(0,f.T)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(x))):d.onClose(),null==i||i(e)},caller:E}),P=n.useMemo(()=>k?R.current?"delayed-open":"instant-open":"closed",[k]),j=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,R.current=!1,L(!0)},[L]),M=n.useCallback(()=>{window.clearTimeout(b.current),b.current=0,L(!1)},[L]),_=n.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{R.current=!0,L(!0),b.current=0},T)},[T,L]);return n.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),(0,v.jsx)(c.fC,{...p,children:(0,v.jsx)(S,{scope:t,contentId:y,open:k,stateAttribute:P,trigger:h,onTriggerChange:w,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?_():j()},[d.isOpenDelayedRef,_,j]),onTriggerLeave:n.useCallback(()=>{m?M():(window.clearTimeout(b.current),b.current=0)},[M,m]),onOpen:j,onClose:M,disableHoverableContent:m,children:r})})};k.displayName=E;var L="TooltipTrigger",P=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=R(L,r),s=C(L,r),u=g(r),p=n.useRef(null),f=(0,l.e)(t,p,a.onTriggerChange),h=n.useRef(!1),w=n.useRef(!1),y=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,v.jsx)(c.ee,{asChild:!0,...u,children:(0,v.jsx)(d.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.M)(e.onPointerMove,e=>{"touch"===e.pointerType||w.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),w.current=!0)}),onPointerLeave:(0,o.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),w.current=!1}),onPointerDown:(0,o.M)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.M)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.M)(e.onBlur,a.onClose),onClick:(0,o.M)(e.onClick,a.onClose)})})});P.displayName=L;var[j,M]=w("TooltipPortal",{forceMount:void 0}),_="TooltipContent",D=n.forwardRef((e,t)=>{let r=M(_,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=R(_,e.__scopeTooltip);return(0,v.jsx)(u.z,{present:n||i.open,children:i.disableHoverableContent?(0,v.jsx)(O,{side:o,...l,ref:t}):(0,v.jsx)(z,{side:o,...l,ref:t})})}),z=n.forwardRef((e,t)=>{let r=R(_,e.__scopeTooltip),o=C(_,e.__scopeTooltip),i=n.useRef(null),a=(0,l.e)(t,i),[s,c]=n.useState(null),{trigger:u,onClose:d}=r,p=i.current,{onPointerInTransitChange:f}=o,h=n.useCallback(()=>{c(null),f(!1)},[f]),w=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(u&&p){let e=e=>w(e,p),t=e=>w(e,u);return u.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[u,p,w,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==u?void 0:u.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,c=i.y,u=a.x,d=a.y;c>n!=d>n&&r<(u-s)*(n-c)/(d-c)+s&&(o=!o)}return o}(r,s);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,p,s,d,h]),(0,v.jsx)(O,{...e,ref:a})}),[W,A]=w(E,{isInside:!1}),N=(0,p.sA)("TooltipContent"),O=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...u}=e,d=R(_,r),p=g(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(x,f),()=>document.removeEventListener(x,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,v.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,v.jsxs)(c.VY,{"data-state":d.stateAttribute,...p,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(N,{children:o}),(0,v.jsx)(W,{scope:r,isInside:!0,children:(0,v.jsx)(h.fC,{id:d.contentId,role:"tooltip",children:l||o})})]})})});D.displayName=_;var H="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=g(r);return A(H,r).isInside?null:(0,v.jsx)(c.Eh,{...o,...n,ref:t})}).displayName=H;var I=T,X=k,V=P,Y=D},31725:function(e,t,r){r.d(t,{TX:function(){return a},fC:function(){return s}});var n=r(2265),o=r(25171),l=r(57437),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,l.jsx)(o.WV.span,{...e,ref:t,style:{...i,...e.style}}));a.displayName="VisuallyHidden";var s=a}}]);