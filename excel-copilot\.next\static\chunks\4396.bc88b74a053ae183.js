"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4396],{84396:function(e,t,a){a.r(t),a.d(t,{SupabaseClient:function(){return i},SupabaseMonitoringService:function(){return l}});var r=a(9821),o=a(18473);let s=new Map;function c(e){let t=s.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}function n(e,t){s.set(e,{data:t,timestamp:Date.now()})}class i{async managementRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.serviceRoleKey)throw Error("Supabase service role key n\xe3o configurada");let a="".concat(this.managementApiUrl).concat(e),r={Authorization:"Bearer ".concat(this.serviceRoleKey),"Content-Type":"application/json",apikey:this.serviceRoleKey,...t.headers};try{let e=await fetch(a,{...t,headers:r});if(!e.ok){let t=await e.text();throw Error("Supabase Management API error: ".concat(e.status," - ").concat(t))}return await e.json()}catch(t){throw o.logger.error("Erro na requisi\xe7\xe3o Supabase Management API",{endpoint:e,error:t}),t}}async projectRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.serviceRoleKey||!this.projectUrl)throw Error("Supabase configura\xe7\xe3o incompleta");let a="".concat(this.projectUrl,"/rest/v1").concat(e),r={Authorization:"Bearer ".concat(this.serviceRoleKey),apikey:this.serviceRoleKey,"Content-Type":"application/json",...t.headers};try{let e=await fetch(a,{...t,headers:r});if(!e.ok){let t=await e.text();throw Error("Supabase REST API error: ".concat(e.status," - ").concat(t))}return await e.json()}catch(t){throw o.logger.error("Erro na requisi\xe7\xe3o Supabase REST API",{endpoint:e,error:t}),t}}async getProjectInfo(){let e="project-info-".concat(this.projectRef),t=c(e);if(t)return t;let a=await this.managementRequest("/projects/".concat(this.projectRef));return n(e,a),a}async getTables(){let e="tables-".concat(this.projectRef),t=c(e);if(t)return t;let a=await this.projectRequest("/rpc/sql?query=".concat(encodeURIComponent("\n      SELECT\n        schemaname as schema,\n        tablename as name,\n        pg_total_relation_size(schemaname||'.'||tablename) as bytes,\n        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size\n      FROM pg_tables\n      WHERE schemaname = 'public'\n      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC\n    ")));return n(e,a),a}async getStorageBuckets(){let e="buckets-".concat(this.projectRef),t=c(e);if(t)return t;let a="".concat(this.projectUrl,"/storage/v1/bucket"),r=await fetch(a,{headers:{Authorization:"Bearer ".concat(this.serviceRoleKey),apikey:this.serviceRoleKey}});if(!r.ok)throw Error("Storage API error: ".concat(r.status));let o=await r.json();return n(e,o),o}async getStorageObjects(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a="".concat(this.projectUrl,"/storage/v1/object/list/").concat(e),r=await fetch(a,{method:"POST",headers:{Authorization:"Bearer ".concat(this.serviceRoleKey),apikey:this.serviceRoleKey,"Content-Type":"application/json"},body:JSON.stringify({limit:100,offset:0,sortBy:{column:"name",order:"asc"},prefix:t})});if(!r.ok)throw Error("Storage list error: ".concat(r.status));return await r.json()}async getProjectMetrics(){let e="metrics-".concat(this.projectRef),t=c(e);if(t)return t;let[a,r]=await Promise.all([this.getTables().catch(()=>[]),this.getStorageBuckets().catch(()=>[])]),o={database:{connections:0,queries_per_second:0,cache_hit_rate:0,table_count:a.length,total_size:a.reduce((e,t)=>e+(t.bytes||0),0).toString()},storage:{total_objects:0,total_size:0,bucket_count:r.length,bandwidth_used:0},realtime:{active_connections:0,messages_per_second:0,channels_count:0}};return n(e,o),o}async checkHealth(){try{if(!this.serviceRoleKey||!this.projectUrl)return{configured:!1,databaseAccessible:!1,storageAccessible:!1,realtimeAccessible:!1,tableCount:0,bucketCount:0,storageUsage:0,recentActivity:0};let[e,t,a]=await Promise.allSettled([this.getProjectInfo(),this.getTables(),this.getStorageBuckets()]),r="fulfilled"===t.status,o="fulfilled"===a.status;return{configured:!0,databaseAccessible:r,storageAccessible:o,realtimeAccessible:!0,lastSync:new Date().toISOString(),projectInfo:"fulfilled"===e.status?e.value:void 0,tableCount:r?t.value.length:0,bucketCount:o?a.value.length:0,storageUsage:0,recentActivity:0}}catch(e){return o.logger.error("Supabase health check failed:",e),{configured:!!this.serviceRoleKey,databaseAccessible:!1,storageAccessible:!1,realtimeAccessible:!1,tableCount:0,bucketCount:0,storageUsage:0,recentActivity:0}}}constructor(e){this.managementApiUrl="https://api.supabase.com/v1",this.serviceRoleKey=(null==e?void 0:e.serviceRoleKey)||r.Vi.SUPABASE_SERVICE_ROLE_KEY||"",this.projectUrl=(null==e?void 0:e.projectUrl)||r.Vi.SUPABASE_URL||"";let t=this.projectUrl.match(/https:\/\/([^.]+)\.supabase\.co/);this.projectRef=(null==t?void 0:t[1])||"",this.serviceRoleKey||o.logger.warn("Supabase service role key n\xe3o configurada"),this.projectRef||o.logger.warn("Supabase project ref n\xe3o pode ser extra\xeddo da URL")}}class l{async getProjectStatus(){try{let e=await this.client.checkHealth(),t="healthy",a="Todos os servi\xe7os funcionando normalmente";e.configured?e.databaseAccessible||e.storageAccessible?e.databaseAccessible&&e.storageAccessible||(t="degraded",a="Alguns servi\xe7os com problemas"):(t="down",a="Servi\xe7os principais inacess\xedveis"):(t="down",a="Supabase n\xe3o configurado");let r=e.configured?await this.client.getProjectMetrics().catch(()=>null):null;return{status:t,project:e.projectInfo||null,services:{database:e.databaseAccessible,storage:e.storageAccessible,realtime:e.realtimeAccessible},metrics:r,message:a}}catch(e){return o.logger.error("Erro ao obter status do projeto Supabase:",e),{status:"down",project:null,services:{database:!1,storage:!1,realtime:!1},metrics:null,message:"Erro ao conectar com Supabase"}}}async getDatabaseSummary(){try{let e=await this.client.getTables(),t=e.reduce((e,t)=>e+(t.bytes||0),0),a=e.sort((e,t)=>(t.bytes||0)-(e.bytes||0)).slice(0,5);return{tables:e,totalTables:e.length,totalSize:this.formatBytes(t),largestTables:a}}catch(e){throw o.logger.error("Erro ao obter resumo do banco:",e),e}}async getStorageSummary(){try{let e=await this.client.getStorageBuckets(),t=await Promise.all(e.map(async e=>{try{let t=await this.client.getStorageObjects(e.name),a=t.reduce((e,t)=>{var a;return e+((null===(a=t.metadata)||void 0===a?void 0:a.size)||0)},0);return{bucket:e,objectCount:t.length,totalSize:a}}catch(t){return o.logger.warn("Erro ao obter objetos do bucket ".concat(e.name,":"),t),{bucket:e,objectCount:0,totalSize:0}}}));return{buckets:e,totalBuckets:e.length,publicBuckets:e.filter(e=>e.public).length,privateBuckets:e.filter(e=>!e.public).length,bucketDetails:t}}catch(e){throw o.logger.error("Erro ao obter resumo do storage:",e),e}}async getBucketContents(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{let[a,r]=await Promise.all([this.client.getStorageBuckets(),this.client.getStorageObjects(e,t)]),o=a.find(t=>t.name===e)||null,s=r.reduce((e,t)=>{var a;return e+((null===(a=t.metadata)||void 0===a?void 0:a.size)||0)},0);return{bucket:o,objects:r,totalObjects:r.length,totalSize:s}}catch(t){throw o.logger.error("Erro ao obter conte\xfado do bucket ".concat(e,":"),t),t}}async getPerformanceMetrics(){try{let e=await this.client.getProjectMetrics();return{database:{tableCount:e.database.table_count,totalSize:this.formatBytes(parseInt(e.database.total_size)),avgQueryTime:0,connectionCount:e.database.connections},storage:{bucketCount:e.storage.bucket_count,totalObjects:e.storage.total_objects,totalSize:this.formatBytes(e.storage.total_size),bandwidthUsed:this.formatBytes(e.storage.bandwidth_used)},realtime:{activeConnections:e.realtime.active_connections,messagesPerSecond:e.realtime.messages_per_second,channelsCount:e.realtime.channels_count}}}catch(e){throw o.logger.error("Erro ao obter m\xe9tricas de performance:",e),e}}formatBytes(e){if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][t]}constructor(e){this.client=new i(e)}}}}]);