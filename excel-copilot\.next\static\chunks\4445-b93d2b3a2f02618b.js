"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4445],{58948:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20",key:"t4utmx"}]])},88592:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},74622:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},11005:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},87140:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},92699:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},38296:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(81066).Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},87138:function(e,t,r){r.d(t,{default:function(){return s.a}});var n=r(231),s=r.n(n)},46246:function(e,t,r){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(2265),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,o=n.useEffect,a=n.useLayoutEffect,u=n.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),s=n[0].inst,c=n[1];return a(function(){s.value=r,s.getSnapshot=t,l(s)&&c({inst:s})},[e,r,t]),o(function(){return l(s)&&c({inst:s}),e(function(){l(s)&&c({inst:s})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},10554:function(e,t,r){e.exports=r(46246)},81464:function(e,t,r){r.d(t,{NY:function(){return S},Ee:function(){return C},fC:function(){return E}});var n=r(2265),s=r(98324),i=r(75137),o=r(1336),a=r(25171),u=r(10554);function l(){return()=>{}}var c=r(57437),h="Avatar",[d,f]=(0,s.b)(h),[p,y]=d(h),m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...s}=e,[i,o]=n.useState("idle");return(0,c.jsx)(p,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:o,children:(0,c.jsx)(a.WV.span,{...s,ref:t})})});m.displayName=h;var v="AvatarImage",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:s,onLoadingStatusChange:h=()=>{},...d}=e,f=y(v,r),p=function(e,t){let{referrerPolicy:r,crossOrigin:s}=t,i=(0,u.useSyncExternalStore)(l,()=>!0,()=>!1),a=n.useRef(null),c=i?(a.current||(a.current=new window.Image),a.current):null,[h,d]=n.useState(()=>O(c,e));return(0,o.b)(()=>{d(O(c,e))},[c,e]),(0,o.b)(()=>{let e=e=>()=>{d(e)};if(!c)return;let t=e("loaded"),n=e("error");return c.addEventListener("load",t),c.addEventListener("error",n),r&&(c.referrerPolicy=r),"string"==typeof s&&(c.crossOrigin=s),()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)}},[c,s,r]),h}(s,d),m=(0,i.W)(e=>{h(e),f.onImageLoadingStatusChange(e)});return(0,o.b)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,c.jsx)(a.WV.img,{...d,ref:t,src:s}):null});b.displayName=v;var g="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:s,...i}=e,o=y(g,r),[u,l]=n.useState(void 0===s);return n.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>l(!0),s);return()=>window.clearTimeout(e)}},[s]),u&&"loaded"!==o.imageLoadingStatus?(0,c.jsx)(a.WV.span,{...i,ref:t}):null});function O(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=g;var E=m,C=b,S=w},48646:function(e,t,r){r.d(t,{_:function(){return n}});function n(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}},68781:function(e,t,r){r.d(t,{j:function(){return o}});var n=r(31811),s=r(75025);class i extends n.l{constructor(){super(),this.setup=e=>{if(!s.sk&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),window.addEventListener("focus",t,!1),()=>{window.removeEventListener("visibilitychange",t),window.removeEventListener("focus",t)}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.cleanup)||e.call(this),this.cleanup=void 0}}setEventListener(e){var t;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.focused!==e&&(this.focused=e,this.onFocus())}onFocus(){this.listeners.forEach(({listener:e})=>{e()})}isFocused(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)}}let o=new i},88172:function(e,t,r){function n(){return{onFetch:e=>{e.fetchFn=()=>{var t,r,n,o,a,u;let l;let c=null==(t=e.fetchOptions)?void 0:null==(r=t.meta)?void 0:r.refetchPage,h=null==(n=e.fetchOptions)?void 0:null==(o=n.meta)?void 0:o.fetchMore,d=null==h?void 0:h.pageParam,f=(null==h?void 0:h.direction)==="forward",p=(null==h?void 0:h.direction)==="backward",y=(null==(a=e.state.data)?void 0:a.pages)||[],m=(null==(u=e.state.data)?void 0:u.pageParams)||[],v=m,b=!1,g=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>{var t,r;return null!=(t=e.signal)&&t.aborted?b=!0:null==(r=e.signal)||r.addEventListener("abort",()=>{b=!0}),e.signal}})},w=e.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+e.options.queryHash+"'")),O=(e,t,r,n)=>(v=n?[t,...v]:[...v,t],n?[r,...e]:[...e,r]),E=(t,r,n,s)=>{if(b)return Promise.reject("Cancelled");if(void 0===n&&!r&&t.length)return Promise.resolve(t);let i={queryKey:e.queryKey,pageParam:n,meta:e.options.meta};return g(i),Promise.resolve(w(i)).then(e=>O(t,n,e,s))};if(y.length){if(f){let t=void 0!==d,r=t?d:s(e.options,y);l=E(y,t,r)}else if(p){let t=void 0!==d,r=t?d:i(e.options,y);l=E(y,t,r,!0)}else{v=[];let t=void 0===e.options.getNextPageParam;l=!c||!y[0]||c(y[0],0,y)?E([],t,m[0]):Promise.resolve(O([],m[0],y[0]));for(let r=1;r<y.length;r++)l=l.then(n=>{if(!c||!y[r]||c(y[r],r,y)){let i=t?m[r]:s(e.options,n);return E(n,t,i)}return Promise.resolve(O(n,m[r],y[r]))})}}else l=E([]);return l.then(e=>({pages:e,pageParams:v}))}}}}function s(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}function i(e,t){return null==e.getPreviousPageParam?void 0:e.getPreviousPageParam(t[0],t)}function o(e,t){if(e.getNextPageParam&&Array.isArray(t)){let r=s(e,t);return null!=r&&!1!==r}}function a(e,t){if(e.getPreviousPageParam&&Array.isArray(t)){let r=i(e,t);return null!=r&&!1!==r}}r.d(t,{Gm:function(){return n},Qy:function(){return o},ZF:function(){return a}})},74502:function(e,t,r){r.d(t,{_:function(){return n}});let n=console},3976:function(e,t,r){r.d(t,{R:function(){return u},m:function(){return a}});var n=r(74502),s=r(96176),i=r(23318),o=r(34786);class a extends i.F{constructor(e){super(),this.defaultOptions=e.defaultOptions,this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.logger=e.logger||n._,this.observers=[],this.state=e.state||u(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(e){this.dispatch({type:"setState",state:e})}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.observers=this.observers.filter(t=>t!==e),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var e,t;return null!=(e=null==(t=this.retryer)?void 0:t.continue())?e:this.execute()}async execute(){var e,t,r,n,s,i,a,u,l,c,h,d,f,p,y,m,v,b,g,w;let O="loading"===this.state.status;try{if(!O){this.dispatch({type:"loading",variables:this.options.variables}),await (null==(l=(c=this.mutationCache.config).onMutate)?void 0:l.call(c,this.state.variables,this));let e=await (null==(h=(d=this.options).onMutate)?void 0:h.call(d,this.state.variables));e!==this.state.context&&this.dispatch({type:"loading",context:e,variables:this.state.variables})}let f=await (()=>{var e;return this.retryer=(0,o.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise})();return await (null==(e=(t=this.mutationCache.config).onSuccess)?void 0:e.call(t,f,this.state.variables,this.state.context,this)),await (null==(r=(n=this.options).onSuccess)?void 0:r.call(n,f,this.state.variables,this.state.context)),await (null==(s=(i=this.mutationCache.config).onSettled)?void 0:s.call(i,f,null,this.state.variables,this.state.context,this)),await (null==(a=(u=this.options).onSettled)?void 0:a.call(u,f,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:f}),f}catch(e){try{throw await (null==(f=(p=this.mutationCache.config).onError)?void 0:f.call(p,e,this.state.variables,this.state.context,this)),await (null==(y=(m=this.options).onError)?void 0:y.call(m,e,this.state.variables,this.state.context)),await (null==(v=(b=this.mutationCache.config).onSettled)?void 0:v.call(b,void 0,e,this.state.variables,this.state.context,this)),await (null==(g=(w=this.options).onSettled)?void 0:g.call(w,void 0,e,this.state.variables,this.state.context)),e}finally{this.dispatch({type:"error",error:e})}}}dispatch(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"loading":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,o.Kw)(this.options.networkMode),status:"loading",variables:e.variables};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"};case"setState":return{...t,...e.state}}})(this.state),s.V.batch(()=>{this.observers.forEach(t=>{t.onMutationUpdate(e)}),this.mutationCache.notify({mutation:this,type:"updated",action:e})})}}function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},96176:function(e,t,r){r.d(t,{V:function(){return s}});var n=r(75025);let s=function(){let e=[],t=0,r=e=>{e()},s=e=>{e()},i=s=>{t?e.push(s):(0,n.A4)(()=>{r(s)})},o=()=>{let t=e;e=[],t.length&&(0,n.A4)(()=>{s(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||o()}return r},batchCalls:e=>(...t)=>{i(()=>{e(...t)})},schedule:i,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{s=e}}}()},56906:function(e,t,r){r.d(t,{N:function(){return a}});var n=r(31811),s=r(75025);let i=["online","offline"];class o extends n.l{constructor(){super(),this.setup=e=>{if(!s.sk&&window.addEventListener){let t=()=>e();return i.forEach(e=>{window.addEventListener(e,t,!1)}),()=>{i.forEach(e=>{window.removeEventListener(e,t)})}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.cleanup)||e.call(this),this.cleanup=void 0}}setEventListener(e){var t;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e(e=>{"boolean"==typeof e?this.setOnline(e):this.onOnline()})}setOnline(e){this.online!==e&&(this.online=e,this.onOnline())}onOnline(){this.listeners.forEach(({listener:e})=>{e()})}isOnline(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine}}let a=new o},47808:function(e,t,r){r.d(t,{S:function(){return m}});var n=r(75025),s=r(74502),i=r(96176),o=r(34786),a=r(23318);class u extends a.F{constructor(e){super(),this.abortSignalConsumed=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.logger=e.logger||s._,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"loading",fetchStatus:"idle"}}(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.cache.remove(this)}setData(e,t){let r=(0,n.oE)(this.state.data,e,this.options);return this.dispatch({data:r,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt,manual:null==t?void 0:t.manual}),r}setState(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})}cancel(e){var t;let r=this.promise;return null==(t=this.retryer)||t.cancel(e),r?r.then(n.ZT).catch(n.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(e=>!1!==e.options.enabled)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(e=>e.getCurrentResult().isStale)}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,n.Kp)(this.state.dataUpdatedAt,e)}onFocus(){var e;let t=this.observers.find(e=>e.shouldFetchOnWindowFocus());t&&t.refetch({cancelRefetch:!1}),null==(e=this.retryer)||e.continue()}onOnline(){var e;let t=this.observers.find(e=>e.shouldFetchOnReconnect());t&&t.refetch({cancelRefetch:!1}),null==(e=this.retryer)||e.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(e,t){var r,s,i,a;if("idle"!==this.state.fetchStatus){if(this.state.dataUpdatedAt&&null!=t&&t.cancelRefetch)this.cancel({silent:!0});else if(this.promise)return null==(i=this.retryer)||i.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let u=(0,n.G9)(),l={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>{if(u)return this.abortSignalConsumed=!0,u.signal}})};c(l);let h={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(l)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'")};c(h),null==(r=this.options.behavior)||r.onFetch(h),this.revertState=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(s=h.fetchOptions)?void 0:s.meta))&&this.dispatch({type:"fetch",meta:null==(a=h.fetchOptions)?void 0:a.meta});let d=e=>{if((0,o.DV)(e)&&e.silent||this.dispatch({type:"error",error:e}),!(0,o.DV)(e)){var t,r,n,s;null==(t=(r=this.cache.config).onError)||t.call(r,e,this),null==(n=(s=this.cache.config).onSettled)||n.call(s,this.state.data,e,this)}this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=(0,o.Mz)({fn:h.fetchFn,abort:null==u?void 0:u.abort.bind(u),onSuccess:e=>{var t,r,n,s;if(void 0===e){d(Error(this.queryHash+" data is undefined"));return}this.setData(e),null==(t=(r=this.cache.config).onSuccess)||t.call(r,e,this),null==(n=(s=this.cache.config).onSettled)||n.call(s,e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:d,onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(e){this.state=(t=>{var r,n;switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null!=(r=e.meta)?r:null,fetchStatus:(0,o.Kw)(this.options.networkMode)?"fetching":"paused",...!t.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(n=e.dataUpdatedAt)?n:Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=e.error;if((0,o.DV)(s)&&s.revert&&this.revertState)return{...this.revertState,fetchStatus:"idle"};return{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate(e)}),this.cache.notify({query:this,type:"updated",action:e})})}}var l=r(31811);class c extends l.l{constructor(e){super(),this.config=e||{},this.queries=[],this.queriesMap={}}build(e,t,r){var s;let i=t.queryKey,o=null!=(s=t.queryHash)?s:(0,n.Rm)(i,t),a=this.get(o);return a||(a=new u({cache:this,logger:e.getLogger(),queryKey:i,queryHash:o,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(i)}),this.add(a)),a}add(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"added",query:e}))}remove(e){let t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter(t=>t!==e),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"removed",query:e}))}clear(){i.V.batch(()=>{this.queries.forEach(e=>{this.remove(e)})})}get(e){return this.queriesMap[e]}getAll(){return this.queries}find(e,t){let[r]=(0,n.I6)(e,t);return void 0===r.exact&&(r.exact=!0),this.queries.find(e=>(0,n._x)(r,e))}findAll(e,t){let[r]=(0,n.I6)(e,t);return Object.keys(r).length>0?this.queries.filter(e=>(0,n._x)(r,e)):this.queries}notify(e){i.V.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}onFocus(){i.V.batch(()=>{this.queries.forEach(e=>{e.onFocus()})})}onOnline(){i.V.batch(()=>{this.queries.forEach(e=>{e.onOnline()})})}}var h=r(3976);class d extends l.l{constructor(e){super(),this.config=e||{},this.mutations=[],this.mutationId=0}build(e,t,r){let n=new h.m({mutationCache:this,logger:e.getLogger(),mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:r,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0});return this.add(n),n}add(e){this.mutations.push(e),this.notify({type:"added",mutation:e})}remove(e){this.mutations=this.mutations.filter(t=>t!==e),this.notify({type:"removed",mutation:e})}clear(){i.V.batch(()=>{this.mutations.forEach(e=>{this.remove(e)})})}getAll(){return this.mutations}find(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find(t=>(0,n.X7)(e,t))}findAll(e){return this.mutations.filter(t=>(0,n.X7)(e,t))}notify(e){i.V.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}resumePausedMutations(){var e;return this.resuming=(null!=(e=this.resuming)?e:Promise.resolve()).then(()=>{let e=this.mutations.filter(e=>e.state.isPaused);return i.V.batch(()=>e.reduce((e,t)=>e.then(()=>t.continue().catch(n.ZT)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}var f=r(68781),p=r(56906),y=r(88172);class m{constructor(e={}){this.queryCache=e.queryCache||new c,this.mutationCache=e.mutationCache||new d,this.logger=e.logger||s._,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,1===this.mountCount&&(this.unsubscribeFocus=f.j.subscribe(()=>{f.j.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=p.N.subscribe(()=>{p.N.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var e,t;this.mountCount--,0===this.mountCount&&(null==(e=this.unsubscribeFocus)||e.call(this),this.unsubscribeFocus=void 0,null==(t=this.unsubscribeOnline)||t.call(this),this.unsubscribeOnline=void 0)}isFetching(e,t){let[r]=(0,n.I6)(e,t);return r.fetchStatus="fetching",this.queryCache.findAll(r).length}isMutating(e){return this.mutationCache.findAll({...e,fetching:!0}).length}getQueryData(e,t){var r;return null==(r=this.queryCache.find(e,t))?void 0:r.state.data}ensureQueryData(e,t,r){let s=(0,n._v)(e,t,r),i=this.getQueryData(s.queryKey);return i?Promise.resolve(i):this.fetchQuery(s)}getQueriesData(e){return this.getQueryCache().findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let s=this.queryCache.find(e),i=null==s?void 0:s.state.data,o=(0,n.SE)(t,i);if(void 0===o)return;let a=(0,n._v)(e),u=this.defaultQueryOptions(a);return this.queryCache.build(this,u).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return i.V.batch(()=>this.getQueryCache().findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e,t){var r;return null==(r=this.queryCache.find(e,t))?void 0:r.state}removeQueries(e,t){let[r]=(0,n.I6)(e,t),s=this.queryCache;i.V.batch(()=>{s.findAll(r).forEach(e=>{s.remove(e)})})}resetQueries(e,t,r){let[s,o]=(0,n.I6)(e,t,r),a=this.queryCache,u={type:"active",...s};return i.V.batch(()=>(a.findAll(s).forEach(e=>{e.reset()}),this.refetchQueries(u,o)))}cancelQueries(e,t,r){let[s,o={}]=(0,n.I6)(e,t,r);return void 0===o.revert&&(o.revert=!0),Promise.all(i.V.batch(()=>this.queryCache.findAll(s).map(e=>e.cancel(o)))).then(n.ZT).catch(n.ZT)}invalidateQueries(e,t,r){let[s,o]=(0,n.I6)(e,t,r);return i.V.batch(()=>{var e,t;if(this.queryCache.findAll(s).forEach(e=>{e.invalidate()}),"none"===s.refetchType)return Promise.resolve();let r={...s,type:null!=(e=null!=(t=s.refetchType)?t:s.type)?e:"active"};return this.refetchQueries(r,o)})}refetchQueries(e,t,r){let[s,o]=(0,n.I6)(e,t,r),a=Promise.all(i.V.batch(()=>this.queryCache.findAll(s).filter(e=>!e.isDisabled()).map(e=>{var t;return e.fetch(void 0,{...o,cancelRefetch:null==(t=null==o?void 0:o.cancelRefetch)||t,meta:{refetchPage:s.refetchPage}})}))).then(n.ZT);return null!=o&&o.throwOnError||(a=a.catch(n.ZT)),a}fetchQuery(e,t,r){let s=(0,n._v)(e,t,r),i=this.defaultQueryOptions(s);void 0===i.retry&&(i.retry=!1);let o=this.queryCache.build(this,i);return o.isStaleByTime(i.staleTime)?o.fetch(i):Promise.resolve(o.state.data)}prefetchQuery(e,t,r){return this.fetchQuery(e,t,r).then(n.ZT).catch(n.ZT)}fetchInfiniteQuery(e,t,r){let s=(0,n._v)(e,t,r);return s.behavior=(0,y.Gm)(),this.fetchQuery(s)}prefetchInfiniteQuery(e,t,r){return this.fetchInfiniteQuery(e,t,r).then(n.ZT).catch(n.ZT)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(e){this.defaultOptions=e}setQueryDefaults(e,t){let r=this.queryDefaults.find(t=>(0,n.yF)(e)===(0,n.yF)(t.queryKey));r?r.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})}getQueryDefaults(e){if(!e)return;let t=this.queryDefaults.find(t=>(0,n.to)(e,t.queryKey));return null==t?void 0:t.defaultOptions}setMutationDefaults(e,t){let r=this.mutationDefaults.find(t=>(0,n.yF)(e)===(0,n.yF)(t.mutationKey));r?r.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})}getMutationDefaults(e){if(!e)return;let t=this.mutationDefaults.find(t=>(0,n.to)(e,t.mutationKey));return null==t?void 0:t.defaultOptions}defaultQueryOptions(e){if(null!=e&&e._defaulted)return e;let t={...this.defaultOptions.queries,...this.getQueryDefaults(null==e?void 0:e.queryKey),...e,_defaulted:!0};return!t.queryHash&&t.queryKey&&(t.queryHash=(0,n.Rm)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.useErrorBoundary&&(t.useErrorBoundary=!!t.suspense),t}defaultMutationOptions(e){return null!=e&&e._defaulted?e:{...this.defaultOptions.mutations,...this.getMutationDefaults(null==e?void 0:e.mutationKey),...e,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}},23318:function(e,t,r){r.d(t,{F:function(){return s}});var n=r(75025);class s{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.PN)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(e){this.cacheTime=Math.max(this.cacheTime||0,null!=e?e:n.sk?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}},34786:function(e,t,r){r.d(t,{DV:function(){return l},Kw:function(){return a},Mz:function(){return c}});var n=r(68781),s=r(56906),i=r(75025);function o(e){return Math.min(1e3*2**e,3e4)}function a(e){return(null!=e?e:"online")!=="online"||s.N.isOnline()}class u{constructor(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent}}function l(e){return e instanceof u}function c(e){let t,r,l,c=!1,h=0,d=!1,f=new Promise((e,t)=>{r=e,l=t}),p=()=>!n.j.isFocused()||"always"!==e.networkMode&&!s.N.isOnline(),y=n=>{d||(d=!0,null==e.onSuccess||e.onSuccess(n),null==t||t(),r(n))},m=r=>{d||(d=!0,null==e.onError||e.onError(r),null==t||t(),l(r))},v=()=>new Promise(r=>{t=e=>{let t=d||!p();return t&&r(e),t},null==e.onPause||e.onPause()}).then(()=>{t=void 0,d||null==e.onContinue||e.onContinue()}),b=()=>{let t;if(!d){try{t=e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(y).catch(t=>{var r,n;if(d)return;let s=null!=(r=e.retry)?r:3,a=null!=(n=e.retryDelay)?n:o,u="function"==typeof a?a(h,t):a,l=!0===s||"number"==typeof s&&h<s||"function"==typeof s&&s(h,t);if(c||!l){m(t);return}h++,null==e.onFail||e.onFail(h,t),(0,i.Gh)(u).then(()=>{if(p())return v()}).then(()=>{c?m(t):b()})})}};return a(e.networkMode)?b():v().then(b),{promise:f,cancel:t=>{d||(m(new u(t)),null==e.abort||e.abort())},continue:()=>(null==t?void 0:t())?f:Promise.resolve(),cancelRetry:()=>{c=!0},continueRetry:()=>{c=!1}}}},31811:function(e,t,r){r.d(t,{l:function(){return n}});class n{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){let t={listener:e};return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},75025:function(e,t,r){r.d(t,{A4:function(){return R},G9:function(){return q},Gh:function(){return S},I6:function(){return d},Kp:function(){return l},PN:function(){return o},Rc:function(){return u},Rm:function(){return y},SE:function(){return i},VS:function(){return g},X7:function(){return p},ZT:function(){return s},_v:function(){return c},_x:function(){return f},e5:function(){return a},lV:function(){return h},oE:function(){return P},sk:function(){return n},to:function(){return v},yF:function(){return m}});let n="undefined"==typeof window||"Deno"in window;function s(){}function i(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function a(e,t){return e.filter(e=>!t.includes(e))}function u(e,t,r){let n=e.slice(0);return n[t]=r,n}function l(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t,r){return C(e)?"function"==typeof t?{...r,queryKey:e,queryFn:t}:{...t,queryKey:e}:e}function h(e,t,r){return C(e)?"function"==typeof t?{...r,mutationKey:e,mutationFn:t}:{...t,mutationKey:e}:"function"==typeof e?{...t,mutationFn:e}:{...e}}function d(e,t,r){return C(e)?[{...t,queryKey:e},r]:[e||{},t]}function f(e,t){let{type:r="all",exact:n,fetchStatus:s,predicate:i,queryKey:o,stale:a}=e;if(C(o)){if(n){if(t.queryHash!==y(o,t.options))return!1}else{if(!b(t.queryKey,o))return!1}}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&(void 0===s||s===t.state.fetchStatus)&&(!i||!!i(t))}function p(e,t){let{exact:r,fetching:n,predicate:s,mutationKey:i}=e;if(C(i)){if(!t.options.mutationKey)return!1;if(r){if(m(t.options.mutationKey)!==m(i))return!1}else{if(!b(t.options.mutationKey,i))return!1}}return("boolean"!=typeof n||"loading"===t.state.status===n)&&(!s||!!s(t))}function y(e,t){return((null==t?void 0:t.queryKeyHashFn)||m)(e)}function m(e){return JSON.stringify(e,(e,t)=>O(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function v(e,t){return b(e,t)}function b(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&!Object.keys(t).some(r=>!b(e[r],t[r]))}function g(e,t){if(e&&!t||t&&!e)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function w(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function O(e){if(!E(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!(E(r)&&r.hasOwnProperty("isPrototypeOf"))}function E(e){return"[object Object]"===Object.prototype.toString.call(e)}function C(e){return Array.isArray(e)}function S(e){return new Promise(t=>{setTimeout(t,e)})}function R(e){S(0).then(e)}function q(){if("function"==typeof AbortController)return new AbortController}function P(e,t,r){return null!=r.isDataEqual&&r.isDataEqual(e,t)?e:"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function e(t,r){if(t===r)return t;let n=w(t)&&w(r);if(n||O(t)&&O(r)){let s=n?t.length:Object.keys(t).length,i=n?r:Object.keys(r),o=i.length,a=n?[]:{},u=0;for(let s=0;s<o;s++){let o=n?s:i[s];a[o]=e(t[o],r[o]),a[o]===t[o]&&u++}return s===o&&u===s?t:a}return r}(e,t):t}},27079:function(e,t,r){r.d(t,{NL:function(){return a},aH:function(){return u}});var n=r(2265);let s=n.createContext(void 0),i=n.createContext(!1);function o(e,t){return e||(t&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=s),window.ReactQueryClientContext):s)}let a=function(){let{context:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=n.useContext(o(e,n.useContext(i)));if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},u=e=>{let{client:t,children:r,context:s,contextSharing:a=!1}=e;n.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]);let u=o(s,a);return n.createElement(i.Provider,{value:!s&&a},n.createElement(u.Provider,{value:t},r))}},89605:function(e,t,r){function n(e){let t={subscribe(t){let r=null,n=!1,s=!1,i=!1;function o(){if(null===r){i=!0;return}!s&&(s=!0,"function"==typeof r?r():r&&r.unsubscribe())}return r=e({next(e){n||t.next?.(e)},error(e){n||(n=!0,t.error?.(e),o())},complete(){n||(n=!0,t.complete?.(),o())}}),i&&o(),{unsubscribe:o}},pipe:(...e)=>(0===e.length?function(e){return e}:1===e.length?e[0]:function(t){return e.reduce((e,t)=>t(e),t)})(t)};return t}r.d(t,{BJ:function(){return R},Bm:function(){return x},N8:function(){return E}});class s extends Error{constructor(e){super(e),this.name="ObservableAbortError",Object.setPrototypeOf(this,s.prototype)}}var i=r(88106);function o(e){return!!e&&!Array.isArray(e)&&"object"==typeof e}class a extends Error{constructor(){super("Unable to transform response from server")}}function u(e,t){let r;try{r=function(e,t){if("error"in e){let r=t.transformer.deserialize(e.error);return{ok:!1,error:{...e,error:r}}}return{ok:!0,result:{...e.result,...(!e.result.type||"data"===e.result.type)&&{type:"data",data:t.transformer.deserialize(e.result.data)}}}}(e,t)}catch(e){throw new a}if(!r.ok&&(!o(r.error.error)||"number"!=typeof r.error.error.code)||r.ok&&!o(r.result))throw new a;return r}class l extends Error{static from(e,t={}){return e instanceof l||e instanceof Error&&"TRPCClientError"===e.name?(t.meta&&(e.meta={...e.meta,...t.meta}),e):o(e)&&o(e.error)&&"number"==typeof e.error.code&&"string"==typeof e.error.message?new l(e.error.message,{...t,result:e}):e instanceof Error?new l(e.message,{...t,cause:(0,i.sZ)(e)}):new l("Unknown error",{...t,cause:e})}constructor(e,t){let r=t?.cause;super(e,{cause:r}),this.meta=t?.meta,this.cause=r,this.shape=t?.result?.error,this.data=t?.result?.error.data,this.name="TRPCClientError",Object.setPrototypeOf(this,l.prototype)}}let c=e=>"function"==typeof e;function h(e){var t;return{url:e.url.toString().replace(/\/$/,""),fetch:e.fetch,AbortController:(t=e.AbortController)?t:"undefined"!=typeof window&&window.AbortController?window.AbortController:"undefined"!=typeof globalThis&&globalThis.AbortController?globalThis.AbortController:null}}let d={query:"GET",mutation:"POST"};function f(e){return"input"in e?e.runtime.transformer.serialize(e.input):function(e){let t={};for(let r=0;r<e.length;r++){let n=e[r];t[r]=n}return t}(e.inputs.map(t=>e.runtime.transformer.serialize(t)))}let p=e=>{let t=e.url+"/"+e.path,r=[];if("inputs"in e&&r.push("batch=1"),"query"===e.type){let t=f(e);void 0!==t&&r.push(`input=${encodeURIComponent(JSON.stringify(t))}`)}return r.length&&(t+="?"+r.join("&")),t},y=e=>{if("query"===e.type)return;let t=f(e);return void 0!==t?JSON.stringify(t):void 0},m=e=>b({...e,contentTypeHeader:"application/json",getUrl:p,getBody:y});async function v(e,t){let r=e.getUrl(e),n=e.getBody(e),{type:s}=e,i=await e.headers();/* istanbul ignore if -- @preserve */if("subscription"===s)throw Error("Subscriptions should use wsLink");let o={...e.contentTypeHeader?{"content-type":e.contentTypeHeader}:{},...e.batchModeHeader?{"trpc-batch-mode":e.batchModeHeader}:{},...i};return(function(e){if(e)return e;if("undefined"!=typeof window&&c(window.fetch))return window.fetch;if("undefined"!=typeof globalThis&&c(globalThis.fetch))return globalThis.fetch;throw Error("No fetch implementation found")})(e.fetch)(r,{method:d[s],signal:t?.signal,body:n,headers:o})}function b(e){let t=e.AbortController?new e.AbortController:null,r={},n=!1;return{promise:new Promise((s,i)=>{v(e,t).then(e=>(r.response=e,n=!0,e.json())).then(e=>{r.responseJSON=e,s({json:e,meta:r})}).catch(e=>{n=!0,i(l.from(e,{meta:r}))})}),cancel:()=>{n||t?.abort()}}}let g=()=>{throw Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new")};function w(e){let t=null,r=null,n=()=>{clearTimeout(r),r=null,t=null};function s(){let r=function(t){let r=[[]],n=0;for(;;){let s=t[n];if(!s)break;let i=r[r.length-1];if(s.aborted){s.reject?.(Error("Aborted")),n++;continue}if(e.validate(i.concat(s).map(e=>e.key))){i.push(s),n++;continue}if(0===i.length){s.reject?.(Error("Input is too big for a single dispatch")),n++;continue}r.push([])}return r}(t);for(let t of(n(),r)){if(!t.length)continue;let r={items:t,cancel:g};for(let e of t)e.batch=r;let n=(e,t)=>{let n=r.items[e];n.resolve?.(t),n.batch=null,n.reject=null,n.resolve=null},{promise:s,cancel:i}=e.fetch(r.items.map(e=>e.key),n);r.cancel=i,s.then(e=>{for(let t=0;t<e.length;t++){let r=e[t];n(t,r)}for(let e of r.items)e.reject?.(Error("Missing result")),e.batch=null}).catch(e=>{for(let t of r.items)t.reject?.(e),t.batch=null})}}return{load:function(e){let n={aborted:!1,key:e,batch:null,resolve:g,reject:g},i=new Promise((e,r)=>{n.reject=r,n.resolve=e,t||(t=[]),t.push(n)});return r||(r=setTimeout(s)),{promise:i,cancel:()=>{n.aborted=!0,n.batch?.items.every(e=>e.aborted)&&(n.batch.cancel(),n.batch=null)}}}}}function O(e){return function(t){let r=h(t),s=t.maxURLLength??1/0;return i=>{let o=n=>({validate:e=>{if(s===1/0)return!0;let t=e.map(e=>e.path).join(","),o=e.map(e=>e.input);return p({...r,runtime:i,type:n,path:t,inputs:o}).length<=s},fetch:e({...r,runtime:i,type:n,opts:t})}),a=w(o("query")),c=w(o("mutation")),h={query:a,subscription:w(o("subscription")),mutation:c};return({op:e})=>n(t=>{let r;let{promise:n,cancel:s}=h[e.type].load(e);return n.then(e=>{r=e;let n=u(e.json,i);if(!n.ok){t.error(l.from(n.error,{meta:e.meta}));return}t.next({context:e.meta,result:n.result}),t.complete()}).catch(e=>{t.error(l.from(e,{meta:r?.meta}))}),()=>{s()}})}}}let E=O(e=>t=>{let r=t.map(e=>e.path).join(","),n=t.map(e=>e.input),{promise:s,cancel:i}=m({...e,path:r,inputs:n,headers:()=>e.opts.headers?"function"==typeof e.opts.headers?e.opts.headers({opList:t}):e.opts.headers:{}});return{promise:s.then(e=>(Array.isArray(e.json)?e.json:t.map(()=>e.json)).map(t=>({meta:e.meta,json:t}))),cancel:i}});function C(e){return t=>{let r=h(t);return s=>({op:i})=>n(n=>{let o;let{path:a,input:c,type:h}=i,{promise:d,cancel:f}=e.requester({...r,runtime:s,type:h,path:a,input:c,headers:()=>t.headers?"function"==typeof t.headers?t.headers({op:i}):t.headers:{}});return d.then(e=>{o=e.meta;let t=u(e.json,s);if(!t.ok){n.error(l.from(t.error,{meta:o}));return}n.next({context:e.meta,result:t.result}),n.complete()}).catch(e=>{n.error(l.from(e,{meta:o}))}),()=>{f()}})}}C({requester:m});class S{$request({type:e,input:t,path:r,context:s={}}){var i;return(i={links:this.links,op:{id:++this.requestId,type:e,path:r,input:t,context:s}},n(e=>(function e(t=0,r=i.op){let n=i.links[t];if(!n)throw Error("No more links to execute - did you forget to add an ending link?");return n({op:r,next:r=>e(t+1,r)})})().subscribe(e))).pipe(e=>{let t=0,r=null,n=[];return{subscribe:s=>(t++,n.push(s),r||(r=e.subscribe({next(e){for(let t of n)t.next?.(e)},error(e){for(let t of n)t.error?.(e)},complete(){for(let e of n)e.complete?.()}})),{unsubscribe(){t--,function(){if(0===t&&r){let e=r;r=null,e.unsubscribe()}}();let e=n.findIndex(e=>e===s);e>-1&&n.splice(e,1)}})}})}requestAsPromise(e){var t;let r;let{promise:n,abort:i}=(t=this.$request(e),{promise:new Promise((e,n)=>{let i=!1;function o(){i||(i=!0,n(new s("This operation was aborted.")),a.unsubscribe())}let a=t.subscribe({next(t){i=!0,e(t),o()},error(e){i=!0,n(e),o()},complete(){i=!0,o()}});r=o}),abort:r});return new Promise((t,r)=>{e.signal?.addEventListener("abort",i),n.then(e=>{t(e.result.data)}).catch(e=>{r(l.from(e))})})}query(e,t,r){return this.requestAsPromise({type:"query",path:e,input:t,context:r?.context,signal:r?.signal})}mutation(e,t,r){return this.requestAsPromise({type:"mutation",path:e,input:t,context:r?.context,signal:r?.signal})}subscription(e,t,r){return this.$request({type:"subscription",path:e,input:t,context:r?.context}).subscribe({next(e){"started"===e.result.type?r.onStarted?.():"stopped"===e.result.type?r.onStopped?.():r.onData?.(e.result.data)},error(e){r.onError?.(e)},complete(){r.onComplete?.()}})}constructor(e){this.requestId=0;let t=(()=>{let t=e.transformer;return t?"input"in t?e.transformer:{input:t,output:t}:{input:{serialize:e=>e,deserialize:e=>e},output:{serialize:e=>e,deserialize:e=>e}}})();this.runtime={transformer:{serialize:e=>t.input.serialize(e),deserialize:e=>t.output.deserialize(e)},combinedTransformer:t},this.links=e.links.map(e=>e(this.runtime))}}function R(e){return new S(e)}let q={query:"query",mutate:"mutation",subscribe:"subscription"},P=e=>q[e];function x(e){return(0,i.yh)(t=>e.hasOwnProperty(t)?e[t]:"__untypedClient"===t?e:(0,i.IX)(({path:r,args:n})=>{let s=[t,...r],i=P(s.pop()),o=s.join(".");return e[i](o,...n)}))}async function Q(e){let t=e.parse??JSON.parse;await k(e.readableStream,r=>{if(e.signal?.aborted||!r||"}"===r)return;let n=r.indexOf(":"),s=r.substring(2,n-1),i=r.substring(n+1);e.onSingle(Number(s),t(i))},e.textDecoder)}async function k(e,t,r){let n="",s=e=>{let s=r.decode(e).split("\n");if(1===s.length)n+=s[0];else if(s.length>1){t(n+s[0]);for(let e=1;e<s.length-1;e++)t(s[e]);n=s[s.length-1]}};"getReader"in e?await T(e,s):await new Promise(t=>{e.on("data",s),e.on("end",t)}),t(n)}async function T(e,t){let r=e.getReader(),n=await r.read();for(;!n.done;)t(n.value),n=await r.read()}let I=(e,t)=>{let r=e.AbortController?new e.AbortController:null;return{cancel:()=>r?.abort(),promise:v({...e,contentTypeHeader:"application/json",batchModeHeader:"stream",getUrl:p,getBody:y},r).then(async n=>{if(!n.body)throw Error("Received response without body");let s={response:n};return Q({readableStream:n.body,onSingle:t,parse:e=>({json:JSON.parse(e),meta:s}),signal:r?.signal,textDecoder:e.textDecoder})})}};O(e=>{let t=function(e){if(e)return e;if("undefined"!=typeof window&&window.TextDecoder)return new window.TextDecoder;if("undefined"!=typeof globalThis&&globalThis.TextDecoder)return new globalThis.TextDecoder;throw Error("No TextDecoder implementation found")}(e.opts.textDecoder);return(r,n)=>{let s=r.map(e=>e.path).join(","),i=r.map(e=>e.input),{cancel:o,promise:a}=I({...e,textDecoder:t,path:s,inputs:i,headers:()=>e.opts.headers?"function"==typeof e.opts.headers?e.opts.headers({opList:r}):e.opts.headers:{}},(e,t)=>{n(e,t)});return{promise:a.then(()=>[]),cancel:o}}});let A=e=>{if("input"in e){if(!(e.input instanceof FormData))throw Error("Input is not FormData");return e.input}};C({requester:e=>{if("mutation"!==e.type)throw Error("We only handle mutations with formdata");return b({...e,getUrl:()=>`${e.url}/${e.path}`,getBody:A})}})},76552:function(e,t,r){let n;r.d(t,{ec:function(){return z}});var s=r(89605),i=r(88106);function o(e,t){let[r,n]=Array.isArray(e)?e:[e],s="string"!=typeof r||""===r?[]:r.split(".");return n||t&&"any"!==t?[s,{...void 0!==n&&{input:n},...t&&"any"!==t&&{type:t}}]:s.length?[s]:[]}var a=r(75025),u=r(96176),l=r(68781),c=r(31811),h=r(34786);class d extends c.l{constructor(e,t){super(),this.client=e,this.options=t,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(t)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.currentQuery.addObserver(this),f(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return p(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return p(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(e,t){let r=this.options,n=this.currentQuery;if(this.options=this.client.defaultQueryOptions(e),(0,a.VS)(r,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),this.updateQuery();let s=this.hasListeners();s&&y(this.currentQuery,n,this.options,r)&&this.executeFetch(),this.updateResult(t),s&&(this.currentQuery!==n||this.options.enabled!==r.enabled||this.options.staleTime!==r.staleTime)&&this.updateStaleTimeout();let i=this.computeRefetchInterval();s&&(this.currentQuery!==n||this.options.enabled!==r.enabled||i!==this.currentRefetchInterval)&&this.updateRefetchInterval(i)}getOptimisticResult(e){let t=this.client.getQueryCache().build(this.client,e),r=this.createResult(t,e);return e.keepPreviousData||(void 0!==e.placeholderData?!r.isPlaceholderData:(0,a.VS)(this.getCurrentResult(),r))||(this.currentResult=r,this.currentResultOptions=this.options,this.currentResultState=this.currentQuery.state),r}getCurrentResult(){return this.currentResult}trackResult(e){let t={};return Object.keys(e).forEach(r=>{Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(r),e[r])})}),t}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch({refetchPage:e,...t}={}){return this.fetch({...t,meta:{refetchPage:e}})}fetchOptimistic(e){let t=this.client.defaultQueryOptions(e),r=this.client.getQueryCache().build(this.client,t);return r.isFetchingOptimistic=!0,r.fetch().then(()=>this.createResult(r,t))}fetch(e){var t;return this.executeFetch({...e,cancelRefetch:null==(t=e.cancelRefetch)||t}).then(()=>(this.updateResult(),this.currentResult))}executeFetch(e){this.updateQuery();let t=this.currentQuery.fetch(this.options,e);return null!=e&&e.throwOnError||(t=t.catch(a.ZT)),t}updateStaleTimeout(){if(this.clearStaleTimeout(),a.sk||this.currentResult.isStale||!(0,a.PN)(this.options.staleTime))return;let e=(0,a.Kp)(this.currentResult.dataUpdatedAt,this.options.staleTime);this.staleTimeoutId=setTimeout(()=>{this.currentResult.isStale||this.updateResult()},e+1)}computeRefetchInterval(){var e;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(e=this.options.refetchInterval)&&e}updateRefetchInterval(e){this.clearRefetchInterval(),this.currentRefetchInterval=e,!a.sk&&!1!==this.options.enabled&&(0,a.PN)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(()=>{(this.options.refetchIntervalInBackground||l.j.isFocused())&&this.executeFetch()},this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(e,t){let r;let n=this.currentQuery,s=this.options,i=this.currentResult,o=this.currentResultState,u=this.currentResultOptions,l=e!==n,c=l?e.state:this.currentQueryInitialState,d=l?this.currentResult:this.previousQueryResult,{state:p}=e,{dataUpdatedAt:v,error:b,errorUpdatedAt:g,fetchStatus:w,status:O}=p,E=!1,C=!1;if(t._optimisticResults){let r=this.hasListeners(),i=!r&&f(e,t),o=r&&y(e,n,t,s);(i||o)&&(w=(0,h.Kw)(e.options.networkMode)?"fetching":"paused",v||(O="loading")),"isRestoring"===t._optimisticResults&&(w="idle")}if(t.keepPreviousData&&!p.dataUpdatedAt&&null!=d&&d.isSuccess&&"error"!==O)r=d.data,v=d.dataUpdatedAt,O=d.status,E=!0;else if(t.select&&void 0!==p.data){if(i&&p.data===(null==o?void 0:o.data)&&t.select===this.selectFn)r=this.selectResult;else try{this.selectFn=t.select,r=t.select(p.data),r=(0,a.oE)(null==i?void 0:i.data,r,t),this.selectResult=r,this.selectError=null}catch(e){this.selectError=e}}else r=p.data;if(void 0!==t.placeholderData&&void 0===r&&"loading"===O){let e;if(null!=i&&i.isPlaceholderData&&t.placeholderData===(null==u?void 0:u.placeholderData))e=i.data;else if(e="function"==typeof t.placeholderData?t.placeholderData():t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.selectError=null}catch(e){this.selectError=e}void 0!==e&&(O="success",r=(0,a.oE)(null==i?void 0:i.data,e,t),C=!0)}this.selectError&&(b=this.selectError,r=this.selectResult,g=Date.now(),O="error");let S="fetching"===w,R="loading"===O,q="error"===O;return{status:O,fetchStatus:w,isLoading:R,isSuccess:"success"===O,isError:q,isInitialLoading:R&&S,data:r,dataUpdatedAt:v,error:b,errorUpdatedAt:g,failureCount:p.fetchFailureCount,failureReason:p.fetchFailureReason,errorUpdateCount:p.errorUpdateCount,isFetched:p.dataUpdateCount>0||p.errorUpdateCount>0,isFetchedAfterMount:p.dataUpdateCount>c.dataUpdateCount||p.errorUpdateCount>c.errorUpdateCount,isFetching:S,isRefetching:S&&!R,isLoadingError:q&&0===p.dataUpdatedAt,isPaused:"paused"===w,isPlaceholderData:C,isPreviousData:E,isRefetchError:q&&0!==p.dataUpdatedAt,isStale:m(e,t),refetch:this.refetch,remove:this.remove}}updateResult(e){let t=this.currentResult,r=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,(0,a.VS)(r,t))return;this.currentResult=r;let n={cache:!0};(null==e?void 0:e.listeners)!==!1&&(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.trackedProps.size)return!0;let n=new Set(null!=r?r:this.trackedProps);return this.options.useErrorBoundary&&n.add("error"),Object.keys(this.currentResult).some(e=>this.currentResult[e]!==t[e]&&n.has(e))})()&&(n.listeners=!0),this.notify({...n,...e})}updateQuery(){let e=this.client.getQueryCache().build(this.client,this.options);if(e===this.currentQuery)return;let t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==t||t.removeObserver(this),e.addObserver(this))}onQueryUpdate(e){let t={};"success"===e.type?t.onSuccess=!e.manual:"error"!==e.type||(0,h.DV)(e.error)||(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()}notify(e){u.V.batch(()=>{var t,r,n,s,i,o,a,u;e.onSuccess?(null==(t=(r=this.options).onSuccess)||t.call(r,this.currentResult.data),null==(n=(s=this.options).onSettled)||n.call(s,this.currentResult.data,null)):e.onError&&(null==(i=(o=this.options).onError)||i.call(o,this.currentResult.error),null==(a=(u=this.options).onSettled)||a.call(u,void 0,this.currentResult.error)),e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)}),e.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})})}}function f(e,t){return!1!==t.enabled&&!e.state.dataUpdatedAt&&!("error"===e.state.status&&!1===t.retryOnMount)||e.state.dataUpdatedAt>0&&p(e,t,t.refetchOnMount)}function p(e,t,r){if(!1!==t.enabled){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&m(e,t)}return!1}function y(e,t,r,n){return!1!==r.enabled&&(e!==t||!1===n.enabled)&&(!r.suspense||"error"!==e.state.status)&&m(e,r)}function m(e,t){return e.isStaleByTime(t.staleTime)}var v=r(2265);let b=r(10554).useSyncExternalStore,g=v.createContext((n=!1,{clearReset:()=>{n=!1},reset:()=>{n=!0},isReset:()=>n})),w=()=>v.useContext(g);var O=r(27079);let E=v.createContext(!1),C=()=>v.useContext(E);function S(e,t){return"function"==typeof e?e(...t):!!e}E.Provider;let R=(e,t)=>{(e.suspense||e.useErrorBoundary)&&!t.isReset()&&(e.retryOnMount=!1)},q=e=>{v.useEffect(()=>{e.clearReset()},[e])},P=e=>{let{result:t,errorResetBoundary:r,useErrorBoundary:n,query:s}=e;return t.isError&&!r.isReset()&&!t.isFetching&&S(n,[t.error,s])},x=e=>{e.suspense&&"number"!=typeof e.staleTime&&(e.staleTime=1e3)},Q=(e,t)=>e.isLoading&&e.isFetching&&!t,k=(e,t,r)=>(null==e?void 0:e.suspense)&&Q(t,r),T=(e,t,r)=>t.fetchOptimistic(e).then(({data:t})=>{null==e.onSuccess||e.onSuccess(t),null==e.onSettled||e.onSettled(t,null)}).catch(t=>{r.clearReset(),null==e.onError||e.onError(t),null==e.onSettled||e.onSettled(void 0,t)});function I(e,t){let r=(0,O.NL)({context:e.context}),n=C(),s=w(),i=r.defaultQueryOptions(e);i._optimisticResults=n?"isRestoring":"optimistic",i.onError&&(i.onError=u.V.batchCalls(i.onError)),i.onSuccess&&(i.onSuccess=u.V.batchCalls(i.onSuccess)),i.onSettled&&(i.onSettled=u.V.batchCalls(i.onSettled)),x(i),R(i,s),q(s);let[o]=v.useState(()=>new t(r,i)),a=o.getOptimisticResult(i);if(b(v.useCallback(e=>{let t=n?()=>void 0:o.subscribe(u.V.batchCalls(e));return o.updateResult(),t},[o,n]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),v.useEffect(()=>{o.setOptions(i,{listeners:!1})},[i,o]),k(i,a,n))throw T(i,o,s);if(P({result:a,errorResetBoundary:s,useErrorBoundary:i.useErrorBoundary,query:o.getCurrentQuery()}))throw a.error;return i.notifyOnChangeProps?a:o.trackResult(a)}var A=r(3976);class D extends c.l{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;let r=this.options;this.options=this.client.defaultMutationOptions(e),(0,a.VS)(r,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){if(!this.hasListeners()){var e;null==(e=this.currentMutation)||e.removeObserver(this)}}onMutationUpdate(e){this.updateResult();let t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){let e=this.currentMutation?this.currentMutation.state:(0,A.R)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){u.V.batch(()=>{if(this.mutateOptions&&this.hasListeners()){var t,r,n,s,i,o,a,u;e.onSuccess?(null==(t=(r=this.mutateOptions).onSuccess)||t.call(r,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(n=(s=this.mutateOptions).onSettled)||n.call(s,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context)):e.onError&&(null==(i=(o=this.mutateOptions).onError)||i.call(o,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(a=(u=this.mutateOptions).onSettled)||a.call(u,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context))}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}}function M(){}var F=r(88172);class j extends d{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,F.Gm)()},t)}getOptimisticResult(e){return e.behavior=(0,F.Gm)(),super.getOptimisticResult(e)}fetchNextPage({pageParam:e,...t}={}){return this.fetch({...t,meta:{fetchMore:{direction:"forward",pageParam:e}}})}fetchPreviousPage({pageParam:e,...t}={}){return this.fetch({...t,meta:{fetchMore:{direction:"backward",pageParam:e}}})}createResult(e,t){var r,n,s,i,o,a;let{state:u}=e,l=super.createResult(e,t),{isFetching:c,isRefetching:h}=l,d=c&&(null==(r=u.fetchMeta)?void 0:null==(n=r.fetchMore)?void 0:n.direction)==="forward",f=c&&(null==(s=u.fetchMeta)?void 0:null==(i=s.fetchMore)?void 0:i.direction)==="backward";return{...l,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,F.Qy)(t,null==(o=u.data)?void 0:o.pages),hasPreviousPage:(0,F.ZF)(t,null==(a=u.data)?void 0:a.pages),isFetchingNextPage:d,isFetchingPreviousPage:f,isRefetching:h&&!d&&!f}}}class U extends c.l{constructor(e,t){super(),this.client=e,this.queries=[],this.result=[],this.observers=[],this.observersMap={},t&&this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.observers.forEach(e=>{e.subscribe(t=>{this.onUpdate(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.observers.forEach(e=>{e.destroy()})}setQueries(e,t){this.queries=e,u.V.batch(()=>{let e=this.observers,r=this.findMatchingObservers(this.queries);r.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions,t));let n=r.map(e=>e.observer),s=Object.fromEntries(n.map(e=>[e.options.queryHash,e])),i=n.map(e=>e.getCurrentResult()),o=n.some((t,r)=>t!==e[r]);(e.length!==n.length||o)&&(this.observers=n,this.observersMap=s,this.result=i,this.hasListeners()&&((0,a.e5)(e,n).forEach(e=>{e.destroy()}),(0,a.e5)(n,e).forEach(e=>{e.subscribe(t=>{this.onUpdate(e,t)})}),this.notify()))})}getCurrentResult(){return this.result}getQueries(){return this.observers.map(e=>e.getCurrentQuery())}getObservers(){return this.observers}getOptimisticResult(e){return this.findMatchingObservers(e).map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions))}findMatchingObservers(e){let t=this.observers,r=new Map(t.map(e=>[e.options.queryHash,e])),n=e.map(e=>this.client.defaultQueryOptions(e)),s=n.flatMap(e=>{let t=r.get(e.queryHash);return null!=t?[{defaultedQueryOptions:e,observer:t}]:[]}),i=new Set(s.map(e=>e.defaultedQueryOptions.queryHash)),o=n.filter(e=>!i.has(e.queryHash)),a=new Set(s.map(e=>e.observer)),u=t.filter(e=>!a.has(e)),l=e=>{let t=this.client.defaultQueryOptions(e),r=this.observersMap[t.queryHash];return null!=r?r:new d(this.client,t)},c=o.map((e,t)=>{if(e.keepPreviousData){let r=u[t];if(void 0!==r)return{defaultedQueryOptions:e,observer:r}}return{defaultedQueryOptions:e,observer:l(e)}});return s.concat(c).sort((e,t)=>n.indexOf(e.defaultedQueryOptions)-n.indexOf(t.defaultedQueryOptions))}onUpdate(e,t){let r=this.observers.indexOf(e);-1!==r&&(this.result=(0,a.Rc)(this.result,r,t),this.notify())}notify(){u.V.batch(()=>{this.listeners.forEach(({listener:e})=>{e(this.result)})})}}function N(e,t){return e.length?void 0===t?[e]:[e,t]:[]}let L=["client","ssrContext","ssrState","abortOnUnmount"],K=(0,v.createContext)(null);function _(e,t){let[r,n]=e;return[r,n,t?.trpc]}function V(e){let{path:t}=e;return(0,v.useMemo)(()=>({path:t}),[t])}function z(e){var t;return t=function(e){let t=(e?.overrides??e?.unstable_overrides)?.useMutation?.onSuccess??(e=>e.originalFn()),r=e?.context??K,n=e?.reactQueryContext;function l(){return v.useContext(r)}function c(e,t,r){let{queryClient:n,ssrState:s}=l();return s&&"mounted"!==s&&n.getQueryCache().find(o(e,t))?.state.status==="error"?{retryOnMount:!1,...r}:r}return{Provider:e=>{let{abortOnUnmount:t=!1,client:n,queryClient:s,ssrContext:i}=e,[a,u]=(0,v.useState)(e.ssrState??!1);return(0,v.useEffect)(()=>{u(e=>!!e&&"mounted")},[]),v.createElement(r.Provider,{value:{abortOnUnmount:t,queryClient:s,client:n,ssrContext:i??null,ssrState:a,fetchQuery:(0,v.useCallback)((e,t)=>s.fetchQuery({...t,queryKey:o(e,"query"),queryFn:()=>n.query(..._(e,t))}),[n,s]),fetchInfiniteQuery:(0,v.useCallback)((e,t)=>s.fetchInfiniteQuery({...t,queryKey:o(e,"infinite"),queryFn:({pageParam:r})=>{let[s,i]=e,o={...i,cursor:r};return n.query(..._([s,o],t))}}),[n,s]),prefetchQuery:(0,v.useCallback)((e,t)=>s.prefetchQuery({...t,queryKey:o(e,"query"),queryFn:()=>n.query(..._(e,t))}),[n,s]),prefetchInfiniteQuery:(0,v.useCallback)((e,t)=>s.prefetchInfiniteQuery({...t,queryKey:o(e,"infinite"),queryFn:({pageParam:r})=>{let[s,i]=e,o={...i,cursor:r};return n.query(..._([s,o],t))}}),[n,s]),ensureQueryData:(0,v.useCallback)((e,t)=>s.ensureQueryData({...t,queryKey:o(e,"query"),queryFn:()=>n.query(..._(e,t))}),[n,s]),invalidateQueries:(0,v.useCallback)((e,t,r)=>s.invalidateQueries({...t,queryKey:o(e,"any")},r),[s]),resetQueries:(0,v.useCallback)((...e)=>{let[t,r,n]=e;return s.resetQueries({...r,queryKey:o(t,"any")},n)},[s]),refetchQueries:(0,v.useCallback)((...e)=>{let[t,r,n]=e;return s.refetchQueries({...r,queryKey:o(t,"any")},n)},[s]),cancelQuery:(0,v.useCallback)(e=>s.cancelQueries({queryKey:o(e,"any")}),[s]),setQueryData:(0,v.useCallback)((...e)=>{let[t,...r]=e;return s.setQueryData(o(t,"query"),...r)},[s]),getQueryData:(0,v.useCallback)((...e)=>{let[t,...r]=e;return s.getQueryData(o(t,"query"),...r)},[s]),setInfiniteQueryData:(0,v.useCallback)((...e)=>{let[t,...r]=e;return s.setQueryData(o(t,"infinite"),...r)},[s]),getInfiniteQueryData:(0,v.useCallback)((...e)=>{let[t,...r]=e;return s.getQueryData(o(t,"infinite"),...r)},[s])}},e.children)},createClient:e=>(0,s.BJ)(e),useContext:l,useUtils:l,useQuery:function(t,r){var s;let i=l();if(!i)throw Error("Unable to retrieve application context. Did you forget to wrap your App inside `withTRPC` HoC?");let{abortOnUnmount:u,client:h,ssrState:f,queryClient:p,prefetchQuery:y}=i,m=p.getQueryDefaults(o(t,"query"));"undefined"!=typeof window||"prepass"!==f||r?.trpc?.ssr===!1||(r?.enabled??m?.enabled)===!1||p.getQueryCache().find(o(t,"query"))||y(t,r);let v=c(t,"query",{...m,...r}),b=r?.trpc?.abortOnUnmount??e?.abortOnUnmount??u,g=(s={...v,queryKey:o(t,"query"),queryFn:e=>{let r={...v,trpc:{...v?.trpc,...b?{signal:e.signal}:{}}};return h.query(..._(t,r))},context:n},I((0,a._v)(s,void 0,void 0),d));return g.trpc=V({path:t[0]}),g},useQueries:(e,t)=>{let{ssrState:r,queryClient:n,prefetchQuery:s,client:a}=l(),c=e((0,i.IX)(e=>{let t=e.path.join("."),[r,n]=e.args;return{queryKey:N(t,r),queryFn:()=>a.query(t,r,n?.trpc),...n}}));if("undefined"==typeof window&&"prepass"===r)for(let e of c)e.trpc?.ssr===!1||n.getQueryCache().find(o(e.queryKey,"query"))||s(e.queryKey,e);return function(e){let{queries:t,context:r}=e,n=(0,O.NL)({context:r}),s=C(),i=w(),o=v.useMemo(()=>t.map(e=>{let t=n.defaultQueryOptions(e);return t._optimisticResults=s?"isRestoring":"optimistic",t}),[t,n,s]);o.forEach(e=>{x(e),R(e,i)}),q(i);let[a]=v.useState(()=>new U(n,o)),l=a.getOptimisticResult(o);b(v.useCallback(e=>s?()=>void 0:a.subscribe(u.V.batchCalls(e)),[a,s]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),v.useEffect(()=>{a.setQueries(o,{listeners:!1})},[o,a]);let c=l.some((e,t)=>k(o[t],e,s))?l.flatMap((e,t)=>{let r=o[t],n=a.getObservers()[t];if(r&&n){if(k(r,e,s))return T(r,n,i);Q(e,s)&&T(r,n,i)}return[]}):[];if(c.length>0)throw Promise.all(c);let h=a.getQueries(),d=l.find((e,t)=>{var r,n;return P({result:e,errorResetBoundary:i,useErrorBoundary:null!=(r=null==(n=o[t])?void 0:n.useErrorBoundary)&&r,query:h[t]})});if(null!=d&&d.error)throw d.error;return l}({queries:c.map(e=>({...e,queryKey:o(e.queryKey,"query")})),context:t})},useMutation:function(e,r){let{client:s}=l(),i=(0,O.NL)({context:n}),o=Array.isArray(e)?e[0]:e,c=i.getMutationDefaults([o.split(".")]),h=function(e,t,r){let n=(0,a.lV)(e,void 0,void 0),s=(0,O.NL)({context:n.context}),[i]=v.useState(()=>new D(s,n));v.useEffect(()=>{i.setOptions(n)},[i,n]);let o=b(v.useCallback(e=>i.subscribe(u.V.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),l=v.useCallback((e,t)=>{i.mutate(e,t).catch(M)},[i]);if(o.error&&S(i.options.useErrorBoundary,[o.error]))throw o.error;return{...o,mutate:l,mutateAsync:o.mutate}}({...r,mutationKey:[o.split(".")],mutationFn:e=>s.mutation(..._([o,e],r)),context:n,onSuccess:(...e)=>t({originalFn:()=>r?.onSuccess?.(...e)??c?.onSuccess?.(...e),queryClient:i,meta:r?.meta??c?.meta??{}})});return h.trpc=V({path:o}),h},useSubscription:/* istanbul ignore next -- @preserve */function(e,t){let r=t?.enabled??!0,n=(0,a.yF)(e),{client:s}=l(),i=(0,v.useRef)(t);i.current=t,(0,v.useEffect)(()=>{if(!r)return;let[t,n]=e,o=!1,a=s.subscription(t,n??void 0,{onStarted:()=>{o||i.current.onStarted?.()},onData:e=>{o||i.current.onData(e)},onError:e=>{o||i.current.onError?.(e)}});return()=>{o=!0,a.unsubscribe()}},[n,r])},useDehydratedState:(e,t)=>(0,v.useMemo)(()=>t?e.runtime.transformer.deserialize(t):t,[t,e]),useInfiniteQuery:function(e,t){var r;let[s,i]=e,{client:u,ssrState:h,prefetchInfiniteQuery:d,queryClient:f,abortOnUnmount:p}=l(),y=f.getQueryDefaults(o(e,"infinite"));"undefined"!=typeof window||"prepass"!==h||t?.trpc?.ssr===!1||(t?.enabled??y?.enabled)===!1||f.getQueryCache().find(o(e,"infinite"))||d(e,{...y,...t});let m=c(e,"infinite",{...y,...t}),v=t?.trpc?.abortOnUnmount??p,b=(r={...m,queryKey:o(e,"infinite"),queryFn:e=>{let r={...m,trpc:{...m?.trpc,...v?{signal:e.signal}:{}}},n={...i??{},cursor:e.pageParam??t?.initialCursor};return u.query(..._([s,n],r))},context:n},I((0,a._v)(r,void 0,void 0),j));return b.trpc=V({path:s}),b}}}(e),(0,i.yh)(e=>"useContext"===e||"useUtils"===e?()=>{let e=t.useUtils();return(0,v.useMemo)(()=>(0,i.yh)(t=>"client"===t?(0,s.Bm)(e.client):L.includes(t)?e[t]:(0,i.IX)(({path:r,args:n})=>{let s=[t,...r],i=s.pop(),o=s.join("."),{queryKey:a,rest:u,updater:l}=(e=>{if(["setData","setInfiniteData"].includes(e)){let[e,t,...r]=n;return{queryKey:N(o,e),updater:t,rest:r}}let[t,...r]=n;return{queryKey:N(o,t),rest:r}})(i);return({fetch:()=>e.fetchQuery(a,...u),fetchInfinite:()=>e.fetchInfiniteQuery(a,...u),prefetch:()=>e.prefetchQuery(a,...u),prefetchInfinite:()=>e.prefetchInfiniteQuery(a,...u),ensureData:()=>e.ensureQueryData(a,...u),invalidate:()=>e.invalidateQueries(a,...u),reset:()=>e.resetQueries(a,...u),refetch:()=>e.refetchQueries(a,...u),cancel:()=>e.cancelQuery(a,...u),setData:()=>{e.setQueryData(a,l,...u)},setInfiniteData:()=>{e.setInfiniteQueryData(a,l,...u)},getData:()=>e.getQueryData(a),getInfiniteData:()=>e.getInfiniteQueryData(a)})[i]()})),[e])}:t.hasOwnProperty(e)?t[e]:(0,i.IX)(r=>{let n=r.args,s=[e,...r.path],i=s.pop(),a=s.join(".");if("useMutation"===i)return t[i](a,...n);let[u,...l]=n,c=N(a,u);if("getQueryKey"===i)return o(c,l[0]??"any");if("_def"===i)return{path:s};if(i.startsWith("useSuspense")){let e=l[0]||{},r=t["useSuspenseQuery"===i?"useQuery":"useInfiniteQuery"](c,{...e,suspense:!0,enabled:!0});return[r.data,r]}return t[i](c,...l)}))}},88106:function(e,t,r){function n(e){let t=Object.create(null);for(let r in e)t[e[r]]=r;return t}r.d(t,{yh:function(){return a},IX:function(){return o},sZ:function(){return l}});let s={PARSE_ERROR:-32700,BAD_REQUEST:-32600,INTERNAL_SERVER_ERROR:-32603,NOT_IMPLEMENTED:-32603,UNAUTHORIZED:-32001,FORBIDDEN:-32003,NOT_FOUND:-32004,METHOD_NOT_SUPPORTED:-32005,TIMEOUT:-32008,CONFLICT:-32009,PRECONDITION_FAILED:-32012,PAYLOAD_TOO_LARGE:-32013,UNPROCESSABLE_CONTENT:-32022,TOO_MANY_REQUESTS:-32029,CLIENT_CLOSED_REQUEST:-32099};n(s),n(s);let i=()=>{},o=e=>(function e(t,r){return new Proxy(i,{get(n,s){if("string"==typeof s&&"then"!==s)return e(t,[...r,s])},apply(e,n,s){let i="apply"===r[r.length-1];return t({args:i?s.length>=2?s[1]:[]:s,path:i?r.slice(0,-1):r})}})})(e,[]),a=e=>new Proxy(i,{get(t,r){if("string"==typeof r&&"then"!==r)return e(r)}});class u extends Error{}function l(e){if(e instanceof Error)return e;let t=typeof e;if("undefined"!==t&&"function"!==t&&null!==e){if("object"!==t)return Error(String(e));if(e&&!Array.isArray(e)&&"object"==typeof e){let t=new u;for(let r in e)t[r]=e[r];return t}}}},51164:function(e,t,r){r.d(t,{c:function(){return l}});var n=r(2265),s=r(25566),i=()=>{window.va||(window.va=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];(window.vaq=window.vaq||[]).push(t)})};function o(){return"undefined"!=typeof window}function a(){return"production"}function u(){return"development"===((o()?window.vam:a())||"production")}function l(e){return(0,n.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]),(0,n.useEffect)(()=>{var t;!function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debug:!0};if(!o())return;(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto";if("auto"===e){window.vam=a();return}window.vam=e})(t.mode),i(),t.beforeSend&&(null==(e=window.va)||e.call(window,"beforeSend",t.beforeSend));let r=t.scriptSrc?t.scriptSrc:u()?"https://va.vercel-scripts.com/v1/script.debug.js":t.basePath?"".concat(t.basePath,"/insights/script.js"):"/_vercel/insights/script.js";if(document.head.querySelector('script[src*="'.concat(r,'"]')))return;let n=document.createElement("script");n.src=r,n.defer=!0,n.dataset.sdkn="@vercel/analytics"+(t.framework?"/".concat(t.framework):""),n.dataset.sdkv="1.5.0",t.disableAutoTrack&&(n.dataset.disableAutoTrack="1"),t.endpoint?n.dataset.endpoint=t.endpoint:t.basePath&&(n.dataset.endpoint="".concat(t.basePath,"/insights")),t.dsn&&(n.dataset.dsn=t.dsn),n.onerror=()=>{u()},u()&&!1===t.debug&&(n.dataset.debug="false"),document.head.appendChild(n)}({framework:e.framework||"react",basePath:null!==(t=e.basePath)&&void 0!==t?t:function(){if(void 0!==s&&void 0!==s.env)return s.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==e.route&&{disableAutoTrack:!0},...e})},[]),(0,n.useEffect)(()=>{e.route&&e.path&&function(e){var t;let{route:r,path:n}=e;null==(t=window.va)||t.call(window,"pageview",{route:r,path:n})}({route:e.route,path:e.path})},[e.route,e.path]),null}},79512:function(e,t,r){r.d(t,{F:function(){return c},ThemeProvider:function(){return h}});var n=r(2265),s=(e,t,r,n,s,i,o,a)=>{let u=document.documentElement,l=["light","dark"];function c(t){(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&i?s.map(e=>i[e]||e):s;r?(u.classList.remove(...n),u.classList.add(i&&i[t]?i[t]:t)):u.setAttribute(e,t)}),a&&l.includes(t)&&(u.style.colorScheme=t)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},i=["light","dark"],o="(prefers-color-scheme: dark)",a="undefined"==typeof window,u=n.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(u))?e:l},h=e=>n.useContext(u)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:a=!0,storageKey:l="theme",themes:c=d,defaultTheme:h=s?"system":"light",attribute:f="data-theme",value:b,children:g,nonce:w,scriptProps:O}=e,[E,C]=n.useState(()=>y(l,h)),[S,R]=n.useState(()=>"system"===E?v():E),q=b?Object.values(b):c,P=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=v());let n=b?b[t]:t,o=r?m(w):null,u=document.documentElement,l=e=>{"class"===e?(u.classList.remove(...q),n&&u.classList.add(n)):e.startsWith("data-")&&(n?u.setAttribute(e,n):u.removeAttribute(e))};if(Array.isArray(f)?f.forEach(l):l(f),a){let e=i.includes(h)?h:null,r=i.includes(t)?t:e;u.style.colorScheme=r}null==o||o()},[w]),x=n.useCallback(e=>{let t="function"==typeof e?e(E):e;C(t);try{localStorage.setItem(l,t)}catch(e){}},[E]),Q=n.useCallback(e=>{R(v(e)),"system"===E&&s&&!t&&P("system")},[E,t]);n.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(Q),Q(e),()=>e.removeListener(Q)},[Q]),n.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?C(e.newValue):x(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[x]),n.useEffect(()=>{P(null!=t?t:E)},[t,E]);let k=n.useMemo(()=>({theme:E,setTheme:x,forcedTheme:t,resolvedTheme:"system"===E?S:E,themes:s?[...c,"system"]:c,systemTheme:s?S:void 0}),[E,x,t,S,s,c]);return n.createElement(u.Provider,{value:k},n.createElement(p,{forcedTheme:t,storageKey:l,attribute:f,enableSystem:s,enableColorScheme:a,defaultTheme:h,value:b,themes:c,nonce:w,scriptProps:O}),g)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:i,enableSystem:o,enableColorScheme:a,defaultTheme:u,value:l,themes:c,nonce:h,scriptProps:d}=e,f=JSON.stringify([i,r,u,t,c,l,o,a]).slice(1,-1);return n.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?h:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(f,")")}})}),y=(e,t)=>{let r;if(!a){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},16865:function(e,t,r){var n,s;r.d(t,{ZP:function(){return $}});class i{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class o{constructor(e){this.generateIdentifier=e,this.kv=new i}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class a extends o{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){"object"==typeof t?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function u(e,t){Object.entries(e).forEach(([e,r])=>t(r,e))}function l(e,t){return -1!==e.indexOf(t)}function c(e,t){for(let r=0;r<e.length;r++){let n=e[r];if(t(n))return n}}class h{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return function(e,t){let r=function(e){if("values"in Object)return Object.values(e);let t=[];for(let r in e)e.hasOwnProperty(r)&&t.push(e[r]);return t}(e);if("find"in r)return r.find(t);for(let e=0;e<r.length;e++){let n=r[e];if(t(n))return n}}(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}}let d=e=>Object.prototype.toString.call(e).slice(8,-1),f=e=>void 0===e,p=e=>null===e,y=e=>"object"==typeof e&&null!==e&&e!==Object.prototype&&(null===Object.getPrototypeOf(e)||Object.getPrototypeOf(e)===Object.prototype),m=e=>y(e)&&0===Object.keys(e).length,v=e=>Array.isArray(e),b=e=>"string"==typeof e,g=e=>"number"==typeof e&&!isNaN(e),w=e=>"boolean"==typeof e,O=e=>e instanceof Map,E=e=>e instanceof Set,C=e=>"Symbol"===d(e),S=e=>"number"==typeof e&&isNaN(e),R=e=>w(e)||p(e)||f(e)||g(e)||b(e)||C(e),q=e=>e===1/0||e===-1/0,P=e=>e.replace(/\./g,"\\."),x=e=>e.map(String).map(P).join("."),Q=e=>{let t=[],r="";for(let n=0;n<e.length;n++){let s=e.charAt(n);if("\\"===s&&"."===e.charAt(n+1)){r+=".",n++;continue}if("."===s){t.push(r),r="";continue}r+=s}let n=r;return t.push(n),t};function k(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let T=[k(f,"undefined",()=>null,()=>void 0),k(e=>"bigint"==typeof e,"bigint",e=>e.toString(),e=>"undefined"!=typeof BigInt?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),k(e=>e instanceof Date&&!isNaN(e.valueOf()),"Date",e=>e.toISOString(),e=>new Date(e)),k(e=>e instanceof Error,"Error",(e,t)=>{let r={name:e.name,message:e.message};return t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r},(e,t)=>{let r=Error(e.message);return r.name=e.name,r.stack=e.stack,t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r}),k(e=>e instanceof RegExp,"regexp",e=>""+e,e=>new RegExp(e.slice(1,e.lastIndexOf("/")),e.slice(e.lastIndexOf("/")+1))),k(E,"set",e=>[...e.values()],e=>new Set(e)),k(O,"map",e=>[...e.entries()],e=>new Map(e)),k(e=>S(e)||q(e),"number",e=>S(e)?"NaN":e>0?"Infinity":"-Infinity",Number),k(e=>0===e&&1/e==-1/0,"number",()=>"-0",Number),k(e=>e instanceof URL,"URL",e=>e.toString(),e=>new URL(e))];function I(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let A=I((e,t)=>!!C(e)&&!!t.symbolRegistry.getIdentifier(e),(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,r)=>{let n=r.symbolRegistry.getValue(t[1]);if(!n)throw Error("Trying to deserialize unknown symbol");return n}),D=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),M=I(e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{let r=D[t[1]];if(!r)throw Error("Trying to deserialize unknown typed array");return new r(e)});function F(e,t){return!!e?.constructor&&!!t.classRegistry.getIdentifier(e.constructor)}let j=I(F,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{let r=t.classRegistry.getAllowedProps(e.constructor);if(!r)return{...e};let n={};return r.forEach(t=>{n[t]=e[t]}),n},(e,t,r)=>{let n=r.classRegistry.getValue(t[1]);if(!n)throw Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(n.prototype),e)}),U=I((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,r)=>{let n=r.customTransformerRegistry.findByName(t[1]);if(!n)throw Error("Trying to deserialize unknown custom value");return n.deserialize(e)}),N=[j,A,U,M],L=(e,t)=>{let r=c(N,r=>r.isApplicable(e,t));if(r)return{value:r.transform(e,t),type:r.annotation(e,t)};let n=c(T,r=>r.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation}},K={};T.forEach(e=>{K[e.annotation]=e});let _=(e,t,r)=>{if(v(t))switch(t[0]){case"symbol":return A.untransform(e,t,r);case"class":return j.untransform(e,t,r);case"custom":return U.untransform(e,t,r);case"typed-array":return M.untransform(e,t,r);default:throw Error("Unknown transformation: "+t)}else{let n=K[t];if(!n)throw Error("Unknown transformation: "+t);return n.untransform(e,r)}},V=(e,t)=>{if(t>e.size)throw Error("index out of bounds");let r=e.keys();for(;t>0;)r.next(),t--;return r.next().value};function z(e){if(l(e,"__proto__"))throw Error("__proto__ is not allowed as a property");if(l(e,"prototype"))throw Error("prototype is not allowed as a property");if(l(e,"constructor"))throw Error("constructor is not allowed as a property")}let H=(e,t)=>{z(t);for(let r=0;r<t.length;r++){let n=t[r];if(E(e))e=V(e,+n);else if(O(e)){let s=+n,i=0==+t[++r]?"key":"value",o=V(e,s);switch(i){case"key":e=o;break;case"value":e=e.get(o)}}else e=e[n]}return e},B=(e,t,r)=>{if(z(t),0===t.length)return r(e);let n=e;for(let e=0;e<t.length-1;e++){let r=t[e];if(v(n))n=n[+r];else if(y(n))n=n[r];else if(E(n))n=V(n,+r);else if(O(n)){if(e===t.length-2)break;let s=+r,i=0==+t[++e]?"key":"value",o=V(n,s);switch(i){case"key":n=o;break;case"value":n=n.get(o)}}}let s=t[t.length-1];if(v(n)?n[+s]=r(n[+s]):y(n)&&(n[s]=r(n[s])),E(n)){let e=V(n,+s),t=r(e);e!==t&&(n.delete(e),n.add(t))}if(O(n)){let e=V(n,+t[t.length-2]);switch(0==+s?"key":"value"){case"key":{let t=r(e);n.set(t,n.get(e)),t!==e&&n.delete(e);break}case"value":n.set(e,r(n.get(e)))}}return e},Z=(e,t)=>y(e)||v(e)||O(e)||E(e)||F(e,t),G=(e,t,r,n,s=[],i=[],o=new Map)=>{let a=R(e);if(!a){!function(e,t,r){let n=r.get(e);n?n.push(t):r.set(e,[t])}(e,s,t);let r=o.get(e);if(r)return n?{transformedValue:null}:r}if(!Z(e,r)){let t=L(e,r),n=t?{transformedValue:t.value,annotations:[t.type]}:{transformedValue:e};return a||o.set(e,n),n}if(l(i,e))return{transformedValue:null};let c=L(e,r),h=c?.value??e,d=v(h)?[]:{},f={};u(h,(a,l)=>{if("__proto__"===l||"constructor"===l||"prototype"===l)throw Error(`Detected property ${l}. This is a prototype pollution risk, please remove it from your object.`);let c=G(a,t,r,n,[...s,l],[...i,e],o);d[l]=c.transformedValue,v(c.annotations)?f[l]=c.annotations:y(c.annotations)&&u(c.annotations,(e,t)=>{f[P(l)+"."+t]=e})});let p=m(f)?{transformedValue:d,annotations:c?[c.type]:void 0}:{transformedValue:d,annotations:c?[c.type,f]:f};return a||o.set(e,p),p};function W(e){return Object.prototype.toString.call(e).slice(8,-1)}function J(e){return"Array"===W(e)}n=function(e){return"Null"===W(e)},s=function(e){return"Undefined"===W(e)},e=>n(e)||s(e)||!1;class ${constructor({dedupe:e=!1}={}){this.classRegistry=new a,this.symbolRegistry=new o(e=>e.description??""),this.customTransformerRegistry=new h,this.allowedErrorProps=[],this.dedupe=e}serialize(e){let t=new Map,r=G(e,t,this,this.dedupe),n={json:r.transformedValue};r.annotations&&(n.meta={...n.meta,values:r.annotations});let s=function(e,t){let r;let n={};return(e.forEach(e=>{if(e.length<=1)return;t||(e=e.map(e=>e.map(String)).sort((e,t)=>e.length-t.length));let[s,...i]=e;0===s.length?r=i.map(x):n[x(s)]=i.map(x)}),r)?m(n)?[r]:[r,n]:m(n)?void 0:n}(t,this.dedupe);return s&&(n.meta={...n.meta,referentialEqualities:s}),n}deserialize(e){let{json:t,meta:r}=e,n=function e(t,r={}){return J(t)?t.map(t=>e(t,r)):!function(e){if("Object"!==W(e))return!1;let t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}(t)?t:[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)].reduce((n,s)=>{if(J(r.props)&&!r.props.includes(s))return n;let i=e(t[s],r);return!function(e,t,r,n,s){let i=({}).propertyIsEnumerable.call(n,t)?"enumerable":"nonenumerable";"enumerable"===i&&(e[t]=r),s&&"nonenumerable"===i&&Object.defineProperty(e,t,{value:r,enumerable:!1,writable:!0,configurable:!0})}(n,s,i,t,r.nonenumerable),n},{})}(t);if(r?.values){var s,i,o;s=n,i=r.values,o=this,function e(t,r,n=[]){if(!t)return;if(!v(t)){u(t,(t,s)=>e(t,r,[...n,...Q(s)]));return}let[s,i]=t;i&&u(i,(t,s)=>{e(t,r,[...n,...Q(s)])}),r(s,n)}(i,(e,t)=>{s=B(s,t,t=>_(t,e,o))}),n=s}return r?.referentialEqualities&&(n=function(e,t){function r(t,r){let n=H(e,Q(r));t.map(Q).forEach(t=>{e=B(e,t,()=>n)})}if(v(t)){let[n,s]=t;n.forEach(t=>{e=B(e,Q(t),()=>e)}),s&&u(s,r)}else u(t,r);return e}(n,r.referentialEqualities)),n}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}$.defaultInstance=new $,$.serialize=$.defaultInstance.serialize.bind($.defaultInstance),$.deserialize=$.defaultInstance.deserialize.bind($.defaultInstance),$.stringify=$.defaultInstance.stringify.bind($.defaultInstance),$.parse=$.defaultInstance.parse.bind($.defaultInstance),$.registerClass=$.defaultInstance.registerClass.bind($.defaultInstance),$.registerSymbol=$.defaultInstance.registerSymbol.bind($.defaultInstance),$.registerCustom=$.defaultInstance.registerCustom.bind($.defaultInstance),$.allowErrorProps=$.defaultInstance.allowErrorProps.bind($.defaultInstance),$.serialize,$.deserialize,$.stringify,$.parse,$.registerClass,$.registerCustom,$.registerSymbol,$.allowErrorProps}}]);