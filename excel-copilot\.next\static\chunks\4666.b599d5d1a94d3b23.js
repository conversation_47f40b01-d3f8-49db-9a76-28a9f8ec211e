"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4666],{84666:function(e,t,n){n.r(t),n.d(t,{CSRFProvider:function(){return s},useCSRF:function(){return c},useFetchWithCSRF:function(){return i}});var r=n(57437),o=n(2265);let a=(0,o.createContext)({csrfToken:null,isLoading:!0,refreshToken:async()=>null}),c=()=>(0,o.useContext)(a);function s(e){let{children:t}=e,[n,c]=(0,o.useState)(null),[s,i]=(0,o.useState)(!0),u=async()=>{try{i(!0);let e=0;for(;e<3;)try{e++;let t=await fetch("/api/csrf",{method:"GET",credentials:"include"});if(t.ok){let e=await t.json();return c(e.csrfToken),i(!1),e.csrfToken}if(console.warn("Tentativa ".concat(e,"/").concat(3," falhou ao obter token CSRF: ").concat(t.status)),e<3){await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e-1)));continue}throw Error("Falha ao obter token CSRF: ".concat(t.status))}catch(t){if(e>=3)throw t;await new Promise(t=>setTimeout(t,1e3*Math.pow(2,e-1)))}throw Error("M\xe1ximo de tentativas excedido")}catch(e){return console.error("Erro ao obter token CSRF:",e),i(!1),null}};return(0,o.useEffect)(()=>{u()},[]),(0,r.jsx)(a.Provider,{value:{csrfToken:n,isLoading:s,refreshToken:u},children:t})}function i(){let{csrfToken:e,isLoading:t,refreshToken:n}=c();return{fetchWithCSRF:async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t&&await new Promise(e=>setTimeout(e,100));let a=new Headers(o.headers||{});if(e)a.set("x-csrf-token",e);else{let e=await n();e&&a.set("x-csrf-token",e)}return fetch(r,{...o,credentials:"include",headers:a})},csrfToken:e,isLoading:t}}}}]);