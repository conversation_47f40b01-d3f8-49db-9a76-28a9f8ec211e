"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4930],{64930:function(e,o,r){r.d(o,{prisma:function(){return u}});var n=r(17090),a=r(25566);let t={info:function(e){for(var o=arguments.length,r=Array(o>1?o-1:0),n=1;n<o;n++)r[n-1]=arguments[n]},error:function(e){for(var o=arguments.length,r=Array(o>1?o-1:0),n=1;n<o;n++)r[n-1]=arguments[n];console.error("[DB ERROR] ".concat(e),...r)},warn:function(e){for(var o=arguments.length,r=Array(o>1?o-1:0),n=1;n<o;n++)r[n-1]=arguments[n];console.warn("[DB WARNING] ".concat(e),...r)}},c={totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null},i=[],u=r.g.prisma||new n.PrismaClient({log:["error"],datasources:{db:{url:a.env.DB_DATABASE_URL||""}}});async function s(){try{await u.$disconnect(),t.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){t.error("Erro ao desconectar do banco de dados",e)}}u.$on("query",e=>{c.totalQueries++,e.duration&&(i.push(e.duration),i.length>100&&i.shift(),c.averageQueryTime=i.reduce((e,o)=>e+o,0)/i.length),e.duration&&e.duration>500&&t.warn("Consulta lenta detectada: ".concat(Math.round(e.duration),"ms - Query: ").concat(e.query||"Query desconhecida"))}),u.$on("error",e=>{c.failedQueries++,c.connectionFailures++,c.lastConnectionFailure=new Date().toISOString(),t.error("Erro na conex\xe3o com o banco de dados: ".concat(e.message||"Erro desconhecido"))}),void 0!==a&&a.on("beforeExit",()=>{s()})}}]);