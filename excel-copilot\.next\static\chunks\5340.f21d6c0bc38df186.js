"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5340],{78068:function(t,e,a){a.d(e,{V6:function(){return m},pm:function(){return T},s6:function(){return d}});var o=a(2265);let n={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},r=0,s=new Map,i=t=>{if(s.has(t))return;let e=setTimeout(()=>{s.delete(t),p({type:n.REMOVE_TOAST,toastId:t})},1e6);s.set(t,e)},l=(t,e)=>{switch(e.type){case n.ADD_TOAST:return{...t,toasts:[e.toast,...t.toasts].slice(0,5)};case n.UPDATE_TOAST:return{...t,toasts:t.toasts.map(t=>{var a;return t.id===(null===(a=e.toast)||void 0===a?void 0:a.id)?{...t,...e.toast}:t})};case n.DISMISS_TOAST:{let{toastId:a}=e;return a?i(a):t.toasts.forEach(t=>{i(t.id)}),{...t,toasts:t.toasts.map(t=>t.id===a||void 0===a?{...t,open:!1}:t)}}case n.REMOVE_TOAST:if(void 0===e.toastId)return{...t,toasts:[]};return{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)};default:return t}},c=[],u={toasts:[]};function p(t){u=l(u,t),c.forEach(t=>{t(u)})}function d(t){let{...e}=t,a=(r=(r+1)%Number.MAX_VALUE).toString(),o=()=>p({type:n.DISMISS_TOAST,toastId:a});return p({type:n.ADD_TOAST,toast:{...e,id:a,open:!0,onOpenChange:t=>{t||o()}}}),{id:a,dismiss:o,update:t=>p({type:n.UPDATE_TOAST,toast:{...t,id:a}})}}function m(){let[t,e]=o.useState(u);return o.useEffect(()=>(c.push(e),()=>{let t=c.indexOf(e);t>-1&&c.splice(t,1)}),[t]),{toast:d,dismiss:t=>p({type:n.DISMISS_TOAST,toastId:void 0===t?"":t}),toasts:t.toasts}}let T=m},45340:function(t,e,a){a.r(e),a.d(e,{TourProvider:function(){return c},getTours:function(){return u},useTour:function(){return l}});var o=a(57437),n=a(16463),r=a(2265),s=a(78068);let i=(0,r.createContext)({currentTour:null,currentStep:0,startTour:()=>{},endTour:()=>{},nextStep:()=>{},prevStep:()=>{},isTourActive:!1,shouldShowTour:()=>!1,markTourCompleted:()=>{}}),l=()=>(0,r.useContext)(i);function c(t){let{children:e}=t,[a,l]=(0,r.useState)(null),[c,p]=(0,r.useState)(0),[d,m]=(0,r.useState)([]);(0,n.useRouter)();let{toast:T}=(0,s.pm)();(0,r.useEffect)(()=>{{let t=localStorage.getItem("excel-copilot-completed-tours");if(t)try{m(JSON.parse(t))}catch(t){console.error("Erro ao recuperar tours completados:",t)}}},[]);let S=t=>{localStorage.setItem("excel-copilot-completed-tours",JSON.stringify(t))},f=()=>{a&&h(a),l(null),p(0)},h=t=>{if(!d.includes(t)){let e=[...d,t];m(e),S(e),T({title:"Tour completado",description:"Voc\xea concluiu o tour com sucesso!"})}},g=null!==a;return(0,o.jsx)(i.Provider,{value:{currentTour:a,currentStep:c,startTour:t=>{l(t),p(0),T({title:"Tour iniciado",description:"Vamos conhecer as principais funcionalidades do Excel Copilot"})},endTour:f,nextStep:()=>{let t=u();a&&c<t[a].length-1?p(c+1):f()},prevStep:()=>{c>0&&p(c-1)},isTourActive:g,shouldShowTour:t=>!d.includes(t),markTourCompleted:h},children:e})}function u(){return{welcome:[{target:"#welcome-header",title:"Bem-vindo ao Excel Copilot",content:"Este assistente vai ajudar voc\xea a explorar as funcionalidades do Excel usando linguagem natural.",placement:"bottom"},{target:"#dashboard-button",title:"Painel Principal",content:"Acesse suas planilhas recentes e crie novas a partir daqui.",placement:"right"},{target:"#create-button",title:"Crie Planilhas",content:"Voc\xea pode criar uma nova planilha do zero ou fazer upload de uma existente.",placement:"left"}],workbook:[{target:"#chat-tab",title:"Conversa\xe7\xe3o com IA",content:"Nesta aba voc\xea pode dar comandos em linguagem natural para manipular sua planilha.",placement:"bottom"},{target:"#spreadsheet-container",title:"Visualiza\xe7\xe3o da Planilha",content:"Aqui voc\xea v\xea os resultados em tempo real enquanto trabalha com sua planilha.",placement:"top"},{target:"#command-examples",title:"Exemplos de Comandos",content:"Use esses exemplos para come\xe7ar. Basta clicar em um deles para usar no chat.",placement:"left"}],dashboard:[{target:"#recent-workbooks",title:"Planilhas Recentes",content:"Acesse rapidamente suas planilhas mais recentes aqui.",placement:"bottom"},{target:"#templates-section",title:"Templates Prontos",content:"Use um de nossos templates para come\xe7ar rapidamente.",placement:"top"},{target:"#actions-menu",title:"A\xe7\xf5es R\xe1pidas",content:"Acesse rapidamente a\xe7\xf5es como criar, importar ou exportar planilhas.",placement:"left"}],chat:[{target:"#chat-input",title:"Comandos em Linguagem Natural",content:"Digite comandos como 'Crie um gr\xe1fico de vendas por regi\xe3o' ou perguntas como 'Qual o total de vendas em 2023?'",placement:"top"},{target:"#command-categories",title:"Categorias de Comandos",content:"Explore diferentes tipos de comandos organizados por categoria.",placement:"right"},{target:"#operations-indicator",title:"Opera\xe7\xf5es Executadas",content:"Veja quais opera\xe7\xf5es foram executadas na sua planilha ap\xf3s cada comando.",placement:"bottom"}]}}}}]);