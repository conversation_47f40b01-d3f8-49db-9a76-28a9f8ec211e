"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5702],{75702:function(e,t,r){r.r(t),r.d(t,{VercelClient:function(){return a},VercelMonitoringService:function(){return s}});var o=r(18473);class a{async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseUrl).concat(e),a={Authorization:"Bearer ".concat(this.apiToken),"Content-Type":"application/json"};t.headers&&Object.assign(a,t.headers),this.teamId&&(a["X-Vercel-Team-Id"]=this.teamId);try{let e=await fetch(r,{...t,headers:a});if(!e.ok){let t=await e.text();throw Error("Vercel API error: ".concat(e.status," - ").concat(t))}return await e.json()}catch(t){throw o.logger.error("Erro na requisi\xe7\xe3o Vercel API",{endpoint:e,error:t}),t}}async getDeployments(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=this.projectId?"/v6/deployments?projectId=".concat(this.projectId,"&limit=").concat(e):"/v6/deployments?limit=".concat(e);return(await this.makeRequest(t)).deployments}async getDeployment(e){return await this.makeRequest("/v13/deployments/".concat(e))}async getDeploymentLogs(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r=new URLSearchParams;t.since&&r.append("since",t.since.toString()),t.until&&r.append("until",t.until.toString()),t.limit&&r.append("limit",t.limit.toString()),t.direction&&r.append("direction",t.direction);let o="/v2/deployments/".concat(e,"/events?").concat(r.toString());return(await this.makeRequest(o)).events||[]}catch(t){return o.logger.error("Erro ao obter logs do deployment ".concat(e),t),[]}}async getProject(e){let t=e||this.projectId;if(!t)throw Error("Project ID \xe9 obrigat\xf3rio");return await this.makeRequest("/v9/projects/".concat(t))}async getAnalytics(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.projectId)throw Error("Project ID \xe9 obrigat\xf3rio para analytics");let t=new URLSearchParams;e.since&&t.append("since",e.since.toString()),e.until&&t.append("until",e.until.toString()),e.granularity&&t.append("granularity",e.granularity);let r="/v1/analytics?projectId=".concat(this.projectId,"&").concat(t.toString());return(await this.makeRequest(r)).metrics}async getEnvironmentVariables(){if(!this.projectId)throw Error("Project ID \xe9 obrigat\xf3rio");let e="/v9/projects/".concat(this.projectId,"/env");return(await this.makeRequest(e)).envs}async setEnvironmentVariable(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:["production","preview"],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"encrypted";if(!this.projectId)throw Error("Project ID \xe9 obrigat\xf3rio");try{let o="/v9/projects/".concat(this.projectId,"/env");return await this.makeRequest(o,{method:"POST",body:JSON.stringify({key:e,value:t,target:r,type:a})}),{success:!0,message:"Vari\xe1vel ".concat(e," configurada com sucesso")}}catch(t){return o.logger.error("Erro ao configurar vari\xe1vel ".concat(e),t),{success:!1,message:"Erro ao configurar ".concat(e,": ").concat(t instanceof Error?t.message:"Erro desconhecido")}}}async deleteEnvironmentVariable(e){if(!this.projectId)throw Error("Project ID \xe9 obrigat\xf3rio");try{let t="/v9/projects/".concat(this.projectId,"/env/").concat(e);return await this.makeRequest(t,{method:"DELETE"}),{success:!0,message:"Vari\xe1vel removida com sucesso"}}catch(t){return o.logger.error("Erro ao remover vari\xe1vel ".concat(e),t),{success:!1,message:"Erro ao remover vari\xe1vel: ".concat(t instanceof Error?t.message:"Erro desconhecido")}}}async setMultipleEnvironmentVariables(e){let t=[],r=0,o=0,a=0,s=(await this.getEnvironmentVariables()).map(e=>e.key);for(let i of e)try{if(s.includes(i.key)){t.push({key:i.key,success:!0,message:"J\xe1 existe (pulado)"}),a++;continue}let e=await this.setEnvironmentVariable(i.key,i.value,i.target,i.type);t.push({key:i.key,success:e.success,message:e.message}),e.success?r++:o++,await new Promise(e=>setTimeout(e,200))}catch(e){t.push({key:i.key,success:!1,message:"Erro: ".concat(e instanceof Error?e.message:"Erro desconhecido")}),o++}return{success:0===o,results:t,summary:{configured:r,failed:o,skipped:a}}}async getBuildStatus(e){let t=await this.getDeployment(e),r=(await this.getDeploymentLogs(e,{limit:100})).filter(e=>"build"===e.source).map(e=>e.message),o=t.ready&&t.buildingAt?t.ready-t.buildingAt:void 0;return{status:t.state,logs:r,duration:o}}async checkRecentErrors(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:24,t=Date.now()-36e5*e,r=(await this.getDeployments(50)).filter(e=>"ERROR"===e.state&&e.created>t),a=[];for(let e of r)try{let t=(await this.getDeploymentLogs(e.uid,{limit:10,direction:"backward"})).filter(e=>"error"===e.level);t.length>0&&t[0]&&a.push({deploymentId:e.uid,timestamp:e.created,message:t[0].message,url:e.url})}catch(t){o.logger.warn("Erro ao obter logs do deployment ".concat(e.uid),t)}return{hasErrors:a.length>0,errorCount:a.length,errors:a}}constructor(e){this.baseUrl="https://api.vercel.com",this.apiToken=e.apiToken,this.teamId=e.teamId||void 0,this.projectId=e.projectId||void 0}}class s{async getProjectStatus(){try{let e=(await this.client.getDeployments(10))[0]||null,t=await this.client.checkRecentErrors(24),r="healthy",o="Sistema funcionando normalmente";e?"ERROR"===e.state?(r="down",o="\xdaltimo deployment falhou"):t.errorCount>3?(r="degraded",o="".concat(t.errorCount," erros nas \xfaltimas 24h")):"BUILDING"===e.state&&(r="degraded",o="Deploy em andamento"):(r="down",o="Nenhum deployment encontrado");let a=e&&"READY"===e.state?Date.now()-e.created:0;return{status:r,lastDeployment:e,recentErrors:t.errorCount,uptime:a,message:o}}catch(e){return o.logger.error("Erro ao obter status do projeto Vercel",e),{status:"down",lastDeployment:null,recentErrors:0,uptime:0,message:"Erro ao conectar com Vercel API"}}}async getPerformanceMetrics(){try{let e=Date.now()-864e5,t=(await this.client.getAnalytics({since:e,granularity:"hour"})).reduce((e,t)=>{var r,o;return{requests:(e.requests||0)+(t.requests||0),errors:(e.errors||0)+(t.errors||0),bandwidth:(e.bandwidth||0)+(t.bandwidth||0),duration:(e.duration||0)+(t.duration||0),cacheHits:(e.cacheHits||0)+((null===(r=t.cache)||void 0===r?void 0:r.hits)||0),cacheMisses:(e.cacheMisses||0)+((null===(o=t.cache)||void 0===o?void 0:o.misses)||0)}},{requests:0,errors:0,bandwidth:0,duration:0,cacheHits:0,cacheMisses:0}),r=(t.requests||0)>0?(t.errors||0)/(t.requests||1)*100:0,o=(t.requests||0)>0?(t.duration||0)/(t.requests||1):0,a=(t.cacheHits||0)+(t.cacheMisses||0),s=a>0?(t.cacheHits||0)/a*100:0;return{requests:t.requests||0,errors:t.errors||0,errorRate:Math.round(100*r)/100,averageResponseTime:Math.round(o),bandwidth:t.bandwidth||0,cacheHitRate:Math.round(100*s)/100}}catch(e){return o.logger.error("Erro ao obter m\xe9tricas de performance",e),{requests:0,errors:0,errorRate:0,averageResponseTime:0,bandwidth:0,cacheHitRate:0}}}async getFilteredLogs(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=await this.client.getDeployments(5),r=[];for(let a of t)try{let t=await this.client.getDeploymentLogs(a.uid,{limit:e.limit||50,direction:"backward"});r.push(...t)}catch(e){o.logger.warn("Erro ao obter logs do deployment ".concat(a.uid),e)}let a=r;if(e.level&&(a=a.filter(t=>t.level===e.level)),e.source&&(a=a.filter(t=>t.source===e.source)),e.search){let t=e.search.toLowerCase();a=a.filter(e=>e.message.toLowerCase().includes(t))}return a.sort((e,t)=>t.timestamp-e.timestamp),a.slice(0,e.limit||50)}catch(e){return o.logger.error("Erro ao obter logs filtrados",e),[]}}constructor(e,t,r){this.client=new a({apiToken:e,...t&&{teamId:t},...r&&{projectId:r}})}}}}]);