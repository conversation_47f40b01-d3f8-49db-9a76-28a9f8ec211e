"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5880],{79055:function(e,r,t){t.d(r,{C:function(){return s}});var n=t(57437),o=t(13027);t(2265);var i=t(49354);let a=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function s(e){let{className:r,variant:t,...o}=e;return(0,n.jsx)("div",{className:(0,i.cn)(a({variant:t}),r),...o})}},89733:function(e,r,t){t.d(r,{Button:function(){return u},d:function(){return l}});var n=t(57437),o=t(71538),i=t(13027),a=t(847),s=t(2265),d=t(18043),c=t(49354);let l=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=s.forwardRef((e,r)=>{let{className:t,variant:i,size:s,rounded:u,cssFeedback:m,asChild:f=!1,animated:g=!1,icon:h,iconPosition:p="left",children:v,...b}=e,x=f?o.g7:"button",R=(0,n.jsxs)("span",{className:"inline-flex items-center justify-center",children:[h&&"left"===p&&(0,n.jsx)("span",{className:"mr-2",children:h}),v,h&&"right"===p&&(0,n.jsx)("span",{className:"ml-2",children:h})]});if(g){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(i)?void 0:{y:-2},transition:{duration:.67*d.zn,ease:d.d}},o=(0,c.cn)(l({variant:i,size:s,rounded:u,cssFeedback:"none",className:t})),m={...b,className:o,...e};return(0,n.jsx)(a.E.button,{ref:r,...m,children:R})}return(0,n.jsx)(x,{className:(0,c.cn)(l({variant:i,size:s,rounded:u,cssFeedback:m,className:t})),ref:r,...b,children:R})});u.displayName="Button"},48185:function(e,r,t){t.d(r,{Ol:function(){return c},SZ:function(){return u},Zb:function(){return d},aY:function(){return m},eW:function(){return f},ll:function(){return l}});var n=t(57437),o=t(847),i=t(2265),a=t(18043),s=t(49354);let d=(0,i.forwardRef)((e,r)=>{let{className:t,children:i,hoverable:d=!1,variant:c="default",noPadding:l=!1,animated:u=!1,...m}=e,f=(0,s.cn)("rounded-xl border shadow-sm",{"p-6":!l,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":d&&!u,"border-border bg-card":"default"===c,"border-border/50 bg-transparent":"outline"===c,"bg-card/90 backdrop-blur-md border-border/50":"glass"===c,"bg-gradient-primary text-primary-foreground border-none":"gradient"===c},t);return u?(0,n.jsx)(o.E.div,{ref:r,className:f,...(0,a.Ph)("card"),whileHover:d?a.q.hover:void 0,whileTap:d?a.q.tap:void 0,...m,children:i}):(0,n.jsx)("div",{ref:r,className:f,...m,children:i})});d.displayName="Card";let c=(0,i.forwardRef)((e,r)=>{let{className:t,...o}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("mb-4 flex flex-col space-y-1.5",t),...o})});c.displayName="CardHeader";let l=(0,i.forwardRef)((e,r)=>{let{className:t,...o}=e;return(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-xl font-semibold leading-none tracking-tight",t),...o})});l.displayName="CardTitle";let u=(0,i.forwardRef)((e,r)=>{let{className:t,...o}=e;return(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...o})});u.displayName="CardDescription";let m=(0,i.forwardRef)((e,r)=>{let{className:t,...o}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("card-content",t),...o})});m.displayName="CardContent";let f=(0,i.forwardRef)((e,r)=>{let{className:t,...o}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center pt-4 mt-auto",t),...o})});f.displayName="CardFooter"},188:function(e,r,t){t.d(r,{Kk:function(){return m},MD:function(){return u}});var n=t(57437),o=t(2265);class i{recordRender(e,r){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3?arguments[3]:void 0;if(!this.isEnabled)return;let o=Date.now();this.renderEvents.push({componentName:e,timestamp:o,renderTime:r,props:n||{},isOptimized:t});let i=this.metrics.get(e);i?(i.renderCount++,i.lastRenderTime=r,i.totalRenderTime+=r,i.averageRenderTime=i.totalRenderTime/i.renderCount):this.metrics.set(e,{componentName:e,renderCount:1,lastRenderTime:r,averageRenderTime:r,totalRenderTime:r,isOptimized:t}),this.renderEvents.length>1e3&&(this.renderEvents=this.renderEvents.slice(-1e3))}getComponentMetrics(e){return this.metrics.get(e)}getAllMetrics(){return Array.from(this.metrics.values())}getRecentRenderEvents(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return this.renderEvents.slice(-e)}getPerformanceComparison(){let e=this.getAllMetrics().filter(e=>e.isOptimized),r=this.getAllMetrics().filter(e=>!e.isOptimized),t=e.reduce((e,r)=>e+r.averageRenderTime,0)/e.length||0,n=r.reduce((e,r)=>e+r.averageRenderTime,0)/r.length||0,o=e.reduce((e,r)=>e+r.renderCount,0)/e.length||0,i=r.reduce((e,r)=>e+r.renderCount,0)/r.length||0;return{optimized:e,nonOptimized:r,improvement:{averageRenderTimeReduction:n>0?(n-t)/n*100:0,renderCountReduction:i>0?(i-o)/i*100:0}}}generateReport(){let e=this.getPerformanceComparison(),r=this.metrics.size,t=e.optimized.length,n=e.nonOptimized.length;return"\n\uD83D\uDCCA RELAT\xd3RIO DE PERFORMANCE - COMPONENTES UI\n\n\uD83D\uDCC8 Resumo Geral:\n- Total de componentes monitorados: ".concat(r,"\n- Componentes otimizados: ").concat(t,"\n- Componentes n\xe3o otimizados: ").concat(n,"\n\n\uD83D\uDE80 Melhorias de Performance:\n- Redu\xe7\xe3o m\xe9dia no tempo de renderiza\xe7\xe3o: ").concat(e.improvement.averageRenderTimeReduction.toFixed(2),"%\n- Redu\xe7\xe3o m\xe9dia no n\xfamero de renderiza\xe7\xf5es: ").concat(e.improvement.renderCountReduction.toFixed(2),"%\n\n\uD83D\uDD1D Top 5 Componentes Mais Renderizados:\n").concat(this.getAllMetrics().sort((e,r)=>r.renderCount-e.renderCount).slice(0,5).map((e,r)=>"".concat(r+1,". ").concat(e.componentName,": ").concat(e.renderCount," renders (").concat(e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado",")")).join("\n"),"\n\n⚡ Top 5 Componentes Mais Lentos:\n").concat(this.getAllMetrics().sort((e,r)=>r.averageRenderTime-e.averageRenderTime).slice(0,5).map((e,r)=>"".concat(r+1,". ").concat(e.componentName,": ").concat(e.averageRenderTime.toFixed(2),"ms (").concat(e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado",")")).join("\n"),"\n    ").trim()}clear(){this.metrics.clear(),this.renderEvents=[]}exportData(){let e=this.getPerformanceComparison();return{metrics:this.getAllMetrics(),events:this.renderEvents,summary:e}}constructor(){this.metrics=new Map,this.renderEvents=[],this.isEnabled=!1,this.isEnabled=!1}}let a=new i;function s(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=o.useRef(0);return o.useEffect(()=>{t.current=performance.now()}),o.useEffect(()=>{let n=performance.now()-t.current;a.recordRender(e,n,r)}),{recordCustomMetric:(t,n)=>{a.recordRender("".concat(e,".").concat(t),n,r)}}}var d=t(89733);function c(e,r){for(let t of["variant","size","disabled","children","className","animated","icon","iconPosition","asChild"])if(e[t]!==r[t])return!1;if("object"==typeof e.children&&"object"==typeof r.children)return e.children===r.children;for(let t of["onClick","onMouseEnter","onMouseLeave","onFocus","onBlur"]){let n=e[t],o=r[t];if((n||o)&&(!n||!o||n!==o))return!1}return!0}let l=(0,o.memo)(d.Button,c),u=o.forwardRef((e,r)=>(s("OptimizedButton",!0),(0,n.jsx)(l,{...e,ref:r})));u.displayName="OptimizedButton";let m=(0,o.memo)(e=>{let{onAction:r,actionId:t,...i}=e;s("ActionButton",!0);let a=o.useCallback(()=>{r()},[r]);return(0,n.jsx)(d.Button,{...i,onClick:a})},(e,r)=>c(e,r)&&e.actionId===r.actionId&&e.onAction===r.onAction);m.displayName="ActionButton"}}]);