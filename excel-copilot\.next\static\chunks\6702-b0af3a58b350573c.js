(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6702],{24654:function(){},88592:function(e,r,n){"use strict";n.d(r,{Z:function(){return t}});let t=(0,n(81066).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},20500:function(e,r,n){"use strict";n.d(r,{Z:function(){return t}});let t=(0,n(81066).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},55430:function(e,r,n){"use strict";n.d(r,{Z:function(){return t}});let t=(0,n(81066).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},89733:function(e,r,n){"use strict";n.d(r,{Button:function(){return l},d:function(){return u}});var t=n(57437),o=n(71538),i=n(13027),a=n(847),s=n(2265),d=n(18043),c=n(49354);let u=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),l=s.forwardRef((e,r)=>{let{className:n,variant:i,size:s,rounded:l,cssFeedback:f,asChild:p=!1,animated:m=!1,icon:v,iconPosition:h="left",children:b,...g}=e,x=p?o.g7:"button",y=(0,t.jsxs)("span",{className:"inline-flex items-center justify-center",children:[v&&"left"===h&&(0,t.jsx)("span",{className:"mr-2",children:v}),b,v&&"right"===h&&(0,t.jsx)("span",{className:"ml-2",children:v})]});if(m){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(i)?void 0:{y:-2},transition:{duration:.67*d.zn,ease:d.d}},o=(0,c.cn)(u({variant:i,size:s,rounded:l,cssFeedback:"none",className:n})),f={...g,className:o,...e};return(0,t.jsx)(a.E.button,{ref:r,...f,children:y})}return(0,t.jsx)(x,{className:(0,c.cn)(u({variant:i,size:s,rounded:l,cssFeedback:f,className:n})),ref:r,...g,children:y})});l.displayName="Button"},48185:function(e,r,n){"use strict";n.d(r,{Ol:function(){return c},SZ:function(){return l},Zb:function(){return d},aY:function(){return f},eW:function(){return p},ll:function(){return u}});var t=n(57437),o=n(847),i=n(2265),a=n(18043),s=n(49354);let d=(0,i.forwardRef)((e,r)=>{let{className:n,children:i,hoverable:d=!1,variant:c="default",noPadding:u=!1,animated:l=!1,...f}=e,p=(0,s.cn)("rounded-xl border shadow-sm",{"p-6":!u,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":d&&!l,"border-border bg-card":"default"===c,"border-border/50 bg-transparent":"outline"===c,"bg-card/90 backdrop-blur-md border-border/50":"glass"===c,"bg-gradient-primary text-primary-foreground border-none":"gradient"===c},n);return l?(0,t.jsx)(o.E.div,{ref:r,className:p,...(0,a.Ph)("card"),whileHover:d?a.q.hover:void 0,whileTap:d?a.q.tap:void 0,...f,children:i}):(0,t.jsx)("div",{ref:r,className:p,...f,children:i})});d.displayName="Card";let c=(0,i.forwardRef)((e,r)=>{let{className:n,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("mb-4 flex flex-col space-y-1.5",n),...o})});c.displayName="CardHeader";let u=(0,i.forwardRef)((e,r)=>{let{className:n,...o}=e;return(0,t.jsx)("h3",{ref:r,className:(0,s.cn)("text-xl font-semibold leading-none tracking-tight",n),...o})});u.displayName="CardTitle";let l=(0,i.forwardRef)((e,r)=>{let{className:n,...o}=e;return(0,t.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",n),...o})});l.displayName="CardDescription";let f=(0,i.forwardRef)((e,r)=>{let{className:n,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("card-content",n),...o})});f.displayName="CardContent";let p=(0,i.forwardRef)((e,r)=>{let{className:n,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center pt-4 mt-auto",n),...o})});p.displayName="CardFooter"},58743:function(e,r,n){"use strict";n.d(r,{Nt:function(){return s},Xf:function(){return i},tt:function(){return d}}),n(13537);var t=n(62848),o=n(25566);let i={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};i.FREE,i.PRO_MONTHLY,i.PRO_ANNUAL;let a=o.env.STRIPE_SECRET_KEY||"";function s(e){switch(e){case i.FREE:return"Gr\xe1tis";case i.PRO_MONTHLY:return"Pro Mensal";case i.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}o.env.STRIPE_WEBHOOK_SECRET,a&&new t.Z(a,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}});let d=()=>"pk_test_51RGJ6nRrKLXtzZkM0EqlkOaZIpDNCLiGwLAHXr1YOmHWGIZB5RxMImDy2bBVyErg7PiQ2T6vqS1pFSK6O4nSEdQJ00Tz72hUX7"},13537:function(e,r,n){"use strict";n.d(r,{J:function(){return g}});var t,o="basil",i="https://js.stripe.com",a="".concat(i,"/").concat(o,"/stripe.js"),s=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,d=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,c=function(){for(var e=document.querySelectorAll('script[src^="'.concat(i,'"]')),r=0;r<e.length;r++){var n,t=e[r];if(n=t.src,s.test(n)||d.test(n))return t}return null},u=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(a).concat(r);var t=document.head||document.body;if(!t)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return t.appendChild(n),n},l=function(e,r){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.2.0",startTime:r})},f=null,p=null,m=null,v=function(e,r,n){if(null===e)return null;var t,i=r[0].match(/^pk_test/),a=3===(t=e.version)?"v3":t;i&&a!==o&&console.warn("Stripe.js@".concat(a," was loaded on the page, but @stripe/stripe-js@").concat("7.2.0"," expected Stripe.js@").concat(o,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var s=e.apply(void 0,r);return l(s,n),s},h=!1,b=function(){return t||(t=(null!==f?f:(f=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var n,t=c();t?t&&null!==m&&null!==p&&(t.removeEventListener("load",m),t.removeEventListener("error",p),null===(n=t.parentNode)||void 0===n||n.removeChild(t),t=u(null)):t=u(null),m=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},p=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},t.addEventListener("load",m),t.addEventListener("error",p)}catch(e){r(e);return}})).catch(function(e){return f=null,Promise.reject(e)})).catch(function(e){return t=null,Promise.reject(e)}))};Promise.resolve().then(function(){return b()}).catch(function(e){h||console.warn(e)});var g=function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];h=!0;var t=Date.now();return b().then(function(e){return v(e,r,t)})}}}]);