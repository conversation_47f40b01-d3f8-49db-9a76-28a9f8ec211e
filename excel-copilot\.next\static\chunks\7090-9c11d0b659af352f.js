(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7090],{35496:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});let{Decimal:n,objectEnumValues:r,makeStrictEnum:s,Public:o,getRuntime:u,skip:a}=i(80736),c={};t.Prisma=c,t.$Enums={},c.prismaVersion={client:"5.22.0",engine:"605197351a3c8bdd595af2d2a9bc3025bca48ea2"},c.PrismaClientKnownRequestError=()=>{let e=u().prettyName;throw Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.<PERSON>rismaClientUnknownRequestError=()=>{let e=u().prettyName;throw Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientRustPanicError=()=>{let e=u().prettyName;throw Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientInitializationError=()=>{let e=u().prettyName;throw Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientValidationError=()=>{let e=u().prettyName;throw Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.NotFoundError=()=>{let e=u().prettyName;throw Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.Decimal=n,c.sql=()=>{let e=u().prettyName;throw Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.empty=()=>{let e=u().prettyName;throw Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.join=()=>{let e=u().prettyName;throw Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.raw=()=>{let e=u().prettyName;throw Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.validator=o.validator,c.getExtensionContext=()=>{let e=u().prettyName;throw Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.defineExtension=()=>{let e=u().prettyName;throw Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.DbNull=r.instances.DbNull,c.JsonNull=r.instances.JsonNull,c.AnyNull=r.instances.AnyNull,c.NullTypes={DbNull:r.classes.DbNull,JsonNull:r.classes.JsonNull,AnyNull:r.classes.AnyNull},t.Prisma.TransactionIsolationLevel=s({ReadUncommitted:"ReadUncommitted",ReadCommitted:"ReadCommitted",RepeatableRead:"RepeatableRead",Serializable:"Serializable"}),t.Prisma.AccountScalarFieldEnum={id:"id",userId:"userId",type:"type",provider:"provider",providerAccountId:"providerAccountId",refresh_token:"refresh_token",access_token:"access_token",expires_at:"expires_at",token_type:"token_type",scope:"scope",id_token:"id_token",session_state:"session_state"},t.Prisma.SessionScalarFieldEnum={id:"id",sessionToken:"sessionToken",userId:"userId",expires:"expires"},t.Prisma.UserScalarFieldEnum={id:"id",name:"name",email:"email",emailVerified:"emailVerified",image:"image",createdAt:"createdAt",updatedAt:"updatedAt",lastIpAddress:"lastIpAddress",lastLoginAt:"lastLoginAt",loginCount:"loginCount",userAgent:"userAgent",isSuspicious:"isSuspicious",isBanned:"isBanned",banReason:"banReason",banDate:"banDate"},t.Prisma.VerificationTokenScalarFieldEnum={identifier:"identifier",token:"token",expires:"expires"},t.Prisma.WorkbookScalarFieldEnum={id:"id",name:"name",description:"description",userId:"userId",isPublic:"isPublic",createdAt:"createdAt",updatedAt:"updatedAt",lastAccessedAt:"lastAccessedAt"},t.Prisma.WorkbookShareScalarFieldEnum={id:"id",workbookId:"workbookId",sharedByUserId:"sharedByUserId",sharedWithUserId:"sharedWithUserId",permissionLevel:"permissionLevel",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.SheetScalarFieldEnum={id:"id",name:"name",workbookId:"workbookId",data:"data",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.ChatHistoryScalarFieldEnum={id:"id",userId:"userId",message:"message",response:"response",workbookId:"workbookId",createdAt:"createdAt"},t.Prisma.SubscriptionScalarFieldEnum={id:"id",userId:"userId",stripeCustomerId:"stripeCustomerId",stripeSubscriptionId:"stripeSubscriptionId",stripePriceId:"stripePriceId",status:"status",plan:"plan",cancelAtPeriodEnd:"cancelAtPeriodEnd",currentPeriodStart:"currentPeriodStart",currentPeriodEnd:"currentPeriodEnd",apiCallsLimit:"apiCallsLimit",apiCallsUsed:"apiCallsUsed",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.PaymentScalarFieldEnum={id:"id",amount:"amount",currency:"currency",stripePaymentId:"stripePaymentId",stripeInvoiceId:"stripeInvoiceId",status:"status",subscriptionId:"subscriptionId",metadata:"metadata",createdAt:"createdAt"},t.Prisma.ApiUsageScalarFieldEnum={id:"id",userId:"userId",count:"count",endpoint:"endpoint",workbookId:"workbookId",billable:"billable",createdAt:"createdAt"},t.Prisma.SecurityLogScalarFieldEnum={id:"id",userId:"userId",eventType:"eventType",details:"details",timestamp:"timestamp"},t.Prisma.UserActionLogScalarFieldEnum={id:"id",userId:"userId",action:"action",details:"details",timestamp:"timestamp"},t.Prisma.CommandFeedbackScalarFieldEnum={id:"id",commandId:"commandId",command:"command",successful:"successful",feedbackText:"feedbackText",timestamp:"timestamp"},t.Prisma.AiMetricsScalarFieldEnum={id:"id",totalCommands:"totalCommands",successfulCommands:"successfulCommands",failedCommands:"failedCommands",lastUpdated:"lastUpdated"},t.Prisma.WebVitalScalarFieldEnum={id:"id",name:"name",value:"value",rating:"rating",delta:"delta",metricId:"metricId",navigationType:"navigationType",url:"url",userAgent:"userAgent",timestamp:"timestamp",createdAt:"createdAt"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.Prisma.NullsOrder={first:"first",last:"last"},t.Prisma.ModelName={Account:"Account",Session:"Session",User:"User",VerificationToken:"VerificationToken",Workbook:"Workbook",WorkbookShare:"WorkbookShare",Sheet:"Sheet",ChatHistory:"ChatHistory",Subscription:"Subscription",Payment:"Payment",ApiUsage:"ApiUsage",SecurityLog:"SecurityLog",UserActionLog:"UserActionLog",CommandFeedback:"CommandFeedback",AiMetrics:"AiMetrics",WebVital:"WebVital"};class l{constructor(){return new Proxy(this,{get(e,t){let i=u();throw Error((i.isEdge?`PrismaClient is not configured to run in ${i.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`:"PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `"+i.prettyName+"`).")+`
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`)}})}}t.PrismaClient=l,Object.assign(t,c)},17090:function(e,t,i){let n=i(35496);e.exports=n},80736:function(e){"use strict";var t=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,r=Object.prototype.hasOwnProperty,s=(e,i)=>{for(var n in i)t(e,n,{get:i[n],enumerable:!0})},o={};s(o,{Decimal:()=>e0,Public:()=>u,getRuntime:()=>y,makeStrictEnum:()=>v,objectEnumValues:()=>g}),e.exports=((e,s,o,u)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let a of n(s))r.call(e,a)||a===o||t(e,a,{get:()=>s[a],enumerable:!(u=i(s,a))||u.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u={};function a(...e){return e=>e}s(u,{validator:()=>a});var c=Symbol(),l=new WeakMap,d=class{constructor(e){e===c?l.set(this,"Prisma.".concat(this._getName())):l.set(this,"new Prisma.".concat(this._getNamespace(),".").concat(this._getName(),"()"))}_getName(){return this.constructor.name}toString(){return l.get(this)}},h=class extends d{_getNamespace(){return"NullTypes"}},f=class extends h{};w(f,"DbNull");var p=class extends h{};w(p,"JsonNull");var m=class extends h{};w(m,"AnyNull");var g={classes:{DbNull:f,JsonNull:p,AnyNull:m},instances:{DbNull:new f(c),JsonNull:new p(c),AnyNull:new m(c)}};function w(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var b=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function v(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!b.has(t))throw TypeError("Invalid enum value: ".concat(String(t)))}})}var N={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function y(){var e,t,i;let n="object"==typeof Netlify?"netlify":"string"==typeof EdgeRuntime?"edge-light":(null==(e=globalThis.navigator)?void 0:e.userAgent)==="Cloudflare-Workers"?"workerd":globalThis.Deno?"deno":globalThis.__lagon__?"lagon":(null==(i=null==(t=globalThis.process)?void 0:t.release)?void 0:i.name)==="node"?"node":globalThis.Bun?"bun":globalThis.fastly?"fastly":"unknown";return{id:n,prettyName:N[n]||n,isEdge:["workerd","deno","netlify","edge-light"].includes(n)}}var E,x,A="0123456789abcdef",P="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",I="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",k={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},S=!0,C="[DecimalError] ",M=C+"Invalid argument: ",F=C+"Precision limit exceeded",_=C+"crypto unavailable",O="[object Decimal]",R=Math.floor,T=Math.pow,D=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,U=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,q=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,L=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Z=P.length-1,$=I.length-1,j={toStringTag:O};function V(e){var t,i,n,r=e.length-1,s="",o=e[0];if(r>0){for(s+=o,t=1;t<r;t++)(i=7-(n=e[t]+"").length)&&(s+=ee(i)),s+=n;(i=7-(n=(o=e[t])+"").length)&&(s+=ee(i))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function W(e,t,i){if(e!==~~e||e<t||e>i)throw Error(M+e)}function H(e,t,i,n){var r,s,o,u;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=7,r=0):(r=Math.ceil((t+1)/7),t%=7),s=T(10,7-t),u=e[r]%s|0,null==n?t<3?(0==t?u=u/100|0:1==t&&(u=u/10|0),o=i<4&&99999==u||i>3&&49999==u||5e4==u||0==u):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(e[r+1]/s/100|0)==T(10,t-2)-1||(u==s/2||0==u)&&(e[r+1]/s/100|0)==0:t<4?(0==t?u=u/1e3|0:1==t?u=u/100|0:2==t&&(u=u/10|0),o=(n||i<4)&&9999==u||!n&&i>3&&4999==u):o=((n||i<4)&&u+1==s||!n&&i>3&&u+1==s/2)&&(e[r+1]/s/1e3|0)==T(10,t-3)-1,o}function B(e,t,i){for(var n,r,s=[0],o=0,u=e.length;o<u;){for(r=s.length;r--;)s[r]*=t;for(s[0]+=A.indexOf(e.charAt(o++)),n=0;n<s.length;n++)s[n]>i-1&&(void 0===s[n+1]&&(s[n+1]=0),s[n+1]+=s[n]/i|0,s[n]%=i)}return s.reverse()}j.absoluteValue=j.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),z(e)},j.ceil=function(){return z(new this.constructor(this),this.e+1,2)},j.clampedTo=j.clamp=function(e,t){var i=this.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(M+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new i(this)},j.comparedTo=j.cmp=function(e){var t,i,n,r,s=this.d,o=(e=new this.constructor(e)).d,u=this.s,a=e.s;if(!s||!o)return u&&a?u!==a?u:s===o?0:!s^u<0?1:-1:NaN;if(!s[0]||!o[0])return s[0]?u:o[0]?-a:0;if(u!==a)return u;if(this.e!==e.e)return this.e>e.e^u<0?1:-1;for(n=s.length,r=o.length,t=0,i=n<r?n:r;t<i;++t)if(s[t]!==o[t])return s[t]>o[t]^u<0?1:-1;return n===r?0:n>r^u<0?1:-1},j.cosine=j.cos=function(){var e,t,i=this,n=i.constructor;return i.d?i.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(i.e,i.sd())+7,n.rounding=1,i=function(e,t){var i,n,r;if(t.isZero())return t;(n=t.d.length)<32?r=(1/ec(4,i=Math.ceil(n/3))).toString():(i=16,r="2.3283064365386962890625e-10"),e.precision+=i,t=ea(e,1,t.times(r),new e(1));for(var s=i;s--;){var o=t.times(t);t=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,t}(n,el(n,i)),n.precision=e,n.rounding=t,z(2==x||3==x?i.neg():i,e,t,!0)):new n(1):new n(NaN)},j.cubeRoot=j.cbrt=function(){var e,t,i,n,r,s,o,u,a,c,l=this.constructor;if(!this.isFinite()||this.isZero())return new l(this);for(S=!1,(s=this.s*T(this.s*this,1/3))&&Math.abs(s)!=1/0?n=new l(s.toString()):(i=V(this.d),(s=((e=this.e)-i.length+1)%3)&&(i+=1==s||-2==s?"0":"00"),s=T(i,1/3),e=R((e+1)/3)-(e%3==(e<0?-1:2)),(n=new l(i=s==1/0?"5e"+e:(i=s.toExponential()).slice(0,i.indexOf("e")+1)+e)).s=this.s),o=(e=l.precision)+3;;)if(n=J((c=(a=(u=n).times(u).times(u)).plus(this)).plus(this).times(u),c.plus(a),o+2,1),V(u.d).slice(0,o)===(i=V(n.d)).slice(0,o)){if("9999"!=(i=i.slice(o-3,o+1))&&(r||"4999"!=i)){+i&&(+i.slice(1)||"5"!=i.charAt(0))||(z(n,e+1,1),t=!n.times(n).times(n).eq(this));break}if(!r&&(z(u,e+1,0),u.times(u).times(u).eq(this))){n=u;break}o+=4,r=1}return S=!0,z(n,e,l.rounding,t)},j.decimalPlaces=j.dp=function(){var e,t=this.d,i=NaN;if(t){if(i=((e=t.length-1)-R(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i},j.dividedBy=j.div=function(e){return J(this,new this.constructor(e))},j.dividedToIntegerBy=j.divToInt=function(e){var t=this.constructor;return z(J(this,new t(e),0,1,1),t.precision,t.rounding)},j.equals=j.eq=function(e){return 0===this.cmp(e)},j.floor=function(){return z(new this.constructor(this),this.e+1,3)},j.greaterThan=j.gt=function(e){return this.cmp(e)>0},j.greaterThanOrEqualTo=j.gte=function(e){var t=this.cmp(e);return 1==t||0===t},j.hyperbolicCosine=j.cosh=function(){var e,t,i,n,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,n=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,(r=s.d.length)<32?t=(1/ec(4,e=Math.ceil(r/3))).toString():(e=16,t="2.3283064365386962890625e-10"),s=ea(o,1,s.times(t),new o(1),!0);for(var a,c=e,l=new o(8);c--;)a=s.times(s),s=u.minus(a.times(l.minus(a.times(l))));return z(s,o.precision=i,o.rounding=n,!0)},j.hyperbolicSine=j.sinh=function(){var e,t,i,n,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(t=s.precision,i=s.rounding,s.precision=t+Math.max(r.e,r.sd())+4,s.rounding=1,(n=r.d.length)<3)r=ea(s,2,r,r,!0);else{e=(e=1.4*Math.sqrt(n))>16?16:0|e,r=ea(s,2,r=r.times(1/ec(5,e)),r,!0);for(var o,u=new s(5),a=new s(16),c=new s(20);e--;)o=r.times(r),r=r.times(u.plus(o.times(a.times(o).plus(c))))}return s.precision=t,s.rounding=i,z(r,t,i,!0)},j.hyperbolicTangent=j.tanh=function(){var e,t,i=this.constructor;return this.isFinite()?this.isZero()?new i(this):(e=i.precision,t=i.rounding,i.precision=e+7,i.rounding=1,J(this.sinh(),this.cosh(),i.precision=e,i.rounding=t)):new i(this.s)},j.inverseCosine=j.acos=function(){var e,t=this,i=t.constructor,n=t.abs().cmp(1),r=i.precision,s=i.rounding;return -1!==n?0===n?t.isNeg()?X(i,r,s):new i(0):new i(NaN):t.isZero()?X(i,r+4,s).times(.5):(i.precision=r+6,i.rounding=1,t=t.asin(),e=X(i,r+4,s).times(.5),i.precision=r,i.rounding=s,e.minus(t))},j.inverseHyperbolicCosine=j.acosh=function(){var e,t,i=this,n=i.constructor;return i.lte(1)?new n(i.eq(1)?0:NaN):i.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(i.e),i.sd())+4,n.rounding=1,S=!1,i=i.times(i).minus(1).sqrt().plus(i),S=!0,n.precision=e,n.rounding=t,i.ln()):new n(i)},j.inverseHyperbolicSine=j.asinh=function(){var e,t,i=this,n=i.constructor;return!i.isFinite()||i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,n.rounding=1,S=!1,i=i.times(i).plus(1).sqrt().plus(i),S=!0,n.precision=e,n.rounding=t,i.ln())},j.inverseHyperbolicTangent=j.atanh=function(){var e,t,i,n,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,t=s.rounding,Math.max(n=r.sd(),e)<-(2*r.e)-1?z(new s(r),e,t,!0):(s.precision=i=n-r.e,r=J(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=t,r.times(.5))):new s(NaN)},j.inverseSine=j.asin=function(){var e,t,i,n,r=this,s=r.constructor;return r.isZero()?new s(r):(t=r.abs().cmp(1),i=s.precision,n=s.rounding,-1!==t?0===t?((e=X(s,i+4,n).times(.5)).s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=n,r.times(2)))},j.inverseTangent=j.atan=function(){var e,t,i,n,r,s,o,u,a,c=this,l=c.constructor,d=l.precision,h=l.rounding;if(c.isFinite()){if(c.isZero())return new l(c);if(c.abs().eq(1)&&d+4<=$)return(o=X(l,d+4,h).times(.25)).s=c.s,o}else{if(!c.s)return new l(NaN);if(d+4<=$)return(o=X(l,d+4,h).times(.5)).s=c.s,o}for(l.precision=u=d+10,l.rounding=1,e=i=Math.min(28,u/7+2|0);e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(S=!1,t=Math.ceil(u/7),n=1,a=c.times(c),o=new l(c),r=c;-1!==e;)if(r=r.times(a),s=o.minus(r.div(n+=2)),r=r.times(a),void 0!==(o=s.plus(r.div(n+=2))).d[t])for(e=t;o.d[e]===s.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),S=!0,z(o,l.precision=d,l.rounding=h,!0)},j.isFinite=function(){return!!this.d},j.isInteger=j.isInt=function(){return!!this.d&&R(this.e/7)>this.d.length-2},j.isNaN=function(){return!this.s},j.isNegative=j.isNeg=function(){return this.s<0},j.isPositive=j.isPos=function(){return this.s>0},j.isZero=function(){return!!this.d&&0===this.d[0]},j.lessThan=j.lt=function(e){return 0>this.cmp(e)},j.lessThanOrEqualTo=j.lte=function(e){return 1>this.cmp(e)},j.logarithm=j.log=function(e){var t,i,n,r,s,o,u,a=this.constructor,c=a.precision,l=a.rounding;if(null==e)e=new a(10),t=!0;else{if(i=(e=new a(e)).d,e.s<0||!i||!i[0]||e.eq(1))return new a(NaN);t=e.eq(10)}if(i=this.d,this.s<0||!i||!i[0]||this.eq(1))return new a(i&&!i[0]?-1/0:1!=this.s?NaN:i?0:1/0);if(t){if(i.length>1)r=!0;else{for(n=i[0];n%10==0;)n/=10;r=1!==n}}if(S=!1,H((u=J(es(this,o=c+5),t?G(a,o+10):es(e,o),o,1)).d,n=c,l))do if(o+=10,u=J(es(this,o),t?G(a,o+10):es(e,o),o,1),!r){+V(u.d).slice(n+1,n+15)+1==1e14&&(u=z(u,c+1,0));break}while(H(u.d,n+=10,l));return S=!0,z(u,c,l)},j.minus=j.sub=function(e){var t,i,n,r,s,o,u,a,c,l,d,h,f=this.constructor;if(e=new f(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new f(e.d||this.s!==e.s?this:NaN):e=new f(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(c=this.d,h=e.d,u=f.precision,a=f.rounding,!c[0]||!h[0]){if(h[0])e.s=-e.s;else{if(!c[0])return new f(3===a?-0:0);e=new f(this)}return S?z(e,u,a):e}if(i=R(e.e/7),l=R(this.e/7),c=c.slice(),s=l-i){for((d=s<0)?(t=c,s=-s,o=h.length):(t=h,i=l,o=c.length),s>(n=Math.max(Math.ceil(u/7),o)+2)&&(s=n,t.length=1),t.reverse(),n=s;n--;)t.push(0);t.reverse()}else{for((d=(n=c.length)<(o=h.length))&&(o=n),n=0;n<o;n++)if(c[n]!=h[n]){d=c[n]<h[n];break}s=0}for(d&&(t=c,c=h,h=t,e.s=-e.s),o=c.length,n=h.length-o;n>0;--n)c[o++]=0;for(n=h.length;n>s;){if(c[--n]<h[n]){for(r=n;r&&0===c[--r];)c[r]=1e7-1;--c[r],c[n]+=1e7}c[n]-=h[n]}for(;0===c[--o];)c.pop();for(;0===c[0];c.shift())--i;return c[0]?(e.d=c,e.e=Q(c,i),S?z(e,u,a):e):new f(3===a?-0:0)},j.modulo=j.mod=function(e){var t,i=this.constructor;return e=new i(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(S=!1,9==i.modulo?(t=J(this,e.abs(),0,3,1),t.s*=e.s):t=J(this,e,0,i.modulo,1),t=t.times(e),S=!0,this.minus(t)):z(new i(this),i.precision,i.rounding):new i(NaN)},j.naturalExponential=j.exp=function(){return er(this)},j.naturalLogarithm=j.ln=function(){return es(this)},j.negated=j.neg=function(){var e=new this.constructor(this);return e.s=-e.s,z(e)},j.plus=j.add=function(e){var t,i,n,r,s,o,u,a,c,l,d=this.constructor;if(e=new d(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new d(e.d||this.s===e.s?this:NaN)):e=new d(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(c=this.d,l=e.d,u=d.precision,a=d.rounding,!c[0]||!l[0])return l[0]||(e=new d(this)),S?z(e,u,a):e;if(s=R(this.e/7),n=R(e.e/7),c=c.slice(),r=s-n){for(r<0?(i=c,r=-r,o=l.length):(i=l,n=s,o=c.length),r>(o=(s=Math.ceil(u/7))>o?s+1:o+1)&&(r=o,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for((o=c.length)-(r=l.length)<0&&(r=o,i=l,l=c,c=i),t=0;r;)t=(c[--r]=c[r]+l[r]+t)/1e7|0,c[r]%=1e7;for(t&&(c.unshift(t),++n),o=c.length;0==c[--o];)c.pop();return e.d=c,e.e=Q(c,n),S?z(e,u,a):e},j.precision=j.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(M+e);return this.d?(t=Y(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},j.round=function(){var e=this.constructor;return z(new e(this),this.e+1,e.rounding)},j.sine=j.sin=function(){var e,t,i=this,n=i.constructor;return i.isFinite()?i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+Math.max(i.e,i.sd())+7,n.rounding=1,i=function(e,t){var i,n=t.d.length;if(n<3)return t.isZero()?t:ea(e,2,t,t);i=(i=1.4*Math.sqrt(n))>16?16:0|i,t=ea(e,2,t=t.times(1/ec(5,i)),t);for(var r,s=new e(5),o=new e(16),u=new e(20);i--;)r=t.times(t),t=t.times(s.plus(r.times(o.times(r).minus(u))));return t}(n,el(n,i)),n.precision=e,n.rounding=t,z(x>2?i.neg():i,e,t,!0)):new n(NaN)},j.squareRoot=j.sqrt=function(){var e,t,i,n,r,s,o=this.d,u=this.e,a=this.s,c=this.constructor;if(1!==a||!o||!o[0])return new c(!a||a<0&&(!o||o[0])?NaN:o?this:1/0);for(S=!1,0==(a=Math.sqrt(+this))||a==1/0?(((t=V(o)).length+u)%2==0&&(t+="0"),a=Math.sqrt(t),u=R((u+1)/2)-(u<0||u%2),n=new c(t=a==1/0?"5e"+u:(t=a.toExponential()).slice(0,t.indexOf("e")+1)+u)):n=new c(a.toString()),i=(u=c.precision)+3;;)if(n=(s=n).plus(J(this,s,i+2,1)).times(.5),V(s.d).slice(0,i)===(t=V(n.d)).slice(0,i)){if("9999"!=(t=t.slice(i-3,i+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(z(n,u+1,1),e=!n.times(n).eq(this));break}if(!r&&(z(s,u+1,0),s.times(s).eq(this))){n=s;break}i+=4,r=1}return S=!0,z(n,u,c.rounding,e)},j.tangent=j.tan=function(){var e,t,i=this,n=i.constructor;return i.isFinite()?i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,(i=i.sin()).s=1,i=J(i,new n(1).minus(i.times(i)).sqrt(),e+10,0),n.precision=e,n.rounding=t,z(2==x||4==x?i.neg():i,e,t,!0)):new n(NaN)},j.times=j.mul=function(e){var t,i,n,r,s,o,u,a,c,l=this.constructor,d=this.d,h=(e=new l(e)).d;if(e.s*=this.s,!d||!d[0]||!h||!h[0])return new l(e.s&&(!d||d[0]||h)&&(!h||h[0]||d)?d&&h?0*e.s:e.s/0:NaN);for(i=R(this.e/7)+R(e.e/7),(a=d.length)<(c=h.length)&&(s=d,d=h,h=s,o=a,a=c,c=o),s=[],n=o=a+c;n--;)s.push(0);for(n=c;--n>=0;){for(t=0,r=a+n;r>n;)u=s[r]+h[n]*d[r-n-1]+t,s[r--]=u%1e7|0,t=u/1e7|0;s[r]=(s[r]+t)%1e7|0}for(;!s[--o];)s.pop();return t?++i:s.shift(),e.d=s,e.e=Q(s,i),S?z(e,l.precision,l.rounding):e},j.toBinary=function(e,t){return ed(this,2,e,t)},j.toDecimalPlaces=j.toDP=function(e,t){var i=this,n=i.constructor;return i=new n(i),void 0===e?i:(W(e,0,1e9),void 0===t?t=n.rounding:W(t,0,8),z(i,e+i.e+1,t))},j.toExponential=function(e,t){var i,n=this,r=n.constructor;return void 0===e?i=K(n,!0):(W(e,0,1e9),void 0===t?t=r.rounding:W(t,0,8),i=K(n=z(new r(n),e+1,t),!0,e+1)),n.isNeg()&&!n.isZero()?"-"+i:i},j.toFixed=function(e,t){var i,n,r=this.constructor;return void 0===e?i=K(this):(W(e,0,1e9),void 0===t?t=r.rounding:W(t,0,8),i=K(n=z(new r(this),e+this.e+1,t),!1,e+n.e+1)),this.isNeg()&&!this.isZero()?"-"+i:i},j.toFraction=function(e){var t,i,n,r,s,o,u,a,c,l,d,h,f=this.d,p=this.constructor;if(!f)return new p(this);if(c=i=new p(1),n=a=new p(0),o=(s=(t=new p(n)).e=Y(f)-this.e-1)%7,t.d[0]=T(10,o<0?7+o:o),null==e)e=s>0?t:c;else{if(!(u=new p(e)).isInt()||u.lt(c))throw Error(M+u);e=u.gt(t)?s>0?t:c:u}for(S=!1,u=new p(V(f)),l=p.precision,p.precision=s=14*f.length;d=J(u,t,0,1,1),1!=(r=i.plus(d.times(n))).cmp(e);)i=n,n=r,r=c,c=a.plus(d.times(r)),a=r,r=t,t=u.minus(d.times(r)),u=r;return r=J(e.minus(i),n,0,1,1),a=a.plus(r.times(c)),i=i.plus(r.times(n)),a.s=c.s=this.s,h=1>J(c,n,s,1).minus(this).abs().cmp(J(a,i,s,1).minus(this).abs())?[c,n]:[a,i],p.precision=l,S=!0,h},j.toHexadecimal=j.toHex=function(e,t){return ed(this,16,e,t)},j.toNearest=function(e,t){var i=this,n=i.constructor;if(i=new n(i),null==e){if(!i.d)return i;e=new n(1),t=n.rounding}else{if(e=new n(e),void 0===t?t=n.rounding:W(t,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(S=!1,i=J(i,e,0,t,1).times(e),S=!0,z(i)):(e.s=i.s,i=e),i},j.toNumber=function(){return+this},j.toOctal=function(e,t){return ed(this,8,e,t)},j.toPower=j.pow=function(e){var t,i,n,r,s,o,u=this,a=u.constructor,c=+(e=new a(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new a(T(+u,c));if((u=new a(u)).eq(1))return u;if(n=a.precision,s=a.rounding,e.eq(1))return z(u,n,s);if((t=R(e.e/7))>=e.d.length-1&&(i=c<0?-c:c)<=9007199254740991)return r=et(a,u,i,n),e.s<0?new a(1).div(r):z(r,n,s);if((o=u.s)<0){if(t<e.d.length-1)return new a(NaN);if(1&e.d[t]||(o=1),0==u.e&&1==u.d[0]&&1==u.d.length)return u.s=o,u}return(t=0!=(i=T(+u,c))&&isFinite(i)?new a(i+"").e:R(c*(Math.log("0."+V(u.d))/Math.LN10+u.e+1)))>a.maxE+1||t<a.minE-1?new a(t>0?o/0:0):(S=!1,a.rounding=u.s=1,i=Math.min(12,(t+"").length),(r=er(e.times(es(u,n+i)),n)).d&&H((r=z(r,n+5,1)).d,n,s)&&(t=n+10,+V((r=z(er(e.times(es(u,t+i)),t),t+5,1)).d).slice(n+1,n+15)+1==1e14&&(r=z(r,n+1,0))),r.s=o,S=!0,a.rounding=s,z(r,n,s))},j.toPrecision=function(e,t){var i,n=this,r=n.constructor;return void 0===e?i=K(n,n.e<=r.toExpNeg||n.e>=r.toExpPos):(W(e,1,1e9),void 0===t?t=r.rounding:W(t,0,8),i=K(n=z(new r(n),e,t),e<=n.e||n.e<=r.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+i:i},j.toSignificantDigits=j.toSD=function(e,t){var i=this.constructor;return void 0===e?(e=i.precision,t=i.rounding):(W(e,1,1e9),void 0===t?t=i.rounding:W(t,0,8)),z(new i(this),e,t)},j.toString=function(){var e=this.constructor,t=K(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},j.truncated=j.trunc=function(){return z(new this.constructor(this),this.e+1,1)},j.valueOf=j.toJSON=function(){var e=this.constructor,t=K(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var J=function(){function e(e,t,i){var n,r=0,s=e.length;for(e=e.slice();s--;)n=e[s]*t+r,e[s]=n%i|0,r=n/i|0;return r&&e.unshift(r),e}function t(e,t,i,n){var r,s;if(i!=n)s=i>n?1:-1;else for(r=s=0;r<i;r++)if(e[r]!=t[r]){s=e[r]>t[r]?1:-1;break}return s}function i(e,t,i,n){for(var r=0;i--;)e[i]-=r,r=e[i]<t[i]?1:0,e[i]=r*n+e[i]-t[i];for(;!e[0]&&e.length>1;)e.shift()}return function(n,r,s,o,u,a){var c,l,d,h,f,p,m,g,w,b,v,N,y,x,A,P,I,k,S,C,M=n.constructor,F=n.s==r.s?1:-1,_=n.d,O=r.d;if(!_||!_[0]||!O||!O[0])return new M(n.s&&r.s&&(_?!O||_[0]!=O[0]:O)?_&&0==_[0]||!O?0*F:F/0:NaN);for(a?(f=1,l=n.e-r.e):(a=1e7,f=7,l=R(n.e/f)-R(r.e/f)),S=O.length,I=_.length,b=(w=new M(F)).d=[],d=0;O[d]==(_[d]||0);d++);if(O[d]>(_[d]||0)&&l--,null==s?(x=s=M.precision,o=M.rounding):x=u?s+(n.e-r.e)+1:s,x<0)b.push(1),p=!0;else{if(x=x/f+2|0,d=0,1==S){for(h=0,O=O[0],x++;(d<I||h)&&x--;d++)A=h*a+(_[d]||0),b[d]=A/O|0,h=A%O|0;p=h||d<I}else{for((h=a/(O[0]+1)|0)>1&&(O=e(O,h,a),_=e(_,h,a),S=O.length,I=_.length),P=S,N=(v=_.slice(0,S)).length;N<S;)v[N++]=0;(C=O.slice()).unshift(0),k=O[0],O[1]>=a/2&&++k;do h=0,(c=t(O,v,S,N))<0?(y=v[0],S!=N&&(y=y*a+(v[1]||0)),(h=y/k|0)>1?(h>=a&&(h=a-1),g=(m=e(O,h,a)).length,N=v.length,1==(c=t(m,v,g,N))&&(h--,i(m,S<g?C:O,g,a))):(0==h&&(c=h=1),m=O.slice()),(g=m.length)<N&&m.unshift(0),i(v,m,N,a),-1==c&&(N=v.length,(c=t(O,v,S,N))<1&&(h++,i(v,S<N?C:O,N,a))),N=v.length):0===c&&(h++,v=[0]),b[d++]=h,c&&v[0]?v[N++]=_[P]||0:(v=[_[P]],N=1);while((P++<I||void 0!==v[0])&&x--);p=void 0!==v[0]}b[0]||b.shift()}if(1==f)w.e=l,E=p;else{for(d=1,h=b[0];h>=10;h/=10)d++;w.e=d+l*f-1,z(w,u?s+w.e+1:s,o,p)}return w}}();function z(e,t,i,n){var r,s,o,u,a,c,l,d,h,f=e.constructor;e:if(null!=t){if(!(d=e.d))return e;for(r=1,u=d[0];u>=10;u/=10)r++;if((s=t-r)<0)s+=7,o=t,a=(l=d[h=0])/T(10,r-o-1)%10|0;else if((h=Math.ceil((s+1)/7))>=(u=d.length)){if(n){for(;u++<=h;)d.push(0);l=a=0,r=1,s%=7,o=s-7+1}else break e}else{for(l=u=d[h],r=1;u>=10;u/=10)r++;s%=7,a=(o=s-7+r)<0?0:l/T(10,r-o-1)%10|0}if(n=n||t<0||void 0!==d[h+1]||(o<0?l:l%T(10,r-o-1)),c=i<4?(a||n)&&(0==i||i==(e.s<0?3:2)):a>5||5==a&&(4==i||n||6==i&&(s>0?o>0?l/T(10,r-o):0:d[h-1])%10&1||i==(e.s<0?8:7)),t<1||!d[0])return d.length=0,c?(t-=e.e+1,d[0]=T(10,(7-t%7)%7),e.e=-t||0):d[0]=e.e=0,e;if(0==s?(d.length=h,u=1,h--):(d.length=h+1,u=T(10,7-s),d[h]=o>0?(l/T(10,r-o)%T(10,o)|0)*u:0),c)for(;;)if(0==h){for(s=1,o=d[0];o>=10;o/=10)s++;for(o=d[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(e.e++,1e7==d[0]&&(d[0]=1));break}else{if(d[h]+=u,1e7!=d[h])break;d[h--]=0,u=1}for(s=d.length;0===d[--s];)d.pop()}return S&&(e.e>f.maxE?(e.d=null,e.e=NaN):e.e<f.minE&&(e.e=0,e.d=[0])),e}function K(e,t,i){if(!e.isFinite())return eo(e);var n,r=e.e,s=V(e.d),o=s.length;return t?(i&&(n=i-o)>0?s=s.charAt(0)+"."+s.slice(1)+ee(n):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):r<0?(s="0."+ee(-r-1)+s,i&&(n=i-o)>0&&(s+=ee(n))):r>=o?(s+=ee(r+1-o),i&&(n=i-r-1)>0&&(s=s+"."+ee(n))):((n=r+1)<o&&(s=s.slice(0,n)+"."+s.slice(n)),i&&(n=i-o)>0&&(r+1===o&&(s+="."),s+=ee(n))),s}function Q(e,t){var i=e[0];for(t*=7;i>=10;i/=10)t++;return t}function G(e,t,i){if(t>Z)throw S=!0,i&&(e.precision=i),Error(F);return z(new e(P),t,1,!0)}function X(e,t,i){if(t>$)throw Error(F);return z(new e(I),t,i,!0)}function Y(e){var t=e.length-1,i=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)i--;for(t=e[0];t>=10;t/=10)i++}return i}function ee(e){for(var t="";e--;)t+="0";return t}function et(e,t,i,n){var r,s=new e(1),o=Math.ceil(n/7+4);for(S=!1;;){if(i%2&&eh((s=s.times(t)).d,o)&&(r=!0),0===(i=R(i/2))){i=s.d.length-1,r&&0===s.d[i]&&++s.d[i];break}eh((t=t.times(t)).d,o)}return S=!0,s}function ei(e){return 1&e.d[e.d.length-1]}function en(e,t,i){for(var n,r=new e(t[0]),s=0;++s<t.length;)if((n=new e(t[s])).s)r[i](n)&&(r=n);else{r=n;break}return r}function er(e,t){var i,n,r,s,o,u,a,c=0,l=0,d=0,h=e.constructor,f=h.rounding,p=h.precision;if(!e.d||!e.d[0]||e.e>17)return new h(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(S=!1,a=p):a=t,u=new h(.03125);e.e>-2;)e=e.times(u),d+=5;for(a+=n=Math.log(T(2,d))/Math.LN10*2+5|0,i=s=o=new h(1),h.precision=a;;){if(s=z(s.times(e),a,1),i=i.times(++l),V((u=o.plus(J(s,i,a,1))).d).slice(0,a)===V(o.d).slice(0,a)){for(r=d;r--;)o=z(o.times(o),a,1);if(null!=t)return h.precision=p,o;if(!(c<3&&H(o.d,a-n,f,c)))return z(o,h.precision=p,f,S=!0);h.precision=a+=10,i=s=u=new h(1),l=0,c++}o=u}}function es(e,t){var i,n,r,s,o,u,a,c,l,d,h,f=1,p=e,m=p.d,g=p.constructor,w=g.rounding,b=g.precision;if(p.s<0||!m||!m[0]||!p.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=p.s?NaN:m?0:p);if(null==t?(S=!1,l=b):l=t,g.precision=l+=10,n=(i=V(m)).charAt(0),!(15e14>Math.abs(s=p.e)))return c=G(g,l+2,b).times(s+""),p=es(new g(n+"."+i.slice(1)),l-10).plus(c),g.precision=b,null==t?z(p,b,w,S=!0):p;for(;n<7&&1!=n||1==n&&i.charAt(1)>3;)n=(i=V((p=p.times(e)).d)).charAt(0),f++;for(s=p.e,n>1?(p=new g("0."+i),s++):p=new g(n+"."+i.slice(1)),d=p,a=o=p=J(p.minus(1),p.plus(1),l,1),h=z(p.times(p),l,1),r=3;;){if(o=z(o.times(h),l,1),V((c=a.plus(J(o,new g(r),l,1))).d).slice(0,l)===V(a.d).slice(0,l)){if(a=a.times(2),0!==s&&(a=a.plus(G(g,l+2,b).times(s+""))),a=J(a,new g(f),l,1),null!=t)return g.precision=b,a;if(!H(a.d,l-10,w,u))return z(a,g.precision=b,w,S=!0);g.precision=l+=10,c=o=p=J(d.minus(1),d.plus(1),l,1),h=z(p.times(p),l,1),r=u=1}a=c,r+=2}}function eo(e){return String(e.s*e.s/0)}function eu(e,t){var i,n,r;for((i=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(i<0&&(i=n),i+=+t.slice(n+1),t=t.substring(0,n)):i<0&&(i=t.length),n=0;48===t.charCodeAt(n);n++);for(r=t.length;48===t.charCodeAt(r-1);--r);if(t=t.slice(n,r)){if(r-=n,e.e=i=i-n-1,e.d=[],n=(i+1)%7,i<0&&(n+=7),n<r){for(n&&e.d.push(+t.slice(0,n)),r-=7;n<r;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=r;for(;n--;)t+="0";e.d.push(+t),S&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function ea(e,t,i,n,r){var s,o,u,a,c=e.precision,l=Math.ceil(c/7);for(S=!1,a=i.times(i),u=new e(n);;){if(o=J(u.times(a),new e(t++*t++),c,1),u=r?n.plus(o):n.minus(o),n=J(o.times(a),new e(t++*t++),c,1),void 0!==(o=u.plus(n)).d[l]){for(s=l;o.d[s]===u.d[s]&&s--;);if(-1==s)break}s=u,u=n,n=o,o=s}return S=!0,o.d.length=l+1,o}function ec(e,t){for(var i=e;--t;)i*=e;return i}function el(e,t){var i,n=t.s<0,r=X(e,e.precision,1),s=r.times(.5);if((t=t.abs()).lte(s))return x=n?4:1,t;if((i=t.divToInt(r)).isZero())x=n?3:2;else{if((t=t.minus(i.times(r))).lte(s))return x=ei(i)?n?2:3:n?4:1,t;x=ei(i)?n?1:4:n?3:2}return t.minus(r).abs()}function ed(e,t,i,n){var r,s,o,u,a,c,l,d,h,f=e.constructor,p=void 0!==i;if(p?(W(i,1,1e9),void 0===n?n=f.rounding:W(n,0,8)):(i=f.precision,n=f.rounding),e.isFinite()){for(o=(l=K(e)).indexOf("."),p?(r=2,16==t?i=4*i-3:8==t&&(i=3*i-2)):r=t,o>=0&&(l=l.replace(".",""),(h=new f(1)).e=l.length-o,h.d=B(K(h),10,r),h.e=h.d.length),s=a=(d=B(l,10,r)).length;0==d[--a];)d.pop();if(d[0]){if(o<0?s--:((e=new f(e)).d=d,e.e=s,d=(e=J(e,h,i,n,0,r)).d,s=e.e,c=E),o=d[i],u=r/2,c=c||void 0!==d[i+1],c=n<4?(void 0!==o||c)&&(0===n||n===(e.s<0?3:2)):o>u||o===u&&(4===n||c||6===n&&1&d[i-1]||n===(e.s<0?8:7)),d.length=i,c)for(;++d[--i]>r-1;)d[i]=0,i||(++s,d.unshift(1));for(a=d.length;!d[a-1];--a);for(o=0,l="";o<a;o++)l+=A.charAt(d[o]);if(p){if(a>1){if(16==t||8==t){for(o=16==t?4:3,--a;a%o;a++)l+="0";for(a=(d=B(l,r,t)).length;!d[a-1];--a);for(o=1,l="1.";o<a;o++)l+=A.charAt(d[o])}else l=l.charAt(0)+"."+l.slice(1)}l=l+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)l="0"+l;l="0."+l}else if(++s>a)for(s-=a;s--;)l+="0";else s<a&&(l=l.slice(0,s)+"."+l.slice(s))}else l=p?"0p+0":"0";l=(16==t?"0x":2==t?"0b":8==t?"0o":"")+l}else l=eo(e);return e.s<0?"-"+l:l}function eh(e,t){if(e.length>t)return e.length=t,!0}function ef(e){return new this(e).abs()}function ep(e){return new this(e).acos()}function em(e){return new this(e).acosh()}function eg(e,t){return new this(e).plus(t)}function ew(e){return new this(e).asin()}function eb(e){return new this(e).asinh()}function ev(e){return new this(e).atan()}function eN(e){return new this(e).atanh()}function ey(e,t){e=new this(e),t=new this(t);var i,n=this.precision,r=this.rounding,s=n+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(i=t.s<0?X(this,n,r):new this(0)).s=e.s:!e.d||t.isZero()?(i=X(this,s,1).times(.5)).s=e.s:t.s<0?(this.precision=s,this.rounding=1,i=this.atan(J(e,t,s,1)),t=X(this,s,1),this.precision=n,this.rounding=r,i=e.s<0?i.minus(t):i.plus(t)):i=this.atan(J(e,t,s,1)):(i=X(this,s,1).times(t.s>0?.25:.75)).s=e.s:i=new this(NaN),i}function eE(e){return new this(e).cbrt()}function ex(e){return z(e=new this(e),e.e+1,2)}function eA(e,t,i){return new this(e).clamp(t,i)}function eP(e){if(!e||"object"!=typeof e)throw Error(C+"Object expected");var t,i,n,r=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(i=s[t],r&&(this[i]=k[i]),void 0!==(n=e[i])){if(R(n)===n&&n>=s[t+1]&&n<=s[t+2])this[i]=n;else throw Error(M+i+": "+n)}if(i="crypto",r&&(this[i]=k[i]),void 0!==(n=e[i])){if(!0===n||!1===n||0===n||1===n){if(n){if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(_)}else this[i]=!1}else throw Error(M+i+": "+n)}return this}function eI(e){return new this(e).cos()}function ek(e){return new this(e).cosh()}function eS(e,t){return new this(e).div(t)}function eC(e){return new this(e).exp()}function eM(e){return z(e=new this(e),e.e+1,3)}function eF(){var e,t,i=new this(0);for(S=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)i.d&&(i=i.plus(t.times(t)));else{if(t.s)return S=!0,new this(1/0);i=t}return S=!0,i.sqrt()}function e_(e){return e instanceof eY||e&&e.toStringTag===O||!1}function eO(e){return new this(e).ln()}function eR(e,t){return new this(e).log(t)}function eT(e){return new this(e).log(2)}function eD(e){return new this(e).log(10)}function eU(){return en(this,arguments,"lt")}function eq(){return en(this,arguments,"gt")}function eL(e,t){return new this(e).mod(t)}function eZ(e,t){return new this(e).mul(t)}function e$(e,t){return new this(e).pow(t)}function ej(e){var t,i,n,r,s=0,o=new this(1),u=[];if(void 0===e?e=this.precision:W(e,1,1e9),n=Math.ceil(e/7),this.crypto){if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));s<n;)(r=t[s])>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);s<n;)(r=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((127&t[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,s):(u.push(r%1e7),s+=4);s=n/4}else throw Error(_)}else for(;s<n;)u[s++]=1e7*Math.random()|0;for(n=u[--s],e%=7,n&&e&&(r=T(10,7-e),u[s]=(n/r|0)*r);0===u[s];s--)u.pop();if(s<0)i=0,u=[0];else{for(i=-1;0===u[0];i-=7)u.shift();for(n=1,r=u[0];r>=10;r/=10)n++;n<7&&(i-=7-n)}return o.e=i,o.d=u,o}function eV(e){return z(e=new this(e),e.e+1,this.rounding)}function eW(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function eH(e){return new this(e).sin()}function eB(e){return new this(e).sinh()}function eJ(e){return new this(e).sqrt()}function ez(e,t){return new this(e).sub(t)}function eK(){var e=0,t=arguments,i=new this(t[0]);for(S=!1;i.s&&++e<t.length;)i=i.plus(t[e]);return S=!0,z(i,this.precision,this.rounding)}function eQ(e){return new this(e).tan()}function eG(e){return new this(e).tanh()}function eX(e){return z(e=new this(e),e.e+1,1)}j[Symbol.for("nodejs.util.inspect.custom")]=j.toString,j[Symbol.toStringTag]="Decimal";var eY=j.constructor=function e(t){var i,n,r;function s(e){var t,i,n;if(!(this instanceof s))return new s(e);if(this.constructor=s,e_(e)){this.s=e.s,S?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(n=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,i=e;i>=10;i/=10)t++;S?t>s.maxE?(this.e=NaN,this.d=null):t<s.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return eu(this,e.toString())}if("string"!==n)throw Error(M+e);return 45===(i=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===i&&(e=e.slice(1)),this.s=1),L.test(e)?eu(this,e):function(e,t){var i,n,r,s,o,u,a,c,l;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),L.test(t))return eu(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(U.test(t))i=16,t=t.toLowerCase();else if(D.test(t))i=2;else if(q.test(t))i=8;else throw Error(M+t);for((s=t.search(/p/i))>0?(a=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),o=(s=t.indexOf("."))>=0,n=e.constructor,o&&(s=(u=(t=t.replace(".","")).length)-s,r=et(n,new n(i),s,2*s)),s=l=(c=B(t,i,1e7)).length-1;0===c[s];--s)c.pop();return s<0?new n(0*e.s):(e.e=Q(c,l),e.d=c,S=!1,o&&(e=J(e,r,4*u)),a&&(e=e.times(54>Math.abs(a)?T(2,a):eY.pow(2,a))),S=!0,e)}(this,e)}if(s.prototype=j,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=eP,s.clone=e,s.isDecimal=e_,s.abs=ef,s.acos=ep,s.acosh=em,s.add=eg,s.asin=ew,s.asinh=eb,s.atan=ev,s.atanh=eN,s.atan2=ey,s.cbrt=eE,s.ceil=ex,s.clamp=eA,s.cos=eI,s.cosh=ek,s.div=eS,s.exp=eC,s.floor=eM,s.hypot=eF,s.ln=eO,s.log=eR,s.log10=eD,s.log2=eT,s.max=eU,s.min=eq,s.mod=eL,s.mul=eZ,s.pow=e$,s.random=ej,s.round=eV,s.sign=eW,s.sin=eH,s.sinh=eB,s.sqrt=eJ,s.sub=ez,s.sum=eK,s.tan=eQ,s.tanh=eG,s.trunc=eX,void 0===t&&(t={}),t&&!0!==t.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<r.length;)t.hasOwnProperty(n=r[i++])||(t[n]=this[n]);return s.config(t),s}(k);P=new eY(P),I=new eY(I);var e0=eY;/*! Bundled license information:

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.4.3
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/}}]);