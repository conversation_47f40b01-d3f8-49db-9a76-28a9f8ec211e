"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7100],{71885:function(t,e,r){r.d(e,{FN:function(){return f},Mi:function(){return v},VW:function(){return u},_i:function(){return l},gD:function(){return p},lj:function(){return x},sA:function(){return m}});var s=r(57437),a=r(44504),o=r(13027),n=r(74697),i=r(2265),d=r(49354);let u=a.zt,l=i.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,s.jsx)(a.l_,{ref:e,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});l.displayName=a.l_.displayName;let c=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=i.forwardRef((t,e)=>{let{className:r,variant:o,...n}=t;return(0,s.jsx)(a.fC,{ref:e,className:(0,d.cn)(c({variant:o}),r),...n})});f.displayName=a.fC.displayName;let p=i.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,s.jsx)(a.aU,{ref:e,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...o})});p.displayName=a.aU.displayName;let m=i.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,s.jsx)(a.x8,{ref:e,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,s.jsx)(n.Z,{className:"h-4 w-4"})})});m.displayName=a.x8.displayName;let v=i.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,s.jsx)(a.Dx,{ref:e,className:(0,d.cn)("text-sm font-semibold",r),...o})});v.displayName=a.Dx.displayName;let x=i.forwardRef((t,e)=>{let{className:r,...o}=t;return(0,s.jsx)(a.dk,{ref:e,className:(0,d.cn)("text-sm opacity-90",r),...o})});x.displayName=a.dk.displayName},47100:function(t,e,r){r.r(e),r.d(e,{Toaster:function(){return n}});var s=r(57437),a=r(71885),o=r(78068);function n(){let{toasts:t}=(0,o.pm)();return(0,s.jsxs)(a.VW,{children:[t.map(function(t){let{id:e,title:r,description:o,action:n,...i}=t;return(0,s.jsxs)(a.FN,{...i,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(a.Mi,{children:r}),o&&(0,s.jsx)(a.lj,{children:o})]}),n,(0,s.jsx)(a.sA,{})]},e)}),(0,s.jsx)(a._i,{})]})}},78068:function(t,e,r){r.d(e,{V6:function(){return p},pm:function(){return m},s6:function(){return f}});var s=r(2265);let a={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},o=0,n=new Map,i=t=>{if(n.has(t))return;let e=setTimeout(()=>{n.delete(t),c({type:a.REMOVE_TOAST,toastId:t})},1e6);n.set(t,e)},d=(t,e)=>{switch(e.type){case a.ADD_TOAST:return{...t,toasts:[e.toast,...t.toasts].slice(0,5)};case a.UPDATE_TOAST:return{...t,toasts:t.toasts.map(t=>{var r;return t.id===(null===(r=e.toast)||void 0===r?void 0:r.id)?{...t,...e.toast}:t})};case a.DISMISS_TOAST:{let{toastId:r}=e;return r?i(r):t.toasts.forEach(t=>{i(t.id)}),{...t,toasts:t.toasts.map(t=>t.id===r||void 0===r?{...t,open:!1}:t)}}case a.REMOVE_TOAST:if(void 0===e.toastId)return{...t,toasts:[]};return{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)};default:return t}},u=[],l={toasts:[]};function c(t){l=d(l,t),u.forEach(t=>{t(l)})}function f(t){let{...e}=t,r=(o=(o+1)%Number.MAX_VALUE).toString(),s=()=>c({type:a.DISMISS_TOAST,toastId:r});return c({type:a.ADD_TOAST,toast:{...e,id:r,open:!0,onOpenChange:t=>{t||s()}}}),{id:r,dismiss:s,update:t=>c({type:a.UPDATE_TOAST,toast:{...t,id:r}})}}function p(){let[t,e]=s.useState(l);return s.useEffect(()=>(u.push(e),()=>{let t=u.indexOf(e);t>-1&&u.splice(t,1)}),[t]),{toast:f,dismiss:t=>c({type:a.DISMISS_TOAST,toastId:void 0===t?"":t}),toasts:t.toasts}}let m=p}}]);