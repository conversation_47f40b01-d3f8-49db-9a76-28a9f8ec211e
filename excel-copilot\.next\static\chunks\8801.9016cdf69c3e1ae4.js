"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8801],{38801:function(e,t,n){n.r(t),n.d(t,{LinearClient:function(){return i},LinearMonitoringService:function(){return r}});var s=n(9821),a=n(18473);class i{async graphqlQuery(e,t){if(!this.apiKey||"mcp_integration_enabled"===this.apiKey)return this.useMCPIntegration(e,t);let n=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:"Bearer ".concat(this.apiKey),"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t})});if(!n.ok)throw Error("Linear API error: ".concat(n.status," ").concat(n.statusText));let s=await n.json();if(s.errors)throw Error("Linear GraphQL error: ".concat(s.errors.map(e=>e.message).join(", ")));return s.data}async useMCPIntegration(e,t){try{a.logger.info("Executando query via integra\xe7\xe3o MCP Linear");let{linear:s}=await n.e(9545).then(n.bind(n,99545)),i=this.convertGraphQLToNaturalLanguage(e,t);return await s({summary:"Execute Linear query via MCP integration",query:i,is_read_only:!e.includes("mutation")})}catch(e){throw a.logger.error("Erro ao usar integra\xe7\xe3o MCP Linear:",e),Error("Falha na integra\xe7\xe3o MCP Linear")}}convertGraphQLToNaturalLanguage(e,t){return e.includes("mutation")||e.includes("CreateIssue")||e.includes("UpdateIssue")?e.includes("CreateIssue")?"Create issue with data: ".concat(JSON.stringify(t)):e.includes("UpdateIssue")?"Update issue with data: ".concat(JSON.stringify(t)):"Execute mutation with data: ".concat(JSON.stringify(t)):e.includes("organization")?"Get workspace organization information and teams":e.includes("issues")?e:e.includes("teams")?"Get all teams with their workflow states":e.includes("projects")?"Get all projects with their details":e.includes("workflowStates")?"Get all workflow states for teams":"Execute Linear query"}async getWorkspaceInfo(){return this.graphqlQuery("\n      query {\n        organization {\n          id\n          name\n          urlKey\n          createdAt\n        }\n        teams {\n          nodes {\n            id\n            name\n            key\n            description\n            states {\n              nodes {\n                id\n                name\n                type\n                color\n              }\n            }\n          }\n        }\n      }\n    ")}async getIssues(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{teamId:t,assigneeId:n,state:s,limit:a=50,includeArchived:i=!1}=e,r="",o=[];t&&o.push('team: { id: { eq: "'.concat(t,'" } }')),n&&o.push('assignee: { id: { eq: "'.concat(n,'" } }')),s&&o.push('state: { name: { eq: "'.concat(s,'" } }')),i||o.push('state: { type: { neq: "canceled" } }'),o.length>0&&(r="filter: { ".concat(o.join(", ")," }"));let c="\n      query GetIssues {\n        issues(first: ".concat(a,", ").concat(r,") {\n          nodes {\n            id\n            identifier\n            title\n            description\n            state {\n              id\n              name\n              type\n            }\n            team {\n              id\n              name\n              key\n            }\n            assignee {\n              id\n              name\n              email\n            }\n            labels {\n              nodes {\n                id\n                name\n                color\n              }\n            }\n            createdAt\n            updatedAt\n            priority\n            estimate\n          }\n        }\n      }\n    ");return{issues:(await this.graphqlQuery(c)).issues.nodes}}async getIssue(e){return(await this.graphqlQuery("\n      query GetIssue($id: String!) {\n        issue(id: $id) {\n          id\n          identifier\n          title\n          description\n          state {\n            id\n            name\n            type\n          }\n          team {\n            id\n            name\n            key\n          }\n          assignee {\n            id\n            name\n            email\n          }\n          labels {\n            nodes {\n              id\n              name\n              color\n            }\n          }\n          createdAt\n          updatedAt\n          priority\n          estimate\n        }\n      }\n    ",{id:e})).issue}async getTeams(){return{teams:(await this.graphqlQuery("\n      query GetTeams {\n        teams {\n          nodes {\n            id\n            name\n            key\n            description\n            states {\n              nodes {\n                id\n                name\n                type\n                color\n              }\n            }\n          }\n        }\n      }\n    ")).teams.nodes}}async createIssue(e){let t={teamId:e.teamId,title:e.title,description:e.description,assigneeId:e.assigneeId,stateId:e.stateId,priority:e.priority,labelIds:e.labelIds},n=await this.graphqlQuery("\n      mutation CreateIssue($input: IssueCreateInput!) {\n        issueCreate(input: $input) {\n          success\n          issue {\n            id\n            identifier\n            title\n            description\n            state {\n              id\n              name\n              type\n            }\n            team {\n              id\n              name\n              key\n            }\n            assignee {\n              id\n              name\n              email\n            }\n            createdAt\n            updatedAt\n          }\n        }\n      }\n    ",{input:t});if(!n.issueCreate.success)throw Error("Falha ao criar issue no Linear");return{issue:n.issueCreate.issue}}async updateIssue(e,t){let n=await this.graphqlQuery("\n      mutation UpdateIssue($id: String!, $input: IssueUpdateInput!) {\n        issueUpdate(id: $id, input: $input) {\n          success\n          issue {\n            id\n            identifier\n            title\n            description\n            state {\n              id\n              name\n              type\n            }\n            team {\n              id\n              name\n              key\n            }\n            assignee {\n              id\n              name\n              email\n            }\n            updatedAt\n          }\n        }\n      }\n    ",{id:e,input:t});if(!n.issueUpdate.success)throw Error("Falha ao atualizar issue no Linear");return{issue:n.issueUpdate.issue}}async getProjects(){return{projects:(await this.graphqlQuery("\n      query GetProjects {\n        projects {\n          nodes {\n            id\n            name\n            description\n            state\n            progress\n            targetDate\n            createdAt\n            updatedAt\n          }\n        }\n      }\n    ")).projects.nodes}}async getWorkflowStates(){return{states:(await this.graphqlQuery("\n      query GetWorkflowStates {\n        workflowStates {\n          nodes {\n            id\n            name\n            type\n            color\n            team {\n              id\n              name\n            }\n          }\n        }\n      }\n    ")).workflowStates.nodes}}async checkHealth(){try{if(!this.apiKey&&"mcp_integration_enabled"!==this.apiKey)return{configured:!1,apiKeyValid:!1,workspaceAccessible:!1,issueCount:0,teamCount:0,recentActivity:0};let[e,t,n]=await Promise.all([this.getWorkspaceInfo(),this.getIssues({limit:10}),this.getTeams()]),s=new Date(Date.now()-864e5),a=t.issues.filter(e=>new Date(e.updatedAt)>s).length;return{configured:!0,apiKeyValid:!0,workspaceAccessible:!0,lastSync:new Date().toISOString(),issueCount:t.issues.length,teamCount:n.teams.length,recentActivity:a}}catch(e){return a.logger.error("Linear health check failed:",e),{configured:!!this.apiKey,apiKeyValid:!1,workspaceAccessible:!1,issueCount:0,teamCount:0,recentActivity:0}}}constructor(e){this.baseUrl="https://api.linear.app/graphql",this.apiKey=e||s.Vi.LINEAR_API_KEY||"",this.apiKey||a.logger.warn("Linear API key n\xe3o configurada")}}class r{async getWorkspaceSummary(){try{let[e,t,n,s]=await Promise.all([this.client.getWorkspaceInfo(),this.client.getIssues({limit:100}),this.client.getTeams(),this.client.getProjects()]),a=t.issues.reduce((e,t)=>{let n=t.state.name;return e[n]=(e[n]||0)+1,e},{}),i=t.issues.reduce((e,t)=>{let n=t.team.name;return e[n]=(e[n]||0)+1,e},{}),r=t.issues.reduce((e,t)=>{var n;let s=(null===(n=t.assignee)||void 0===n?void 0:n.name)||"Unassigned";return e[s]=(e[s]||0)+1,e},{});return{workspace:(null==e?void 0:e.organization)||"Unknown",summary:{totalIssues:t.issues.length,totalTeams:n.teams.length,totalProjects:s.projects.length,issuesByState:a,issuesByTeam:i,issuesByAssignee:r},recentIssues:t.issues.sort((e,t)=>new Date(t.updatedAt).getTime()-new Date(e.updatedAt).getTime()).slice(0,10)}}catch(e){throw a.logger.error("Erro ao obter resumo do workspace Linear:",e),e}}async getExcelCopilotIssues(){try{let e=(await this.client.getIssues({limit:100})).issues.filter(e=>{var t,n;return e.title.toLowerCase().includes("excel")||e.title.toLowerCase().includes("copilot")||e.title.toLowerCase().includes("mcp")||(null===(t=e.description)||void 0===t?void 0:t.toLowerCase().includes("excel"))||(null===(n=e.description)||void 0===n?void 0:n.toLowerCase().includes("copilot"))});return{issues:e,summary:{total:e.length,byState:e.reduce((e,t)=>{let n=t.state.name;return e[n]=(e[n]||0)+1,e},{})}}}catch(e){throw a.logger.error("Erro ao obter issues do Excel Copilot:",e),e}}async createExcelCopilotIssue(e){try{let t=(await this.client.getTeams()).teams[0];if(!t)throw Error("Nenhum team encontrado no workspace Linear");let n={teamId:t.id,title:"[Excel Copilot] ".concat(e.title),description:"**Tipo:** ".concat(e.type,"\n\n").concat(e.description,"\n\n---\n*Criado automaticamente via MCP Integration*"),priority:e.priority||3,...e.assigneeId&&{assigneeId:e.assigneeId}};return await this.client.createIssue(n)}catch(e){throw a.logger.error("Erro ao criar issue do Excel Copilot:",e),e}}async getDevelopmentMetrics(){try{let e=await this.client.getIssues({limit:200}),t=new Date,n=new Date(t.getTime()-6048e5),s=new Date(t.getTime()-2592e6),a=e.issues.filter(e=>new Date(e.createdAt)>n),i=e.issues.filter(e=>"completed"===e.state.type&&new Date(e.updatedAt)>s),r=e.issues.filter(e=>"started"===e.state.type);return{totalIssues:e.issues.length,recentIssues:a.length,completedThisMonth:i.length,inProgress:r.length,averageCompletionTime:this.calculateAverageCompletionTime(i),velocity:i.length}}catch(e){throw a.logger.error("Erro ao obter m\xe9tricas de desenvolvimento:",e),e}}calculateAverageCompletionTime(e){return 0===e.length?0:Math.round(e.reduce((e,t)=>{let n=new Date(t.createdAt).getTime();return e+(new Date(t.updatedAt).getTime()-n)},0)/e.length/864e5)}constructor(e){this.client=new i(e)}}}}]);