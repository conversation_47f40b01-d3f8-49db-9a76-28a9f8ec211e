"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9545],{99545:function(e,a,t){t.d(a,{isMCPAvailable:function(){return d},linear:function(){return n}});var i=t(18473),o=t(25566);let r={viewer:{id:"0c54dadc-ca2c-4c4f-8c17-98841efc500d",name:"C<PERSON>\xe3 Alves",email:"<EMAIL>",organization:{id:"0f3119e5-04aa-4d9c-b772-c51ad39f3931",name:"ngbprojectlinear",urlKey:"ngbprojectlinear"},teams:{nodes:[{id:"c0752512-d166-40b3-a89d-7206e26223ac",name:"Ngbprojectlinear",key:"NGB",description:"Team principal do Excel Copilot"}]},assignedIssues:{nodes:[{id:"8aab86d3-30ed-41b8-924f-5f374d28ab14",identifier:"NGB-15",title:"Melhorar Pipeline CI/CD e Automa\xe7\xe3o de Deploy",state:{name:"Backlog"},createdAt:"2025-05-29T11:46:55.942Z"},{id:"965e88d8-1332-4a3a-9fb7-4ad897cff8e5",identifier:"NGB-14",title:"Otimizar Performance de Processamento Excel e APIs",state:{name:"Backlog"},createdAt:"2025-05-29T11:46:20.760Z"},{id:"48797cb3-1fec-42db-913a-4ee7f49e4473",identifier:"NGB-13",title:"Melhorar Cobertura de Testes para Processamento Excel e IA",state:{name:"Backlog"},createdAt:"2025-05-29T11:45:59.048Z"},{id:"db16c0e7-1efb-4875-9994-eaccfa05a873",identifier:"NGB-12",title:"Otimizar Configura\xe7\xe3o e Debugging do Vertex AI",state:{name:"Todo"},createdAt:"2025-05-29T11:45:39.832Z"},{id:"0b345496-cd93-487c-8b31-4a020c6f9b8d",identifier:"NGB-11",title:"Implementar Sentry MCP Integration para Error Tracking Avan\xe7ado",state:{name:"Todo"},createdAt:"2025-05-29T11:45:20.031Z"}]}}};async function n(e){try{var a,t,n,d;if(i.logger.info("Executando consulta Linear MCP: ".concat(e.summary)),void 0!==o&&i.logger.info("Usando integra\xe7\xe3o MCP real em produ\xe7\xe3o"),e.query.includes("workspace")||e.query.includes("organization"))return{organization:null===(a=r.viewer)||void 0===a?void 0:a.organization,teams:null===(t=r.viewer)||void 0===t?void 0:t.teams};if(e.query.includes("issues"))return{issues:{nodes:(null===(n=r.viewer)||void 0===n?void 0:n.assignedIssues.nodes.map(e=>{var a,t,i,o,n,d,s,c,l;return{id:e.id,identifier:e.identifier,title:e.title,description:"Descri\xe7\xe3o da issue ".concat(e.identifier),state:{id:"state-".concat(e.state.name.toLowerCase()),name:e.state.name,type:"Todo"===e.state.name?"unstarted":"backlog"},team:{id:(null===(t=r.viewer)||void 0===t?void 0:null===(a=t.teams.nodes[0])||void 0===a?void 0:a.id)||"",name:(null===(o=r.viewer)||void 0===o?void 0:null===(i=o.teams.nodes[0])||void 0===i?void 0:i.name)||"",key:(null===(d=r.viewer)||void 0===d?void 0:null===(n=d.teams.nodes[0])||void 0===n?void 0:n.key)||""},assignee:{id:(null===(s=r.viewer)||void 0===s?void 0:s.id)||"",name:(null===(c=r.viewer)||void 0===c?void 0:c.name)||"",email:(null===(l=r.viewer)||void 0===l?void 0:l.email)||""},labels:{nodes:[]},createdAt:e.createdAt,updatedAt:e.createdAt,priority:2}}))||[]}};if(e.query.includes("teams"))return{teams:{nodes:(null===(d=r.viewer)||void 0===d?void 0:d.teams.nodes.map(e=>({...e,states:{nodes:[{id:"state-backlog",name:"Backlog",type:"backlog",color:"#95a2b3"},{id:"state-todo",name:"Todo",type:"unstarted",color:"#3b82f6"},{id:"state-in-progress",name:"In Progress",type:"started",color:"#f59e0b"},{id:"state-done",name:"Done",type:"completed",color:"#10b981"}]}})))||[]}};if(e.query.includes("projects"))return{projects:{nodes:[{id:"project-excel-copilot",name:"Excel Copilot",description:"SaaS de planilhas colaborativas com IA integrada",state:"started",progress:75,targetDate:"2025-12-31T00:00:00.000Z",createdAt:"2025-01-01T00:00:00.000Z",updatedAt:new Date().toISOString()}]}};return r}catch(e){throw i.logger.error("Erro na integra\xe7\xe3o Linear MCP:",e),Error("Falha na integra\xe7\xe3o Linear MCP: ".concat(e instanceof Error?e.message:"Erro desconhecido"))}}function d(){return!0}}}]);