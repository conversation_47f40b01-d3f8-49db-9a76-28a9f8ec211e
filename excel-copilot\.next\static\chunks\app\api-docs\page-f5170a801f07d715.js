(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1100],{73425:function(e,t,r){Promise.resolve().then(r.bind(r,97551))},81066:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var s=r(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{color:o="currentColor",size:l=24,strokeWidth:d=2,absoluteStrokeWidth:c,children:u,...m}=r;return(0,s.createElement)("svg",{ref:i,...a,width:l,height:l,stroke:o,strokeWidth:c?24*Number(d)/Number(l):d,className:"lucide lucide-".concat(n(e)),...m},[...t.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...(Array.isArray(u)?u:[u])||[]])});return r.displayName="".concat(e),r}},77515:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},42421:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},87592:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},6884:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},98094:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},54817:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(81066).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},97551:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return k}});var s=r(57437),a=r(2265),n=r(48185),i=r(79055),o=r(89733),l=r(86864),d=r(77209),c=r(70402),u=r(4919),m=r(54817),p=r(81066);let f=(0,p.Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var h=r(77515),x=r(42421),v=r(87592);let g=(0,p.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var b=r(6884),y=r(98094),j=r(27776);let N=[{path:"/api/auth/login",method:"POST",summary:"Login de usu\xe1rio",description:"Autentica um usu\xe1rio e retorna token de acesso",tags:["Auth"],requiresAuth:!1,requestBody:{type:"application/json",example:JSON.stringify({email:"<EMAIL>",password:"password123"},null,2)},responses:[{status:200,description:"Login realizado com sucesso",example:JSON.stringify({success:!0,token:"jwt_token_here",user:{id:"1",email:"<EMAIL>"}},null,2)},{status:401,description:"Credenciais inv\xe1lidas",example:JSON.stringify({success:!1,error:"Credenciais inv\xe1lidas"},null,2)}]},{path:"/api/workbooks",method:"GET",summary:"Listar planilhas",description:"Lista todas as planilhas do usu\xe1rio autenticado",tags:["Workbooks"],requiresAuth:!0,parameters:[{name:"page",type:"number",required:!1,description:"N\xfamero da p\xe1gina (padr\xe3o: 1)"},{name:"limit",type:"number",required:!1,description:"Itens por p\xe1gina (padr\xe3o: 10)"}],responses:[{status:200,description:"Lista de planilhas retornada com sucesso",example:JSON.stringify({success:!0,data:[{id:"1",name:"Vendas Q1",createdAt:"2025-06-18T10:00:00Z"},{id:"2",name:"Relat\xf3rio Mensal",createdAt:"2025-06-17T15:30:00Z"}],pagination:{page:1,limit:10,total:2}},null,2)}]},{path:"/api/workbooks",method:"POST",summary:"Criar planilha",description:"Cria uma nova planilha para o usu\xe1rio autenticado",tags:["Workbooks"],requiresAuth:!0,requestBody:{type:"application/json",example:JSON.stringify({name:"Nova Planilha",description:"Descri\xe7\xe3o da planilha",template:"blank"},null,2)},responses:[{status:201,description:"Planilha criada com sucesso",example:JSON.stringify({success:!0,data:{id:"3",name:"Nova Planilha",description:"Descri\xe7\xe3o da planilha",createdAt:"2025-06-18T12:00:00Z"}},null,2)}]},{path:"/api/chat",method:"POST",summary:"Chat com IA",description:"Envia mensagem para processamento por IA e manipula\xe7\xe3o de planilha",tags:["AI"],requiresAuth:!0,requestBody:{type:"application/json",example:JSON.stringify({message:"Adicione uma coluna Total que some as colunas A e B",workbookId:"1",context:"spreadsheet"},null,2)},responses:[{status:200,description:"Comando processado com sucesso",example:JSON.stringify({success:!0,data:{response:"Coluna Total adicionada com sucesso!",operations:["add_column"],result:{columnAdded:"C",formula:"=A1+B1"}}},null,2)}]},{path:"/api/excel/analyze",method:"POST",summary:"Analisar dados",description:"Analisa dados da planilha para obter insights e estat\xedsticas",tags:["Excel"],requiresAuth:!0,requestBody:{type:"application/json",example:JSON.stringify({workbookId:"1",sheetName:"Sheet1",range:"A1:C10",analysisType:"statistics"},null,2)},responses:[{status:200,description:"An\xe1lise conclu\xedda com sucesso",example:JSON.stringify({success:!0,data:{statistics:{mean:45.6,median:42,mode:40,stdDev:12.3},insights:["Dados mostram tend\xeancia crescente","Outliers detectados na linha 7"],charts:[{type:"line",data:"..."}]}},null,2)}]},{path:"/api/metrics",method:"GET",summary:"M\xe9tricas do Sistema",description:"Obt\xe9m m\xe9tricas de desempenho da aplica\xe7\xe3o (apenas administradores)",tags:["Admin"],requiresAuth:!0,adminOnly:!0,responses:[{status:200,description:"M\xe9tricas retornadas com sucesso",example:JSON.stringify({success:!0,data:{activeUsers:1250,totalWorkbooks:5430,apiCalls:125e3,uptime:"99.9%"}},null,2)}]}],w={GET:"bg-green-100 text-green-800 border-green-200",POST:"bg-blue-100 text-blue-800 border-blue-200",PUT:"bg-yellow-100 text-yellow-800 border-yellow-200",PATCH:"bg-orange-100 text-orange-800 border-orange-200",DELETE:"bg-red-100 text-red-800 border-red-200"};function k(){let[e,t]=(0,a.useState)(""),[r,p]=(0,a.useState)("all"),[k,C]=(0,a.useState)(null),[A,q]=(0,a.useState)({}),T=["all",...Array.from(new Set(N.flatMap(e=>e.tags)))],Z=N.filter(t=>{let s=t.path.toLowerCase().includes(e.toLowerCase())||t.summary.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase()),a="all"===r||t.tags.includes(r);return s&&a}),O=e=>{navigator.clipboard.writeText(e),j.toast.success("Copiado para a \xe1rea de transfer\xeancia!")},S=async e=>{var t;A["".concat(e.method,"-").concat(e.path)]||null===(t=e.requestBody)||void 0===t||t.example;try{j.toast.loading("Testando endpoint...",{id:"test-endpoint"}),await new Promise(e=>setTimeout(e,1e3)),j.toast.success("Teste realizado com sucesso!",{id:"test-endpoint",description:"Verifique a resposta abaixo"})}catch(e){j.toast.error("Erro no teste do endpoint",{id:"test-endpoint"})}};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Documenta\xe7\xe3o da API"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground mb-6",children:"Explore e teste todos os endpoints da API do Excel Copilot"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(m.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,s.jsx)(d.I,{placeholder:"Pesquisar endpoints...",value:e,onChange:e=>t(e.target.value),className:"pl-10"})]}),(0,s.jsx)("div",{className:"flex gap-2 flex-wrap",children:T.map(e=>(0,s.jsx)(o.Button,{variant:r===e?"default":"outline",size:"sm",onClick:()=>p(e),children:"all"===e?"Todos":e},e))})]}),(0,s.jsx)(n.Zb,{className:"mb-6",children:(0,s.jsx)(n.aY,{className:"p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Base URL"}),(0,s.jsx)("code",{className:"text-sm bg-muted p-2 rounded block",children:"https://excel-copilot-eight.vercel.app"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Autentica\xe7\xe3o"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Bearer Token via header Authorization"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Formato"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"JSON (application/json)"})]})]})})})]}),(0,s.jsx)("div",{className:"space-y-4",children:Z.map((e,t)=>{var r;let a="".concat(e.method,"-").concat(e.path),m=k===a;return(0,s.jsxs)(n.Zb,{className:"overflow-hidden",children:[(0,s.jsx)(n.Ol,{className:"cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>C(m?null:a),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(i.C,{className:"".concat(w[e.method]," border"),children:e.method}),(0,s.jsxs)("div",{children:[(0,s.jsx)(n.ll,{className:"text-lg",children:e.path}),(0,s.jsx)(n.SZ,{children:e.summary})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.requiresAuth&&(0,s.jsx)(f,{className:"h-4 w-4 text-amber-500"}),e.adminOnly&&(0,s.jsx)(h.Z,{className:"h-4 w-4 text-red-500"}),m?(0,s.jsx)(x.Z,{className:"h-4 w-4"}):(0,s.jsx)(v.Z,{className:"h-4 w-4"})]})]})}),m&&(0,s.jsx)(n.aY,{className:"border-t",children:(0,s.jsxs)(l.mQ,{defaultValue:"overview",className:"w-full",children:[(0,s.jsxs)(l.dr,{className:"grid w-full grid-cols-4",children:[(0,s.jsx)(l.SP,{value:"overview",children:"Vis\xe3o Geral"}),(0,s.jsx)(l.SP,{value:"parameters",children:"Par\xe2metros"}),(0,s.jsx)(l.SP,{value:"responses",children:"Respostas"}),(0,s.jsx)(l.SP,{value:"test",children:"Testar"})]}),(0,s.jsxs)(l.nU,{value:"overview",className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Descri\xe7\xe3o"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.description})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Tags"}),(0,s.jsx)("div",{className:"flex gap-2",children:e.tags.map(e=>(0,s.jsx)(i.C,{variant:"outline",children:e},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Autentica\xe7\xe3o"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.requiresAuth?(0,s.jsxs)(i.C,{variant:"destructive",children:[(0,s.jsx)(f,{className:"h-3 w-3 mr-1"}),"Requer autentica\xe7\xe3o"]}):(0,s.jsxs)(i.C,{variant:"secondary",children:[(0,s.jsx)(g,{className:"h-3 w-3 mr-1"}),"P\xfablico"]}),e.adminOnly&&(0,s.jsxs)(i.C,{variant:"destructive",children:[(0,s.jsx)(h.Z,{className:"h-3 w-3 mr-1"}),"Apenas administradores"]})]})]})]}),(0,s.jsxs)(l.nU,{value:"parameters",className:"space-y-4",children:[e.parameters&&e.parameters.length>0?(0,s.jsx)("div",{className:"space-y-3",children:e.parameters.map((e,t)=>(0,s.jsxs)("div",{className:"border rounded p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("code",{className:"text-sm font-mono",children:e.name}),(0,s.jsx)(i.C,{variant:e.required?"destructive":"secondary",className:"text-xs",children:e.required?"Obrigat\xf3rio":"Opcional"}),(0,s.jsx)(i.C,{variant:"outline",className:"text-xs",children:e.type})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]},t))}):(0,s.jsx)("p",{className:"text-muted-foreground",children:"Nenhum par\xe2metro necess\xe1rio"}),e.requestBody&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Corpo da Requisi\xe7\xe3o"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("pre",{className:"bg-muted p-4 rounded text-sm overflow-x-auto",children:(0,s.jsx)("code",{children:e.requestBody.example})}),(0,s.jsx)(o.Button,{size:"sm",variant:"outline",className:"absolute top-2 right-2",onClick:()=>O(e.requestBody.example),children:(0,s.jsx)(b.Z,{className:"h-3 w-3"})})]})]})]}),(0,s.jsx)(l.nU,{value:"responses",className:"space-y-4",children:null===(r=e.responses)||void 0===r?void 0:r.map((e,t)=>(0,s.jsxs)("div",{className:"border rounded p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)(i.C,{variant:e.status<300?"default":"destructive",className:"text-sm",children:e.status}),(0,s.jsx)("span",{className:"font-medium",children:e.description})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("pre",{className:"bg-muted p-3 rounded text-sm overflow-x-auto",children:(0,s.jsx)("code",{children:e.example})}),(0,s.jsx)(o.Button,{size:"sm",variant:"outline",className:"absolute top-2 right-2",onClick:()=>O(e.example),children:(0,s.jsx)(b.Z,{className:"h-3 w-3"})})]})]},t))}),(0,s.jsx)(l.nU,{value:"test",className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c._,{htmlFor:"test-url",children:"URL do Endpoint"}),(0,s.jsx)(d.I,{id:"test-url",value:"https://excel-copilot-eight.vercel.app".concat(e.path),readOnly:!0,className:"font-mono text-sm"})]}),e.requestBody&&(0,s.jsxs)("div",{children:[(0,s.jsx)(c._,{htmlFor:"test-body",children:"Corpo da Requisi\xe7\xe3o (JSON)"}),(0,s.jsx)(u.Z,{id:"test-body",value:A[a]||e.requestBody.example,onChange:e=>q(t=>({...t,[a]:e.target.value})),className:"font-mono text-sm min-h-[200px]"})]}),(0,s.jsxs)(o.Button,{onClick:()=>S(e),className:"w-full",children:[(0,s.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),"Testar Endpoint"]}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,s.jsxs)("p",{children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"Dica:"})," Para testar endpoints autenticados, certifique-se de estar logado na aplica\xe7\xe3o."]})})]})})]})})]},t)})}),0===Z.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Nenhum endpoint encontrado com os filtros aplicados."})})]})}},79055:function(e,t,r){"use strict";r.d(t,{C:function(){return o}});var s=r(57437),a=r(13027);r(2265);var n=r(49354);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...a})}},89733:function(e,t,r){"use strict";r.d(t,{Button:function(){return u},d:function(){return c}});var s=r(57437),a=r(71538),n=r(13027),i=r(847),o=r(2265),l=r(18043),d=r(49354);let c=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary-dark",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-primary text-primary-foreground border-none shadow-md",success:"bg-success text-success-foreground hover:bg-success/90",info:"bg-info text-info-foreground hover:bg-info/90",warning:"bg-warning text-warning-foreground hover:bg-warning/90",glass:"bg-background/80 backdrop-blur-md border border-border hover:bg-background/90"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-md px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8"},rounded:{default:"rounded-md",full:"rounded-full",xl:"rounded-xl"},cssFeedback:{none:"",scale:"transition-transform active:scale-95",pulse:"transition-all active:scale-95 hover:shadow-md"}},defaultVariants:{variant:"default",size:"default",rounded:"default",cssFeedback:"scale"}}),u=o.forwardRef((e,t)=>{let{className:r,variant:n,size:o,rounded:u,cssFeedback:m,asChild:p=!1,animated:f=!1,icon:h,iconPosition:x="left",children:v,...g}=e,b=p?a.g7:"button",y=(0,s.jsxs)("span",{className:"inline-flex items-center justify-center",children:[h&&"left"===x&&(0,s.jsx)("span",{className:"mr-2",children:h}),v,h&&"right"===x&&(0,s.jsx)("span",{className:"ml-2",children:h})]});if(f){let e={whileTap:{scale:.97},whileHover:["link","ghost"].includes(n)?void 0:{y:-2},transition:{duration:.67*l.zn,ease:l.d}},a=(0,d.cn)(c({variant:n,size:o,rounded:u,cssFeedback:"none",className:r})),m={...g,className:a,...e};return(0,s.jsx)(i.E.button,{ref:t,...m,children:y})}return(0,s.jsx)(b,{className:(0,d.cn)(c({variant:n,size:o,rounded:u,cssFeedback:m,className:r})),ref:t,...g,children:y})});u.displayName="Button"},48185:function(e,t,r){"use strict";r.d(t,{Ol:function(){return d},SZ:function(){return u},Zb:function(){return l},aY:function(){return m},eW:function(){return p},ll:function(){return c}});var s=r(57437),a=r(847),n=r(2265),i=r(18043),o=r(49354);let l=(0,n.forwardRef)((e,t)=>{let{className:r,children:n,hoverable:l=!1,variant:d="default",noPadding:c=!1,animated:u=!1,...m}=e,p=(0,o.cn)("rounded-xl border shadow-sm",{"p-6":!c,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":l&&!u,"border-border bg-card":"default"===d,"border-border/50 bg-transparent":"outline"===d,"bg-card/90 backdrop-blur-md border-border/50":"glass"===d,"bg-gradient-primary text-primary-foreground border-none":"gradient"===d},r);return u?(0,s.jsx)(a.E.div,{ref:t,className:p,...(0,i.Ph)("card"),whileHover:l?i.q.hover:void 0,whileTap:l?i.q.tap:void 0,...m,children:n}):(0,s.jsx)("div",{ref:t,className:p,...m,children:n})});l.displayName="Card";let d=(0,n.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("mb-4 flex flex-col space-y-1.5",r),...a})});d.displayName="CardHeader";let c=(0,n.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let u=(0,n.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})});u.displayName="CardDescription";let m=(0,n.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("card-content",r),...a})});m.displayName="CardContent";let p=(0,n.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center pt-4 mt-auto",r),...a})});p.displayName="CardFooter"},6432:function(e,t,r){"use strict";r.d(t,{RM:function(){return o},aF:function(){return l}});var s=r(2265),a=r(49354);let n={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},i={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"md",r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=arguments.length>3?arguments[3]:void 0;return(0,a.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n[e],i[t],r&&"min-h-[80px] resize-vertical",s)}function l(e,t){return t?s.createElement("div",{className:t},e):e}},77209:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var s=r(57437),a=r(2265),n=r(6432);let i=a.forwardRef((e,t)=>{let{className:r,type:a,wrapperClassName:i,variant:o="default",fieldSize:l="md",inputSize:d,...c}=e,u=(0,s.jsx)("input",{type:a,className:(0,n.RM)(o,d||l,!1,r),ref:t,...c});return(0,n.aF)(u,i)});i.displayName="Input",t.Z=i},70402:function(e,t,r){"use strict";r.d(t,{_:function(){return d}});var s=r(57437),a=r(38364),n=r(13027),i=r(2265),o=r(49354);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.f,{ref:t,className:(0,o.cn)(l(),r),...n})});d.displayName=a.f.displayName},86864:function(e,t,r){"use strict";r.d(t,{SP:function(){return d},dr:function(){return l},mQ:function(){return o},nU:function(){return c}});var s=r(57437),a=r(62447),n=r(2265),i=r(49354);let o=a.fC,l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...n})});l.displayName=a.aV.displayName;let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...n})});d.displayName=a.xz.displayName;let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...n})});c.displayName=a.VY.displayName},4919:function(e,t,r){"use strict";var s=r(57437),a=r(2265),n=r(6432);let i=a.forwardRef((e,t)=>{let{className:r,wrapperClassName:a,variant:i="default",fieldSize:o="md",textareaSize:l,...d}=e,c=(0,s.jsx)("textarea",{className:(0,n.RM)(i,l||o,!0,r),ref:t,...d});return(0,n.aF)(c,a)});i.displayName="Textarea",t.Z=i},38364:function(e,t,r){"use strict";r.d(t,{f:function(){return o}});var s=r(2265),a=r(25171),n=r(57437),i=s.forwardRef((e,t)=>(0,n.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},62447:function(e,t,r){"use strict";r.d(t,{VY:function(){return S},aV:function(){return Z},fC:function(){return T},xz:function(){return O}});var s=r(2265),a=r(78149),n=r(98324),i=r(53398),o=r(31383),l=r(25171),d=r(87513),c=r(91715),u=r(53201),m=r(57437),p="Tabs",[f,h]=(0,n.b)(p,[i.Pc]),x=(0,i.Pc)(),[v,g]=f(p),b=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,onValueChange:a,defaultValue:n,orientation:i="horizontal",dir:o,activationMode:f="automatic",...h}=e,x=(0,d.gm)(o),[g,b]=(0,c.T)({prop:s,onChange:a,defaultProp:null!=n?n:"",caller:p});return(0,m.jsx)(v,{scope:r,baseId:(0,u.M)(),value:g,onValueChange:b,orientation:i,dir:x,activationMode:f,children:(0,m.jsx)(l.WV.div,{dir:x,"data-orientation":i,...h,ref:t})})});b.displayName=p;var y="TabsList",j=s.forwardRef((e,t)=>{let{__scopeTabs:r,loop:s=!0,...a}=e,n=g(y,r),o=x(r);return(0,m.jsx)(i.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:s,children:(0,m.jsx)(l.WV.div,{role:"tablist","aria-orientation":n.orientation,...a,ref:t})})});j.displayName=y;var N="TabsTrigger",w=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,disabled:n=!1,...o}=e,d=g(N,r),c=x(r),u=A(d.baseId,s),p=q(d.baseId,s),f=s===d.value;return(0,m.jsx)(i.ck,{asChild:!0,...c,focusable:!n,active:f,children:(0,m.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":p,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(s)}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(s)}),onFocus:(0,a.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;f||n||!e||d.onValueChange(s)})})})});w.displayName=N;var k="TabsContent",C=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:n,children:i,...d}=e,c=g(k,r),u=A(c.baseId,a),p=q(c.baseId,a),f=a===c.value,h=s.useRef(f);return s.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(o.z,{present:n||f,children:r=>{let{present:s}=r;return(0,m.jsx)(l.WV.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:p,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:s&&i})}})});function A(e,t){return"".concat(e,"-trigger-").concat(t)}function q(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=k;var T=b,Z=j,O=w,S=C},13027:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});var s=r(44839);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.W,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let n=a(t)||a(s);return i[e][n]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,l,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[7142,8638,7776,3526,8194,2971,7023,1744],function(){return e(e.s=73425)}),_N_E=e.O()}]);