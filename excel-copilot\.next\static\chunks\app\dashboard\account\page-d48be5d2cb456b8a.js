(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[813],{33995:function(e,a,r){Promise.resolve().then(r.bind(r,45646))},77424:function(e,a,r){"use strict";r.d(a,{Z:function(){return t}});let t=(0,r(81066).Z)("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},23787:function(e,a,r){"use strict";r.d(a,{Z:function(){return t}});let t=(0,r(81066).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},74109:function(e,a,r){"use strict";r.d(a,{Z:function(){return t}});let t=(0,r(81066).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},45646:function(e,a,r){"use strict";r.r(a),r.d(a,{default:function(){return $}});var t=r(57437),s=r(74109),n=r(30998),i=r(2265),l=r(27776),o=r(20500),c=r(77424),d=r(88592),u=r(23787),m=r(55430),x=r(89733),p=r(48185),f=r(98324),h=r(25171),v="Progress",[j,N]=(0,f.b)(v),[b,y]=j(v),g=i.forwardRef((e,a)=>{var r,s,n,i;let{__scopeProgress:l,value:o=null,max:c,getValueLabel:d=P,...u}=e;(c||0===c)&&!S(c)&&console.error((r="".concat(c),s="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(s,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let m=S(c)?c:100;null===o||Z(o,m)||console.error((n="".concat(o),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let x=Z(o,m)?o:null,p=E(x)?d(x,m):void 0;return(0,t.jsx)(b,{scope:l,value:x,max:m,children:(0,t.jsx)(h.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":E(x)?x:void 0,"aria-valuetext":p,role:"progressbar","data-state":C(x,m),"data-value":null!=x?x:void 0,"data-max":m,...u,ref:a})})});g.displayName=v;var w="ProgressIndicator",k=i.forwardRef((e,a)=>{var r;let{__scopeProgress:s,...n}=e,i=y(w,s);return(0,t.jsx)(h.WV.div,{"data-state":C(i.value,i.max),"data-value":null!==(r=i.value)&&void 0!==r?r:void 0,"data-max":i.max,...n,ref:a})});function P(e,a){return"".concat(Math.round(e/a*100),"%")}function C(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function E(e){return"number"==typeof e}function S(e){return E(e)&&!isNaN(e)&&e>0}function Z(e,a){return E(e)&&!isNaN(e)&&e<=a&&e>=0}k.displayName=w;var _=r(49354);let A=i.forwardRef((e,a)=>{let{className:r,value:s,...n}=e;return(0,t.jsx)(g,{ref:a,className:(0,_.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",r),...n,children:(0,t.jsx)(k,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});A.displayName=g.displayName;var M=r(58743);function O(e){let{subscription:a,onCreatePortalSession:r}=e,[s,n]=(0,i.useState)(!1),f=Math.min(Math.round(a.apiCallsUsed/a.apiCallsLimit*100),100),h=e=>e?new Intl.DateTimeFormat("pt-BR",{day:"numeric",month:"long",year:"numeric"}).format(new Date(e)):"N/A",v=async()=>{try{n(!0);let e=await r();if(e)window.location.href=e;else throw Error("N\xe3o foi poss\xedvel obter o link do portal")}catch(e){console.error("Erro ao abrir portal de assinatura:",e),l.toast.error("N\xe3o foi poss\xedvel acessar o portal de faturamento. Tente novamente mais tarde.")}finally{n(!1)}};return(0,t.jsxs)(p.Zb,{className:"border-primary/30",children:[(0,t.jsxs)(p.Ol,{className:"pb-2",children:[(0,t.jsxs)(p.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(o.Z,{className:"free"!==a.plan?"text-primary":"text-muted-foreground",size:18}),(0,M.Nt)(a.plan),"past_due"===a.status&&(0,t.jsx)("span",{className:"text-xs font-normal ml-2 py-0.5 px-2 rounded bg-amber-100 text-amber-800 dark:bg-amber-900/60 dark:text-amber-200",children:"Pagamento pendente"}),a.cancelAtPeriodEnd&&(0,t.jsx)("span",{className:"text-xs font-normal ml-2 py-0.5 px-2 rounded bg-rose-100 text-rose-800 dark:bg-rose-900/60 dark:text-rose-200",children:"Cancelamento agendado"})]}),(0,t.jsx)(p.SZ,{children:"free"===a.id?"Plano gratuito com recursos b\xe1sicos":"Acesso a todos os recursos pro"})]}),(0,t.jsxs)(p.aY,{className:"pb-2 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1.5 font-medium",children:[(0,t.jsx)(c.Z,{size:16,className:"text-muted-foreground"}),(0,t.jsx)("span",{children:"Uso da API"})]}),(0,t.jsxs)("span",{children:[a.apiCallsUsed," / ",a.apiCallsLimit," chamadas"]})]}),(0,t.jsx)(A,{value:f,className:"h-2"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:f>=80?"Voc\xea est\xe1 pr\xf3ximo do seu limite mensal.":"Seu uso est\xe1 dentro do esperado."})]}),"free"!==a.id&&(0,t.jsxs)("div",{className:"space-y-4 pt-2",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground mb-1",children:"Status"}),(0,t.jsxs)("p",{className:"font-medium",children:["active"===a.status&&"Ativo","trialing"===a.status&&"Em trial","past_due"===a.status&&"Pagamento pendente","canceled"===a.status&&"Cancelado","incomplete"===a.status&&"Incompleto"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground mb-1",children:"Pr\xf3xima cobran\xe7a"}),(0,t.jsx)("p",{className:"font-medium",children:h(a.currentPeriodEnd)})]})]}),a.cancelAtPeriodEnd&&(0,t.jsx)("div",{className:"rounded-md bg-amber-50 dark:bg-amber-950/30 p-3 text-sm text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-800/40",children:(0,t.jsxs)("p",{children:["Sua assinatura ser\xe1 encerrada em ",h(a.currentPeriodEnd),"."]})})]})]}),(0,t.jsx)(p.eW,{className:"pt-2",children:"free"!==a.id?(0,t.jsx)(x.Button,{onClick:()=>v(),className:"w-full",disabled:s,children:s?"Carregando...":(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Z,{className:"mr-2 h-4 w-4"}),"Gerenciar Assinatura",(0,t.jsx)(u.Z,{className:"ml-2 h-3.5 w-3.5"})]})}):(0,t.jsxs)(x.Button,{variant:"outline",className:"w-full",onClick:()=>window.location.href="/pricing",children:[(0,t.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Fazer Upgrade"]})})]})}function $(){var e,a;let{data:r}=(0,n.useSession)(),[o,c]=(0,i.useState)(null),[d,u]=(0,i.useState)(!0);(0,i.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/user/subscription"),a=await e.json();e.ok?c(a.subscription):c({id:"free",plan:"free",status:"active",currentPeriodEnd:null,apiCallsUsed:0,apiCallsLimit:50,cancelAtPeriodEnd:!1})}catch(e){l.toast.error("N\xe3o foi poss\xedvel carregar os dados da assinatura")}finally{u(!1)}};r&&e()},[r]);let m=async()=>{try{let e=await fetch("/api/billing/customer-portal",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({returnUrl:"".concat(window.location.origin,"/dashboard/account")})}),a=await e.json();if(!e.ok)throw Error(a.error||"Erro ao acessar o portal");return a.url}catch(e){return l.toast.error("N\xe3o foi poss\xedvel acessar o portal de gerenciamento"),null}};return d?(0,t.jsx)("div",{className:"flex items-center justify-center h-[calc(100vh-200px)]",children:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,t.jsx)(s.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Carregando informa\xe7\xf5es da conta..."})]})}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Sua Conta"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)(p.Zb,{children:[(0,t.jsxs)(p.Ol,{children:[(0,t.jsx)(p.ll,{children:"Perfil"}),(0,t.jsx)(p.SZ,{children:"Suas informa\xe7\xf5es pessoais"})]}),(0,t.jsx)(p.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-muted-foreground",children:"Nome"}),(0,t.jsx)("p",{children:(null==r?void 0:null===(e=r.user)||void 0===e?void 0:e.name)||"N\xe3o informado"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-muted-foreground",children:"Email"}),(0,t.jsx)("p",{children:(null==r?void 0:null===(a=r.user)||void 0===a?void 0:a.email)||"N\xe3o informado"})]})]})})]})}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Sua Assinatura"}),o&&(0,t.jsx)(O,{subscription:o,onCreatePortalSession:m})]})]})]})}},98324:function(e,a,r){"use strict";r.d(a,{b:function(){return i},k:function(){return n}});var t=r(2265),s=r(57437);function n(e,a){let r=t.createContext(a),n=e=>{let{children:a,...n}=e,i=t.useMemo(()=>n,Object.values(n));return(0,s.jsx)(r.Provider,{value:i,children:a})};return n.displayName=e+"Provider",[n,function(s){let n=t.useContext(r);if(n)return n;if(void 0!==a)return a;throw Error(`\`${s}\` must be used within \`${e}\``)}]}function i(e,a=[]){let r=[],n=()=>{let a=r.map(e=>t.createContext(e));return function(r){let s=r?.[e]||a;return t.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return n.scopeName=e,[function(a,n){let i=t.createContext(n),l=r.length;r=[...r,n];let o=a=>{let{scope:r,children:n,...o}=a,c=r?.[e]?.[l]||i,d=t.useMemo(()=>o,Object.values(o));return(0,s.jsx)(c.Provider,{value:d,children:n})};return o.displayName=a+"Provider",[o,function(r,s){let o=s?.[e]?.[l]||i,c=t.useContext(o);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${a}\``)}]},function(...e){let a=e[0];if(1===e.length)return a;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((a,{useScope:r,scopeName:t})=>{let s=r(e)[`__scope${t}`];return{...a,...s}},{});return t.useMemo(()=>({[`__scope${a.scopeName}`]:s}),[s])}};return r.scopeName=a.scopeName,r}(n,...a)]}},25171:function(e,a,r){"use strict";r.d(a,{WV:function(){return l},jH:function(){return o}});var t=r(2265),s=r(54887),n=r(71538),i=r(57437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let r=(0,n.Z8)(`Primitive.${a}`),s=t.forwardRef((e,t)=>{let{asChild:s,...n}=e,l=s?r:a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...n,ref:t})});return s.displayName=`Primitive.${a}`,{...e,[a]:s}},{});function o(e,a){e&&s.flushSync(()=>e.dispatchEvent(a))}}},function(e){e.O(0,[7142,8638,7776,5660,2848,8194,6702,2971,7023,1744],function(){return e(e.s=33995)}),_N_E=e.O()}]);