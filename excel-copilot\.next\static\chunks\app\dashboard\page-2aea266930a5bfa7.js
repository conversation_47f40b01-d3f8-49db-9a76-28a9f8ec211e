(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7702],{5352:function(e,a,s){Promise.resolve().then(s.bind(s,17139))},17139:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return ep}});var t=s(57437),r=s(33142),l=s(47201),o=s(36356),i=s(77424),n=s(70518),c=s(87592),d=s(74109),m=s(33907),u=s(54817),h=s(74697),x=s(40933),p=s(24241),f=s(66648),j=s(16463),b=s(30998),v=s(2265),g=s(27776),N=s(92513),w=s(58184),k=s(6884),y=s(11240),C=s(39127),E=s(38711),A=s(34567),Z=s(89733),S=s(48185),R=s(49354);function T(e){let{className:a,onCreateWorkbook:s,onImportFile:r,recentWorkbooks:l=[]}=e,i=(0,j.useRouter)(),[n,c]=(0,v.useState)(!1),[d,u]=(0,v.useState)(!1),h=async()=>{if(s){s();return}try{c(!0),i.push("/dashboard?create=true")}catch(e){g.toast.error("Erro ao criar planilha")}finally{c(!1)}},x=async()=>{if(r){r();return}try{u(!0);let e=document.createElement("input");e.type="file",e.accept=".xlsx,.xls,.csv",e.onchange=async e=>{var a;let s=null===(a=e.target.files)||void 0===a?void 0:a[0];s&&g.toast.success("Arquivo ".concat(s.name," selecionado para importa\xe7\xe3o"))},e.click()}catch(e){g.toast.error("Erro ao importar arquivo")}finally{u(!1)}},p=async()=>{if(0===l.length){g.toast.error("Nenhuma planilha recente encontrada");return}let e=l[0];if(!e){g.toast.error("Nenhuma planilha recente encontrada");return}try{g.toast.success('Duplicando planilha "'.concat(e.name,'"'))}catch(e){g.toast.error("Erro ao duplicar planilha")}},f=[{id:"create",title:"Nova Planilha",description:"Criar planilha em branco",icon:(0,t.jsx)(N.Z,{className:"h-5 w-5"}),action:h,disabled:n},{id:"ai-create",title:"Criar com IA",description:"Usar assistente inteligente",icon:(0,t.jsx)(m.Z,{className:"h-5 w-5"}),action:()=>{i.push("/dashboard?create=true&ai=true")},badge:"IA"},{id:"import",title:"Importar Arquivo",description:"Excel, CSV ou outros formatos",icon:(0,t.jsx)(w.Z,{className:"h-5 w-5"}),action:x,variant:"outline",disabled:d},{id:"duplicate",title:"Duplicar Recente",description:"Copiar \xfaltima planilha editada",icon:(0,t.jsx)(k.Z,{className:"h-5 w-5"}),action:p,variant:"outline",disabled:0===l.length}],b=[{id:"templates",title:"Templates",description:"Modelos prontos",icon:(0,t.jsx)(o.Z,{className:"h-4 w-4"}),action:()=>{i.push("/dashboard?templates=true")},variant:"secondary"},{id:"collaborate",title:"Colaborar",description:"Planilhas compartilhadas",icon:(0,t.jsx)(y.Z,{className:"h-4 w-4"}),action:()=>{i.push("/dashboard?tab=shared")},variant:"secondary"}];return(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",a),children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-lg",children:"A\xe7\xf5es R\xe1pidas"}),(0,t.jsx)(S.SZ,{children:"Comece rapidamente com suas planilhas"})]}),(0,t.jsxs)(S.aY,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:f.map(e=>(0,t.jsxs)(Z.Button,{variant:e.variant||"default",className:(0,R.cn)("h-auto p-4 flex flex-col items-start space-y-2 relative",e.disabled&&"opacity-50 cursor-not-allowed"),onClick:e.action,disabled:e.disabled,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.icon,(0,t.jsx)("span",{className:"font-medium",children:e.title})]}),e.badge&&(0,t.jsx)("span",{className:"text-xs bg-primary/20 text-primary px-2 py-1 rounded-full",children:e.badge})]}),(0,t.jsx)("p",{className:"text-xs text-left opacity-80",children:e.description})]},e.id))}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:b.map(e=>(0,t.jsxs)(Z.Button,{variant:e.variant||"secondary",size:"sm",className:"flex items-center space-x-2",onClick:e.action,disabled:e.disabled,children:[e.icon,(0,t.jsx)("span",{children:e.title})]},e.id))})]})]})}function D(e){let{className:a}=e,s=(0,j.useRouter)(),r=[{id:"financial",name:"Controle Financeiro",icon:(0,t.jsx)(C.Z,{className:"h-4 w-4"}),color:"text-green-600"},{id:"dashboard",name:"Dashboard",icon:(0,t.jsx)(E.Z,{className:"h-4 w-4"}),color:"text-blue-600"},{id:"calculator",name:"Calculadora",icon:(0,t.jsx)(A.Z,{className:"h-4 w-4"}),color:"text-purple-600"}],l=e=>{s.push("/dashboard?template=".concat(e)),g.toast.success("Carregando template...")};return(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",a),children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-base",children:"Templates Populares"}),(0,t.jsx)(S.SZ,{children:"Comece com modelos prontos"})]}),(0,t.jsxs)(S.aY,{children:[(0,t.jsx)("div",{className:"space-y-2",children:r.map(e=>(0,t.jsxs)(Z.Button,{variant:"ghost",className:"w-full justify-start h-auto p-3",onClick:()=>l(e.id),children:[(0,t.jsx)("div",{className:(0,R.cn)("mr-3",e.color),children:e.icon}),(0,t.jsx)("span",{className:"text-sm",children:e.name})]},e.id))}),(0,t.jsx)(Z.Button,{variant:"outline",size:"sm",className:"w-full mt-3",onClick:()=>s.push("/dashboard?templates=true"),children:"Ver Todos os Templates"})]})]})}function P(e){let{recentWorkbooks:a=[],className:s}=e,r=(0,j.useRouter)(),l=e=>{r.push("/workbook/".concat(e))};return 0===a.length?null:(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",s),children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-base",children:"Acesso R\xe1pido"}),(0,t.jsx)(S.SZ,{children:"Suas planilhas mais recentes"})]}),(0,t.jsxs)(S.aY,{children:[(0,t.jsx)("div",{className:"space-y-2",children:a.slice(0,3).map(e=>(0,t.jsxs)(Z.Button,{variant:"ghost",className:"w-full justify-start h-auto p-3",onClick:()=>l(e.id),children:[(0,t.jsx)(o.Z,{className:"h-4 w-4 mr-3 text-primary"}),(0,t.jsxs)("div",{className:"flex-1 text-left",children:[(0,t.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Editado ",new Date(e.updatedAt).toLocaleDateString("pt-BR")]})]})]},e.id))}),a.length>3&&(0,t.jsxs)(Z.Button,{variant:"outline",size:"sm",className:"w-full mt-3",onClick:()=>r.push("/dashboard?tab=recent"),children:["Ver Todas (",a.length,")"]})]})]})}var z=s(39451),B=s(37733),W=s(23787),F=s(87138),O=s(79055),V=s(31590),I=s(2183);let _={workbook_created:{icon:o.Z,color:"text-green-600",bgColor:"bg-green-50",label:"Cria\xe7\xe3o"},workbook_edited:{icon:z.Z,color:"text-blue-600",bgColor:"bg-blue-50",label:"Edi\xe7\xe3o"},ai_command:{icon:m.Z,color:"text-purple-600",bgColor:"bg-purple-50",label:"IA"},collaboration:{icon:y.Z,color:"text-orange-600",bgColor:"bg-orange-50",label:"Colabora\xe7\xe3o"}};function M(e){var a,s;let{activity:o}=e,i=_[o.type],n=i.icon,c=(0,r.Q)(new Date(o.timestamp),{addSuffix:!0,locale:l.F}),d=null===(a=o.metadata)||void 0===a?void 0:a.workbookId,m=null===(s=o.metadata)||void 0===s?void 0:s.hasAI;return(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group",children:[(0,t.jsx)("div",{className:(0,R.cn)("flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",i.bgColor),children:(0,t.jsx)(n,{className:(0,R.cn)("h-4 w-4",i.color)})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:o.title}),(0,t.jsx)(O.C,{variant:"outline",className:"text-xs",children:i.label}),m&&(0,t.jsx)(O.C,{variant:"secondary",className:"text-xs bg-purple-100 text-purple-700",children:"IA"})]}),(0,t.jsxs)(V.h_,{children:[(0,t.jsx)(V.$F,{asChild:!0,children:(0,t.jsx)(Z.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsx)(B.Z,{className:"h-3 w-3"})})}),(0,t.jsxs)(V.AW,{align:"end",children:[d&&(0,t.jsx)(V.Xi,{asChild:!0,children:(0,t.jsxs)(F.default,{href:"/workbook/".concat(d),children:[(0,t.jsx)(W.Z,{className:"h-3 w-3 mr-2"}),"Abrir Planilha"]})}),(0,t.jsx)(V.Xi,{children:"Ver Detalhes"})]})]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1 truncate",children:o.description}),o.metadata&&(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.Z,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:c})]}),o.metadata.sharedBy&&(0,t.jsxs)("span",{children:["por ",o.metadata.sharedBy]}),o.metadata.sharedWith&&(0,t.jsxs)("span",{children:["com ",o.metadata.sharedWith]})]})]})]})}function L(){return(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3",children:[(0,t.jsx)(I.O,{className:"w-8 h-8 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(I.O,{className:"h-4 w-32"}),(0,t.jsx)(I.O,{className:"h-4 w-12"})]}),(0,t.jsx)(I.O,{className:"h-3 w-48"}),(0,t.jsx)(I.O,{className:"h-3 w-24"})]})]})}function U(e){let{activities:a,isLoading:s,className:r,maxItems:l=10}=e,o=a.slice(0,l);return(0,t.jsxs)(S.Zb,{className:(0,R.cn)("",r),children:[(0,t.jsx)(S.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.ll,{className:"text-lg",children:"Atividade Recente"}),(0,t.jsx)(S.SZ,{children:"Suas \xfaltimas a\xe7\xf5es no Excel Copilot"})]}),!s&&a.length>l&&(0,t.jsxs)(Z.Button,{variant:"outline",size:"sm",children:["Ver Todas (",a.length,")"]})]})}),(0,t.jsx)(S.aY,{className:"p-0",children:s?(0,t.jsx)("div",{className:"space-y-1",children:Array.from({length:5}).map((e,a)=>(0,t.jsx)(L,{},a))}):o.length>0?(0,t.jsx)("div",{className:"space-y-1",children:o.map(e=>(0,t.jsx)(M,{activity:e},e.id))}):(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,t.jsx)(x.Z,{className:"h-8 w-8 text-muted-foreground mb-2"}),(0,t.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Nenhuma atividade recente"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Suas a\xe7\xf5es aparecer\xe3o aqui conforme voc\xea usa o Excel Copilot"})]})})]})}var Y=s(77515),q=s(24258),X=s(97529),Q=s(10883),J=s(84666);function $(e){let{title:a,description:s,icon:r,action:l,className:o}=e;return(0,t.jsxs)("div",{className:(0,R.cn)("flex flex-col items-center justify-center p-8 text-center","bg-gray-50 dark:bg-gray-900 border border-dashed rounded-lg","min-h-[220px]",o),children:[r&&(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:r}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-200",children:a}),s&&(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400 max-w-md",children:s}),l&&(0,t.jsx)("div",{className:"mt-5",children:l})]})}var H=s(188);let K=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("table",{ref:a,className:(0,R.cn)("w-full caption-bottom text-sm",s),...r})});K.displayName="Table";let G=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{ref:a,className:(0,R.cn)("[&_tr]:border-b",s),...r})});G.displayName="TableHeader";let ee=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{ref:a,className:(0,R.cn)("[&_tr:last-child]:border-0",s),...r})});ee.displayName="TableBody",v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{ref:a,className:(0,R.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let ea=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{ref:a,className:(0,R.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});ea.displayName="TableRow";let es=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{ref:a,className:(0,R.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})});es.displayName="TableHead";let et=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{ref:a,className:(0,R.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})});et.displayName="TableCell";let er=v.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("caption",{ref:a,className:(0,R.cn)("mt-4 text-sm text-muted-foreground",s),...r})});er.displayName="TableCaption";var el=s(18473);function eo(e){var a,s,i;let{searchQuery:n=""}=e,c=(0,j.useRouter)(),{data:d}=(0,b.useSession)(),[m,u]=(0,v.useState)([]),[h,f]=(0,v.useState)(!0),[w,y]=(0,v.useState)(null),{fetchWithCSRF:C}=(0,J.useFetchWithCSRF)(),[E,A]=(0,v.useState)(!1),S=e=>(0,r.Q)(e,{addSuffix:!0,locale:l.F}),R=(0,v.useCallback)(async()=>{var e,a,s,t;try{let t;if(f(!0),!(null==d?void 0:d.user)){A(!0),u([]),f(!1);return}let r=0,l=!1;for(;r<3&&!l;)try{r++;let e=new URLSearchParams({...n&&{search:n}}),a=await C("/api/workbooks?".concat(e.toString()),{headers:{"Content-Type":"application/json"}});if(a.ok)l=!0,t=await a.json();else{let e="";try{let s=await a.json();e=s.details||s.error||""}catch(e){}if(401===a.status)throw Error("N\xe3o autorizado: ".concat(e));if(r<3)await new Promise(e=>setTimeout(e,1e3*r));else throw Error("API retornou c\xf3digo ".concat(a.status,": ").concat(e))}}catch(e){if(e instanceof Error&&(null===(s=e.message)||void 0===s?void 0:s.includes("N\xe3o autorizado"))){g.toast.error("Voc\xea precisa estar autenticado para acessar suas planilhas"),A(!0),u([]),f(!1);return}if(r<3)await new Promise(e=>setTimeout(e,1e3*r));else throw e}if(!l)throw Error("M\xe1ximo de tentativas atingido ao carregar planilhas");el.logger.debug("WorkbooksTable: Processando workbooks",{responseDataKeys:Object.keys(t||{}),hasData:!!(null==t?void 0:t.data),hasWorkbooks:!!(null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.workbooks)||!!(null==t?void 0:t.workbooks)});let o=(null==t?void 0:null===(a=t.data)||void 0===a?void 0:a.workbooks)||(null==t?void 0:t.workbooks)||[];el.logger.debug("WorkbooksTable: Array de workbooks extra\xeddo",{count:o.length,isArray:Array.isArray(o)});let i=o.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));u(i),A(!1)}catch(e){el.logger.error("WorkbooksTable: Erro ao carregar planilhas",e,{userId:null==d?void 0:null===(t=d.user)||void 0===t?void 0:t.id,searchQuery:n,attempts:"max_attempts_reached"}),e instanceof Error?e.message.includes("401")||e.message.includes("unauthorized")?g.toast.error("Sess\xe3o expirada. Fa\xe7a login novamente."):e.message.includes("network")||e.message.includes("fetch")?g.toast.error("Erro de conex\xe3o. Verificando conectividade..."):e.message.includes("timeout")?g.toast.error("Tempo limite excedido. Tente novamente."):g.toast.error("Erro ao carregar planilhas: ".concat(e.message)):g.toast.error("Erro desconhecido ao carregar planilhas. Tente novamente mais tarde."),A(!0),u([])}finally{el.logger.debug("WorkbooksTable: Finalizando carregamento",{workbooksCount:m.length,hasError:E}),f(!1)}},[C,null==d?void 0:null===(a=d.user)||void 0===a?void 0:a.id,n]),T=()=>{A(!1),f(!0),R()};(0,v.useEffect)(()=>{var e;(null==d?void 0:null===(e=d.user)||void 0===e?void 0:e.id)?R():f(!1)},[null==d?void 0:null===(s=d.user)||void 0===s?void 0:s.id]),(0,v.useEffect)(()=>{var e;(null==d?void 0:null===(e=d.user)||void 0===e?void 0:e.id)&&void 0!==n&&R()},[n,R,null==d?void 0:null===(i=d.user)||void 0===i?void 0:i.id]);let D=e=>{c.push("/workbook/".concat(e))},P=async e=>{try{let a=await C("/api/workbooks/".concat(e,"/duplicate"),{method:"POST",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Erro ao duplicar planilha");let s=await a.json(),t={...s.workbook,createdAt:new Date(s.workbook.createdAt),updatedAt:new Date(s.workbook.updatedAt),sheets:Array.isArray(s.workbook.sheets)?s.workbook.sheets:s.workbook.sheetsCount||0};u([...m,t]),g.toast.success("Planilha duplicada com sucesso!")}catch(s){var a;el.logger.error("WorkbooksTable: Erro ao duplicar workbook",s,{workbookId:e,userId:null==d?void 0:null===(a=d.user)||void 0===a?void 0:a.id}),g.toast.error("N\xe3o foi poss\xedvel duplicar a planilha")}},z=async e=>{y(e);try{if(!(await C("/api/workbooks/".concat(e),{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao excluir planilha");u(m.filter(a=>a.id!==e)),g.toast.success("Planilha exclu\xedda com sucesso!")}catch(s){var a;el.logger.error("WorkbooksTable: Erro ao excluir workbook",s,{workbookId:e,userId:null==d?void 0:null===(a=d.user)||void 0===a?void 0:a.id}),g.toast.error("N\xe3o foi poss\xedvel excluir a planilha")}finally{y(null)}};return h?(0,t.jsx)("div",{className:"w-full py-10 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"})}):E?(0,t.jsx)("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:(0,t.jsx)($,{icon:(0,t.jsx)(Y.Z,{className:"h-12 w-12 text-destructive"}),title:"Erro ao carregar planilhas",description:"N\xe3o foi poss\xedvel carregar suas planilhas. Isso pode ser um problema tempor\xe1rio.",action:(0,t.jsx)(Z.Button,{onClick:()=>T(),children:"Tentar Novamente"})})}):0===m.length?(0,t.jsx)("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:(0,t.jsx)($,{icon:(0,t.jsx)(o.Z,{className:"h-12 w-12"}),title:"Nenhuma planilha encontrada",description:n?'N\xe3o encontramos planilhas com "'.concat(n,'". Tente outro termo de busca.'):"Voc\xea ainda n\xe3o criou nenhuma planilha. Comece criando sua primeira planilha agora.",action:n?void 0:(0,t.jsxs)(H.MD,{onClick:()=>c.push("/dashboard?create=true"),children:[(0,t.jsx)(N.Z,{className:"h-4 w-4 mr-2"}),"Criar Nova Planilha"]})})}):(0,t.jsx)("div",{className:"w-full",children:(0,t.jsxs)(K,{children:[(0,t.jsx)(er,{children:"Lista de suas planilhas Excel"}),(0,t.jsx)(G,{children:(0,t.jsxs)(ea,{children:[(0,t.jsx)(es,{className:"w-[50%]",children:"Nome"}),(0,t.jsx)(es,{children:"Folhas"}),(0,t.jsx)(es,{children:"Criado"}),(0,t.jsx)(es,{children:"Modificado"}),(0,t.jsx)(es,{className:"text-right",children:"A\xe7\xf5es"})]})}),(0,t.jsx)(ee,{children:m.map(e=>(0,t.jsxs)(ea,{className:"cursor-pointer hover:bg-muted/50",children:[(0,t.jsxs)(et,{className:"font-medium flex items-center gap-2",onClick:()=>D(e.id),children:[(0,t.jsx)(o.Z,{className:"h-4 w-4 text-primary"}),e.name]}),(0,t.jsx)(et,{onClick:()=>D(e.id),children:(0,t.jsxs)(O.C,{variant:"outline",className:"text-xs",children:["number"==typeof e.sheets?e.sheets:e.sheets.length," ",("number"==typeof e.sheets?e.sheets:e.sheets.length)>1?"folhas":"folha"]})}),(0,t.jsx)(et,{onClick:()=>D(e.id),children:(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,t.jsx)(p.Z,{className:"h-3 w-3 mr-1"}),S(e.createdAt)]})}),(0,t.jsx)(et,{onClick:()=>D(e.id),children:(0,t.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[(0,t.jsx)(x.Z,{className:"h-3 w-3 mr-1"}),S(e.updatedAt)]})}),(0,t.jsx)(et,{className:"text-right",children:(0,t.jsxs)(V.h_,{children:[(0,t.jsx)(V.$F,{asChild:!0,children:(0,t.jsx)(H.Kk,{variant:"ghost",size:"icon",actionId:e.id,onAction:()=>{},children:(0,t.jsx)(q.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(V.AW,{align:"end",children:[(0,t.jsx)(V.Ju,{children:"Op\xe7\xf5es"}),(0,t.jsx)(V.VD,{}),(0,t.jsxs)(V.Xi,{onClick:()=>D(e.id),children:[(0,t.jsx)(X.Z,{className:"h-4 w-4 mr-2"}),"Editar"]}),(0,t.jsxs)(V.Xi,{onClick:()=>P(e.id),children:[(0,t.jsx)(k.Z,{className:"h-4 w-4 mr-2"}),"Duplicar"]}),(0,t.jsx)(V.VD,{}),(0,t.jsx)(V.Xi,{className:"text-destructive focus:text-destructive",onClick:()=>z(e.id),disabled:w===e.id,children:w===e.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 mr-2 rounded-full border-2 border-destructive/20 border-t-destructive animate-spin"}),"Excluindo..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Q.Z,{className:"h-4 w-4 mr-2"}),"Excluir"]})})]})]})})]},e.id))})]})})}var ei=s(70402),en=s(86864);function ec(e){let{workbookId:a,onComplete:s,allowDuplicate:r=!0,allowDelete:l=!0,buttonVariant:o="ghost",buttonSize:i="icon",onlyEdit:n=!1}=e,c=(0,j.useRouter)(),{fetchWithCSRF:d}=(0,J.useFetchWithCSRF)(),[m,u]=(0,v.useState)(!1),h=e=>{e&&e.stopPropagation(),c.push("/workbook/".concat(a)),s&&s()},x=async e=>{e&&e.stopPropagation();try{if(!(await d("/api/workbooks/".concat(a,"/duplicate"),{method:"POST",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao duplicar planilha");g.toast.success("Planilha duplicada com sucesso!"),s&&s()}catch(e){console.error("Erro ao duplicar workbook:",e),g.toast.error("N\xe3o foi poss\xedvel duplicar a planilha")}},p=async e=>{e&&e.stopPropagation();try{if(u(!0),!(await d("/api/workbooks/".concat(a),{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok)throw Error("Erro ao excluir planilha");g.toast.success("Planilha exclu\xedda com sucesso!"),s&&s()}catch(e){console.error("Erro ao excluir workbook:",e),g.toast.error("N\xe3o foi poss\xedvel excluir a planilha")}finally{u(!1)}};return n?(0,t.jsxs)(Z.Button,{variant:o,size:i,onClick:h,"aria-label":"Editar planilha",children:[(0,t.jsx)(X.Z,{className:"h-4 w-4 mr-2"}),"Editar"]}):(0,t.jsxs)(V.h_,{children:[(0,t.jsx)(V.$F,{asChild:!0,onClick:e=>e.stopPropagation(),children:(0,t.jsx)(Z.Button,{variant:o,size:i,"aria-label":"Op\xe7\xf5es da planilha",children:(0,t.jsx)(q.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(V.AW,{align:"end",children:[(0,t.jsx)(V.Ju,{children:"Op\xe7\xf5es"}),(0,t.jsx)(V.VD,{}),(0,t.jsxs)(V.Xi,{onClick:e=>h(e),children:[(0,t.jsx)(X.Z,{className:"h-4 w-4 mr-2"}),"Editar"]}),r&&(0,t.jsxs)(V.Xi,{onClick:e=>x(e),children:[(0,t.jsx)(k.Z,{className:"h-4 w-4 mr-2"}),"Duplicar"]}),l&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(V.VD,{}),(0,t.jsxs)(V.Xi,{className:"text-destructive focus:text-destructive",onClick:e=>p(e),disabled:m,children:[(0,t.jsx)(Q.Z,{className:"h-4 w-4 mr-2"}),m?"Excluindo...":"Excluir"]})]})]})]})}var ed=s(82518),em=s(77209),eu=s(4919);let eh={headers:["A","B","C","D","E"],rows:[["","","","",""],["","","","",""],["","","","",""],["","","","",""],["","","","",""]],charts:[],name:"Nova Planilha"},ex=[{title:"Planilha de Finan\xe7as",description:"Crie uma planilha para controle de gastos mensais com categorias",icon:(0,t.jsx)(o.Z,{className:"h-6 w-6 text-green-500"}),command:"Criar planilha de controle financeiro pessoal com categorias de gastos e receitas e balan\xe7o mensal"},{title:"Controle de Tarefas",description:"Organize suas tarefas com datas, status e prioridades",icon:(0,t.jsx)(o.Z,{className:"h-6 w-6 text-blue-500"}),command:"Criar planilha de gerenciamento de tarefas com datas, status, respons\xe1veis e prioridades"},{title:"Dashboard de Vendas",description:"Visualize dados de vendas com gr\xe1ficos e tabelas din\xe2micas",icon:(0,t.jsx)(i.Z,{className:"h-6 w-6 text-indigo-500"}),command:"Criar dashboard de vendas com tabela de dados, gr\xe1fico de barras para vendas mensais e gr\xe1fico de pizza para produtos"}];function ep(){var e,a;let s=(0,j.useRouter)(),{data:i,status:N}=(0,b.useSession)(),[w,k]=(0,v.useState)(""),[y,C]=(0,v.useState)(!1),[E,A]=(0,v.useState)(""),[R,z]=(0,v.useState)(""),[B,W]=(0,v.useState)(""),[F,O]=(0,v.useState)(!1),V=(0,j.useSearchParams)(),{csrfToken:I}=(0,J.useCSRF)(),[_,M]=(0,v.useState)([]),[L,Y]=(0,v.useState)([]),q={recentActivity:[]},[X,Q]=(0,v.useState)(!1),[H,er]=(0,v.useState)(null),[el,ep]=(0,v.useState)({}),ef=(0,v.useRef)({}),[ej,eb]=(0,v.useState)({current:0,total:1,limit:10,hasMore:!1}),[ev,eg]=(0,v.useState)("");(0,v.useEffect)(()=>{if(V){let e=V.get("command");e&&(W(e),C(!0))}},[V]),(0,v.useEffect)(()=>{let e=new Date().getHours();e<12?k("Bom dia"):e<18?k("Boa tarde"):k("Boa noite")},[]),(0,v.useEffect)(()=>{"unauthenticated"===N&&s.push("/auth/signin?callbackUrl="+encodeURIComponent("/dashboard"))},[N,s]),(0,v.useEffect)(()=>()=>{Object.values(ef.current).forEach(e=>{e.abort()})},[]);let eN=()=>{C(!0)},ew=async()=>{try{if(O(!0),!E&&!B){g.toast.error("Por favor, forne\xe7a um nome ou um comando para a planilha"),O(!1);return}let e=await fetch("/api/workbooks",{method:"POST",headers:{"Content-Type":"application/json",...I?{"x-csrf-token":I}:{}},body:JSON.stringify({name:E||"Nova Planilha",description:R,aiCommand:B||null,initialData:eh})});if(!e.ok)throw Error("Erro ao criar planilha");let a=await e.json();g.toast.success("Planilha criada com sucesso"),B?s.push("/workbook/".concat(a.workbook.id,"?command=").concat(encodeURIComponent(B))):s.push("/workbook/".concat(a.workbook.id))}catch(e){g.toast.error("N\xe3o foi poss\xedvel criar a planilha"),O(!1)}},ek=e=>{W(e),C(!0)},ey=(0,v.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{if(!(null==i?void 0:i.user))return;ef.current.recentWorkbooks&&ef.current.recentWorkbooks.abort();let a=new AbortController;if(ef.current.recentWorkbooks=a,!I)throw Error("Token CSRF n\xe3o dispon\xedvel");let s=await fetch("/api/workbooks/recent?page=".concat(e,"&limit=").concat(ej.limit),{headers:{"Content-Type":"application/json",...I?{"x-csrf-token":I}:{}},signal:a.signal});if(delete ef.current.recentWorkbooks,!s.ok){let e=await s.json();ep(a=>({...a,recentWorkbooks:e.details||e.error||"Erro ao carregar planilhas recentes"}));return}let t=await s.json();t.pagination&&eb({current:t.pagination.page,total:t.pagination.totalPages,limit:t.pagination.limit,hasMore:t.pagination.hasMore});let r=t.workbooks.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),lastAccessedAt:new Date(e.lastAccessedAt||e.updatedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));M(r),ep(e=>({...e,recentWorkbooks:""}))}catch(a){if(a instanceof DOMException&&"AbortError"===a.name)return;console.error("Erro ao carregar planilhas recentes:",a);let e=a instanceof Error?"Falha ao carregar planilhas: ".concat(a.message):"Erro ao carregar planilhas recentes";g.toast.error(e),ep(a=>({...a,recentWorkbooks:e}))}},[i,I,ej.limit]);(0,v.useEffect)(()=>{(null==i?void 0:i.user)&&ey(0)},[i,ey]);let eC=()=>{ej.hasMore&&ey(ej.current+1)},eE=()=>{ej.current>0&&ey(ej.current-1)},eA=(0,v.useCallback)(async()=>{try{if(!(null==i?void 0:i.user))return;Q(!0),ef.current.sharedWorkbooks&&ef.current.sharedWorkbooks.abort();let e=new AbortController;ef.current.sharedWorkbooks=e;let a=await fetch("/api/workbooks/shared",{headers:{"Content-Type":"application/json",...I?{"x-csrf-token":I}:{}},signal:e.signal});if(delete ef.current.sharedWorkbooks,!a.ok){let e=await a.json();ep(a=>({...a,sharedWorkbooks:e.details||e.error||"Erro ao carregar planilhas compartilhadas"}));return}let s=(await a.json()).workbooks.map(e=>({...e,createdAt:new Date(e.createdAt),updatedAt:new Date(e.updatedAt),sharedAt:new Date(e.sharedAt),sheets:Array.isArray(e.sheets)?e.sheets:e.sheetsCount||0}));Y(s),ep(e=>({...e,sharedWorkbooks:""}))}catch(a){if(a instanceof DOMException&&"AbortError"===a.name)return;console.error("Erro ao carregar planilhas compartilhadas:",a);let e=a instanceof Error?"Falha ao carregar planilhas compartilhadas: ".concat(a.message):"Erro ao carregar planilhas compartilhadas";g.toast.error(e),ep(a=>({...a,sharedWorkbooks:e}))}finally{Q(!1)}},[i,I]);(0,v.useEffect)(()=>{(null==i?void 0:i.user)&&eA()},[i,eA]);let eZ=e=>(0,r.Q)(e,{addSuffix:!0,locale:l.F}),eS=e=>{s.push("/workbook/".concat(e))};return"loading"===N?(0,t.jsx)("div",{className:"h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)(d.Z,{className:"h-10 w-10 animate-spin text-primary"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):"unauthenticated"===N?null:y?(0,t.jsxs)("main",{className:"container mx-auto py-6 px-4 max-w-3xl",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Criar Nova Planilha"}),(0,t.jsx)(Z.Button,{variant:"ghost",onClick:()=>C(!1),children:"Voltar"})]}),(0,t.jsxs)(S.Zb,{className:"mb-6",children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsx)(S.ll,{className:"text-xl",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,t.jsx)(S.SZ,{children:"Defina os detalhes da sua planilha"})]}),(0,t.jsxs)(S.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ei._,{htmlFor:"name",children:"Nome da Planilha"}),(0,t.jsx)(em.Z,{id:"name",placeholder:"Ex: Controle Financeiro 2023",value:E,onChange:e=>A(e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ei._,{htmlFor:"description",children:"Descri\xe7\xe3o (opcional)"}),(0,t.jsx)(eu.Z,{id:"description",placeholder:"Descreva o prop\xf3sito desta planilha...",rows:2,value:R,onChange:e=>z(e.target.value)})]})]})]}),(0,t.jsxs)(S.Zb,{className:"mb-6",children:[(0,t.jsxs)(S.Ol,{children:[(0,t.jsxs)(S.ll,{className:"text-xl flex items-center gap-2",children:[(0,t.jsx)(m.Z,{className:"h-5 w-5 text-primary"}),"Assistente IA"]}),(0,t.jsx)(S.SZ,{children:"Descreva o que voc\xea deseja criar e deixe a IA fazer o trabalho"})]}),(0,t.jsx)(S.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(ei._,{htmlFor:"prompt",children:"Comando para IA (opcional)"}),(0,t.jsx)(eu.Z,{id:"prompt",placeholder:"Ex: Crie uma planilha de controle financeiro com categorias de gastos e gr\xe1ficos...",rows:3,value:B,onChange:e=>W(e.target.value)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Descreva em linguagem natural o que voc\xea quer criar. Quanto mais detalhes, melhor ser\xe1 o resultado."})]})}),(0,t.jsxs)(S.eW,{className:"flex justify-between items-center",children:[(0,t.jsx)(Z.Button,{variant:"outline",onClick:()=>W(""),disabled:!B,children:"Limpar"}),(0,t.jsx)(Z.Button,{onClick:()=>ew(),disabled:F,className:"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",children:F?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),"Criando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Criar Nova Planilha"]})})]})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Dica: voc\xea tamb\xe9m pode usar templates pr\xe9-definidos"}),(0,t.jsx)(ed.WorkbookTemplatesServer,{})]})]}):(0,t.jsxs)("main",{className:"container mx-auto py-6 px-4 max-w-7xl",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-6 mb-8 shadow-sm",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gradient bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400",children:[w,", ",(null==i?void 0:null===(a=i.user)||void 0===a?void 0:null===(e=a.name)||void 0===e?void 0:e.split(" ")[0])||"Bem-vindo","!"]}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2 max-w-xl",children:"Pronto para turbinar suas planilhas? Use o Excel Copilot com IA para criar, editar e analisar dados de forma inteligente."})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(Z.Button,{size:"lg",onClick:()=>eN(),className:"mt-4 md:mt-0 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-all duration-300 hover:shadow-lg",children:[(0,t.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),"Criar Nova Planilha"]})})]})}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-medium mb-4",children:"Criar Rapidamente"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:ex.map((e,a)=>(0,t.jsxs)(S.Zb,{className:"cursor-pointer hover:shadow-md transition-all border-2 hover:border-primary/20",onClick:()=>ek(e.command),children:[(0,t.jsx)(S.Ol,{className:"pb-2",children:(0,t.jsxs)(S.ll,{className:"text-lg flex items-center gap-2",children:[e.icon,e.title]})}),(0,t.jsx)(S.aY,{children:(0,t.jsx)(S.SZ,{children:e.description})})]},a))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)(U,{activities:(null==q?void 0:q.recentActivity)||[],isLoading:!1,maxItems:8})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(T,{onCreateWorkbook:()=>C(!0),recentWorkbooks:_}),(0,t.jsx)(D,{}),(0,t.jsx)(P,{recentWorkbooks:_})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative max-w-md",children:[(0,t.jsx)(u.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,t.jsx)(em.Z,{placeholder:"Buscar planilhas...",value:ev,onChange:e=>eg(e.target.value),className:"pl-10 pr-10"}),ev&&(0,t.jsx)(Z.Button,{variant:"ghost",size:"sm",onClick:()=>eg(""),className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",children:(0,t.jsx)(h.Z,{className:"h-3 w-3"})})]})}),(0,t.jsxs)(en.mQ,{defaultValue:"all",className:"w-full",children:[(0,t.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,t.jsxs)(en.dr,{children:[(0,t.jsx)(en.SP,{value:"all",children:"Todas as Planilhas"}),(0,t.jsx)(en.SP,{value:"recent",children:"Recentes"}),(0,t.jsx)(en.SP,{value:"shared",children:"Compartilhadas"})]})}),(0,t.jsx)(en.nU,{value:"all",className:"mt-0",children:(0,t.jsx)(eo,{searchQuery:ev})}),(0,t.jsxs)(en.nU,{value:"recent",className:"mt-0",children:[el.recentWorkbooks?(0,t.jsx)("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Erro ao carregar planilhas recentes"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:(0,t.jsx)("p",{children:el.recentWorkbooks})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(Z.Button,{size:"sm",variant:"outline",onClick:()=>ey(ej.current),className:"text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40",children:"Tentar novamente"})})]})]})}):null,_.length>0?(0,t.jsxs)("div",{className:"border rounded-md",children:[(0,t.jsxs)(K,{children:[(0,t.jsx)(G,{children:(0,t.jsxs)(ea,{children:[(0,t.jsx)(es,{children:"Nome"}),(0,t.jsx)(es,{children:"\xdaltimo acesso"}),(0,t.jsx)(es,{className:"text-right",children:"A\xe7\xf5es"})]})}),(0,t.jsx)(ee,{children:_.map(e=>(0,t.jsxs)(ea,{className:"cursor-pointer hover:bg-muted/40",onClick:()=>eS(e.id),children:[(0,t.jsxs)(et,{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(o.Z,{className:"h-4 w-4 text-blue-500"}),e.name]}),(0,t.jsx)(et,{className:"text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(x.Z,{className:"h-3 w-3"}),eZ(e.lastAccessedAt||e.updatedAt)]})}),(0,t.jsx)(et,{className:"text-right",children:(0,t.jsx)(ec,{workbookId:e.id,onComplete:ey,buttonVariant:"ghost",buttonSize:"icon"})})]},e.id))})]}),(0,t.jsx)(()=>(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["P\xe1gina ",ej.current+1," de ",Math.max(1,ej.total)]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(Z.Button,{variant:"outline",size:"sm",onClick:eE,disabled:ej.current<=0,children:[(0,t.jsx)(n.Z,{className:"h-4 w-4 mr-1"})," Anterior"]}),(0,t.jsxs)(Z.Button,{variant:"outline",size:"sm",onClick:eC,disabled:!ej.hasMore,children:["Pr\xf3xima ",(0,t.jsx)(c.Z,{className:"h-4 w-4 ml-1"})]})]})]}),{})]}):(0,t.jsx)($,{icon:(0,t.jsx)(o.Z,{className:"h-10 w-10"}),title:"Nenhuma planilha recente",description:"As planilhas que voc\xea acessou recentemente aparecer\xe3o aqui."})]}),(0,t.jsxs)(en.nU,{value:"shared",className:"mt-0",children:[el.sharedWorkbooks?(0,t.jsx)("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Erro ao carregar planilhas compartilhadas"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:(0,t.jsx)("p",{children:el.sharedWorkbooks})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(Z.Button,{size:"sm",variant:"outline",onClick:eA,className:"text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40",children:"Tentar novamente"})})]})]})}):null,X?(0,t.jsx)("div",{className:"w-full py-10 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"})}):L.length>0?(0,t.jsx)("div",{className:"border rounded-md",children:(0,t.jsxs)(K,{children:[(0,t.jsx)(G,{children:(0,t.jsxs)(ea,{children:[(0,t.jsx)(es,{children:"Nome"}),(0,t.jsx)(es,{children:"Compartilhado por"}),(0,t.jsx)(es,{children:"Data de compartilhamento"}),(0,t.jsx)(es,{className:"text-right",children:"A\xe7\xf5es"})]})}),(0,t.jsx)(ee,{children:L.map(e=>{var a;return(0,t.jsxs)(ea,{className:"cursor-pointer hover:bg-muted/40",onClick:()=>eS(e.id),children:[(0,t.jsxs)(et,{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(o.Z,{className:"h-4 w-4 text-blue-500"}),e.name]}),(0,t.jsx)(et,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.sharedBy.image?(0,t.jsx)(f.default,{src:e.sharedBy.image,alt:e.sharedBy.name||"Usu\xe1rio",width:24,height:24,className:"h-6 w-6 rounded-full"}):(0,t.jsx)("div",{className:"h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs",children:(null===(a=e.sharedBy.name)||void 0===a?void 0:a.charAt(0))||"?"}),(0,t.jsx)("span",{children:e.sharedBy.name||e.sharedBy.email})]})}),(0,t.jsx)(et,{className:"text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.Z,{className:"h-3 w-3"}),eZ(e.sharedAt)]})}),(0,t.jsx)(et,{className:"text-right",children:(0,t.jsx)(ec,{workbookId:e.id,onComplete:eA,onlyEdit:!0,buttonVariant:"ghost",buttonSize:"sm",allowDelete:!1,allowDuplicate:!1})})]},e.id)})})]})}):(0,t.jsx)($,{icon:(0,t.jsx)(o.Z,{className:"h-10 w-10"}),title:"Nenhuma planilha compartilhada",description:"As planilhas compartilhadas com voc\xea aparecer\xe3o aqui."})]})]})]})]})}},31590:function(e,a,s){"use strict";s.d(a,{$F:function(){return m},AW:function(){return h},Ju:function(){return p},Qk:function(){return u},VD:function(){return f},Xi:function(){return x},h_:function(){return d}});var t=s(57437),r=s(81622),l=s(87592),o=s(22468),i=s(28165),n=s(2265),c=s(49354);let d=r.fC,m=r.xz,u=r.ZA;r.Uv,r.Tr,r.Ee,n.forwardRef((e,a)=>{let{className:s,inset:o,children:i,...n}=e;return(0,t.jsxs)(r.fF,{ref:a,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",o&&"pl-8",s),...n,children:[i,(0,t.jsx)(l.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=r.fF.displayName,n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.tu,{ref:a,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})}).displayName=r.tu.displayName;let h=n.forwardRef((e,a)=>{let{className:s,sideOffset:l=4,...o}=e;return(0,t.jsx)(r.Uv,{children:(0,t.jsx)(r.VY,{ref:a,sideOffset:l,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...o})})});h.displayName=r.VY.displayName;let x=n.forwardRef((e,a)=>{let{className:s,inset:l,...o}=e;return(0,t.jsx)(r.ck,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",s),...o})});x.displayName=r.ck.displayName,n.forwardRef((e,a)=>{let{className:s,children:l,checked:i,...n}=e;return(0,t.jsxs)(r.oC,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:null!=i&&i,...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(r.wU,{children:(0,t.jsx)(o.Z,{className:"h-4 w-4"})})}),l]})}).displayName=r.oC.displayName,n.forwardRef((e,a)=>{let{className:s,children:l,...o}=e;return(0,t.jsxs)(r.Rk,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...o,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(r.wU,{children:(0,t.jsx)(i.Z,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=r.Rk.displayName;let p=n.forwardRef((e,a)=>{let{className:s,inset:l,...o}=e;return(0,t.jsx)(r.__,{ref:a,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",s),...o})});p.displayName=r.__.displayName;let f=n.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...l})});f.displayName=r.Z0.displayName},6432:function(e,a,s){"use strict";s.d(a,{RM:function(){return i},aF:function(){return n}});var t=s(2265),r=s(49354);let l={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},o={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"md",s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],t=arguments.length>3?arguments[3]:void 0;return(0,r.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",l[e],o[a],s&&"min-h-[80px] resize-vertical",t)}function n(e,a){return a?t.createElement("div",{className:a},e):e}},77209:function(e,a,s){"use strict";s.d(a,{I:function(){return o}});var t=s(57437),r=s(2265),l=s(6432);let o=r.forwardRef((e,a)=>{let{className:s,type:r,wrapperClassName:o,variant:i="default",fieldSize:n="md",inputSize:c,...d}=e,m=(0,t.jsx)("input",{type:r,className:(0,l.RM)(i,c||n,!1,s),ref:a,...d});return(0,l.aF)(m,o)});o.displayName="Input",a.Z=o},70402:function(e,a,s){"use strict";s.d(a,{_:function(){return c}});var t=s(57437),r=s(38364),l=s(13027),o=s(2265),i=s(49354);let n=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.f,{ref:a,className:(0,i.cn)(n(),s),...l})});c.displayName=r.f.displayName},86864:function(e,a,s){"use strict";s.d(a,{SP:function(){return c},dr:function(){return n},mQ:function(){return i},nU:function(){return d}});var t=s(57437),r=s(62447),l=s(2265),o=s(49354);let i=r.fC,n=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.aV,{ref:a,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...l})});n.displayName=r.aV.displayName;let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.xz,{ref:a,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...l})});c.displayName=r.xz.displayName;let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(r.VY,{ref:a,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...l})});d.displayName=r.VY.displayName},4919:function(e,a,s){"use strict";var t=s(57437),r=s(2265),l=s(6432);let o=r.forwardRef((e,a)=>{let{className:s,wrapperClassName:r,variant:o="default",fieldSize:i="md",textareaSize:n,...c}=e,d=(0,t.jsx)("textarea",{className:(0,l.RM)(o,n||i,!0,s),ref:a,...c});return(0,l.aF)(d,r)});o.displayName="Textarea",a.Z=o}},function(e){e.O(0,[7142,8638,7776,5660,3526,231,3123,193,1567,8194,5880,2518,2971,7023,1744],function(){return e(e.s=5352)}),_N_E=e.O()}]);