(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2483],{73232:function(e,n,a){Promise.resolve().then(a.bind(a,38894))},38894:function(e,n,a){"use strict";a.r(n),a.d(n,{default:function(){return d}});var t=a(57437),s=a(74109),l=a(16463),c=a(30998),i=a(2265),o=a(27776),r=a(15803);let u={headers:["A","B","C","D","E"],rows:[["","","","",""],["","","","",""],["","","","",""],["","","","",""],["","","","",""]],charts:[],name:"Nova Planilha"};function d(){let{status:e}=(0,c.useSession)(),n=(0,l.useSearchParams)(),a=(0,l.useRouter)(),[d,m]=(0,i.useState)(null),[f,h]=(0,i.useState)(!0);return((0,i.useEffect)(()=>{if("unauthenticated"===e){let e=(null==n?void 0:n.get("command"))?"&command=".concat(encodeURIComponent((null==n?void 0:n.get("command"))||"")):"";a.push("/auth/signin?callbackUrl=".concat(encodeURIComponent("/workbook/new")).concat(e));return}if("authenticated"===e){let e=null==n?void 0:n.get("command");e&&(m(e),o.toast.info("Comando recebido",{description:"Vamos processar: "+e,duration:3e3})),h(!1)}},[n,e,a]),"loading"===e||f)?(0,t.jsx)("div",{className:"h-full w-full flex items-center justify-center min-h-screen",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)(s.Z,{className:"h-10 w-10 animate-spin text-primary"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Verificando autentica\xe7\xe3o..."})]})}):"unauthenticated"===e?null:(0,t.jsx)("div",{className:"w-full h-[calc(100vh-64px)] flex flex-col",children:(0,t.jsx)(i.Suspense,{fallback:(0,t.jsxs)("div",{className:"h-full w-full flex items-center justify-center",children:[(0,t.jsx)(s.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,t.jsx)("span",{className:"ml-2",children:"Preparando nova planilha..."})]}),children:(0,t.jsx)(r.SpreadsheetEditor,{workbookId:"new",initialData:u,initialCommand:d})})})}}},function(e){e.O(0,[3113,7142,8638,7776,5660,3526,2848,3123,193,9109,7090,6180,3932,7966,8194,5880,8418,2708,2971,7023,1744],function(){return e(e.s=73232)}),_N_E=e.O()}]);