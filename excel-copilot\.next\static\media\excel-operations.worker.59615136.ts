/**
 * Web Worker para processamento de operações Excel em thread separada
 *
 * Este worker executa operações pesadas de Excel (fórmulas, gráficos, filtros, etc.)
 * em uma thread separada para não bloquear a UI principal.
 *
 * Compatível com Vercel serverless e Next.js
 */ import { ExcelOperationType } from "../types";
import { executeOperation } from "../lib/operations";
import { logger } from "../lib/logger";
// Logger seguro para workers - só loga em desenvolvimento
const isDev = "production" !== "production";
const workerLogger = {
    log: (message, metadata)=>{
        if (isDev) logger.debug(message, metadata);
    },
    error: (message, error)=>{
        // Erros sempre são logados, mesmo em produção
        logger.error(message, error, {
            component: "ExcelWorker"
        });
    }
};
// Configuração do worker
const WORKER_CONFIG = {
    timeout: 30000,
    maxRetries: 3,
    enableLogging: true
};
/**
 * Processa uma operação Excel
 */ async function processExcelOperation(operation, sheetData) {
    try {
        // Log da operação (apenas em desenvolvimento)
        if (WORKER_CONFIG.enableLogging) {
            workerLogger.log("Processando opera\xe7\xe3o: ".concat(operation.type), {
                operationType: operation.type,
                hasData: !!sheetData,
                operationId: operation.id
            });
        }
        // Executar a operação usando o sistema existente
        const result = await executeOperation(sheetData, operation);
        // Normalizar resultado para o formato esperado
        const normalizedResult = {
            updatedData: result.updatedData,
            resultSummary: result.resultSummary,
            modifiedCells: []
        };
        if (WORKER_CONFIG.enableLogging) {
            workerLogger.log("Opera\xe7\xe3o conclu\xedda: ".concat(operation.type), {
                success: true,
                resultSummary: result.resultSummary
            });
        }
        return normalizedResult;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
        if (WORKER_CONFIG.enableLogging) {
            console.error("[Excel Worker] Erro na opera\xe7\xe3o: ".concat(operation.type), {
                error: errorMessage,
                operationType: operation.type,
                operationId: operation.id
            });
        }
        throw new Error("Erro ao processar opera\xe7\xe3o ".concat(operation.type, ": ").concat(errorMessage));
    }
}
/**
 * Valida se a operação é suportada
 */ function validateOperation(operation) {
    if (!operation || typeof operation !== "object") {
        throw new Error("Opera\xe7\xe3o inv\xe1lida: deve ser um objeto");
    }
    if (!operation.type) {
        throw new Error("Opera\xe7\xe3o inv\xe1lida: tipo n\xe3o especificado");
    }
    // Verificar se o tipo é suportado
    const supportedTypes = [
        ExcelOperationType.FORMULA,
        ExcelOperationType.CHART,
        ExcelOperationType.FILTER,
        ExcelOperationType.SORT,
        ExcelOperationType.CELL_UPDATE,
        ExcelOperationType.TABLE
    ];
    if (!supportedTypes.includes(operation.type)) {
        throw new Error("Tipo de opera\xe7\xe3o n\xe3o suportado: ".concat(operation.type));
    }
    if (!operation.data) {
        throw new Error("Opera\xe7\xe3o inv\xe1lida: dados n\xe3o especificados");
    }
}
/**
 * Handler principal de mensagens do worker
 */ self.onmessage = async (event)=>{
    const { operation, sheetData, requestId, operationType } = event.data;
    let response;
    try {
        // Validar entrada
        if (!requestId) {
            throw new Error("ID da requisi\xe7\xe3o n\xe3o fornecido");
        }
        if (!operation) {
            throw new Error("Opera\xe7\xe3o n\xe3o fornecida");
        }
        // Validar operação
        validateOperation(operation);
        // Processar operação
        const result = await processExcelOperation(operation, sheetData);
        // Resposta de sucesso
        response = {
            requestId,
            success: true,
            result
        };
    } catch (error) {
        // Resposta de erro
        const errorMessage = error instanceof Error ? error.message : "Erro desconhecido no worker";
        response = {
            requestId: requestId || "unknown",
            success: false,
            error: errorMessage
        };
        // Log do erro
        if (WORKER_CONFIG.enableLogging) {
            workerLogger.error("Erro fatal no processamento", {
                error: errorMessage,
                requestId,
                operationType
            });
        }
    }
    // Enviar resposta de volta para a thread principal
    self.postMessage(response);
};
/**
 * Handler de erros não capturados
 */ self.onerror = (error)=>{
    workerLogger.error("Erro n\xe3o capturado no worker", error);
    // Extrair mensagem de erro de forma segura
    const errorMessage = typeof error === "string" ? error : error instanceof ErrorEvent ? error.message : "Erro desconhecido";
    // Tentar enviar resposta de erro se possível
    self.postMessage({
        requestId: "error",
        success: false,
        error: "Erro fatal no worker: ".concat(errorMessage)
    });
};
/**
 * Handler de erros de promise rejeitadas
 */ self.onunhandledrejection = (event)=>{
    workerLogger.error("Promise rejeitada n\xe3o tratada", event.reason);
    // Tentar enviar resposta de erro se possível
    self.postMessage({
        requestId: "unhandled-rejection",
        success: false,
        error: "Promise rejeitada: ".concat(event.reason || "Erro desconhecido")
    });
};
// Log de inicialização
if (WORKER_CONFIG.enableLogging) {
    workerLogger.log("Worker inicializado com sucesso", {
        timeout: WORKER_CONFIG.timeout,
        maxRetries: WORKER_CONFIG.maxRetries,
        supportedOperations: [
            "FORMULA",
            "CHART",
            "FILTER",
            "SORT",
            "CELL_UPDATE",
            "TABLE"
        ]
    });
}
