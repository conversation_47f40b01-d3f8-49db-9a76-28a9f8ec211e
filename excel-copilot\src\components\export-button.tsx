/**
 * Componente de botão de exportação para dados Excel
 */
'use client';

import { Download, Settings, FileSpreadsheet, Package } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useExcelFile } from '@/hooks/useExcelFile';
import { BatchExportDialog } from './export/BatchExportDialog';
import { ExportOptionsDialog } from './export/ExportOptionsDialog';

interface ExportButtonProps {
  workbookId: string;
  workbookName: string;
  sheets: {
    name: string;
    data: any;
  }[];
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  enableAdvancedExport?: boolean; // Habilitar funcionalidades avançadas
  allowBatchExport?: boolean; // Permitir export em lote
}

/**
 * Botão para exportar planilha para arquivo Excel com funcionalidades avançadas
 */
export function ExportButton({
  workbookId,
  workbookName,
  sheets,
  variant = 'outline',
  size = 'sm',
  enableAdvancedExport = false,
  allowBatchExport = false,
}: ExportButtonProps) {
  const { exportExcel, isLoading } = useExcelFile();

  // Estados para funcionalidades avançadas
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [showBatchExport, setShowBatchExport] = useState(false);

  const handleExportExcel = async () => {
    await exportExcel(sheets, workbookName, 'xlsx', {
      trackAnalytics: true,
      workbookId,
    });
  };

  const handleExportCSV = async () => {
    await exportExcel(sheets, workbookName, 'csv', {
      trackAnalytics: true,
      workbookId,
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={variant}
            size={size}
            disabled={!sheets || sheets.length === 0 || isLoading}
            className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Exportando...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Exportar
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => handleExportExcel()}>
            <Download className="h-4 w-4 mr-2" />
            <span>Exportar como Excel (.xlsx)</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleExportCSV()}>
            <Download className="h-4 w-4 mr-2" />
            <span>Exportar como CSV (.csv)</span>
          </DropdownMenuItem>

          {enableAdvancedExport && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowExportOptions(true)}>
                <Settings className="h-4 w-4 mr-2" />
                <span>Opções Avançadas</span>
              </DropdownMenuItem>
            </>
          )}

          {allowBatchExport && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowBatchExport(true)}>
                <Package className="h-4 w-4 mr-2" />
                <span>Export em Lote</span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Dialog de Opções Avançadas */}
      {enableAdvancedExport && (
        <ExportOptionsDialog
          open={showExportOptions}
          onOpenChange={setShowExportOptions}
          workbookId={workbookId}
          workbookName={workbookName}
          sheets={sheets}
          onExport={(options) => {
            setShowExportOptions(false);
            // TODO: Implementar export com opções avançadas
          }}
        />
      )}

      {/* Dialog de Export em Lote */}
      {allowBatchExport && (
        <BatchExportDialog
          open={showBatchExport}
          onOpenChange={setShowBatchExport}
          workbookId={workbookId}
          onExport={(batchOptions) => {
            setShowBatchExport(false);
            // TODO: Implementar batch export
          }}
        />
      )}
    </>
  );
}
