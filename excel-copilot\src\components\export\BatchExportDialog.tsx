'use client';

import { useState } from 'react';
import { Package, Download, Calendar, Clock, FileSpreadsheet, Settings } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface BatchExportOptions {
  formats: string[];
  schedule?: {
    type: 'immediate' | 'scheduled';
    datetime?: Date | undefined;
    recurring?: 'daily' | 'weekly' | 'monthly' | undefined;
  };
  compression: boolean;
  splitBySheets: boolean;
  includeMetadata: boolean;
  customNaming: string;
}

interface BatchExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  workbookId: string;
  onExport: (options: BatchExportOptions) => void;
}

const EXPORT_FORMATS = [
  { id: 'xlsx', name: 'Excel (.xlsx)', icon: <FileSpreadsheet className="h-4 w-4" /> },
  { id: 'csv', name: 'CSV (.csv)', icon: <FileSpreadsheet className="h-4 w-4" /> },
  { id: 'json', name: 'JSON (.json)', icon: <FileSpreadsheet className="h-4 w-4" /> },
  { id: 'pdf', name: 'PDF (.pdf)', icon: <FileSpreadsheet className="h-4 w-4" /> },
];

export function BatchExportDialog({ open, onOpenChange, workbookId, onExport }: BatchExportDialogProps) {
  const [selectedFormats, setSelectedFormats] = useState<string[]>(['xlsx']);
  const [scheduleType, setScheduleType] = useState<'immediate' | 'scheduled'>('immediate');
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [recurring, setRecurring] = useState<'daily' | 'weekly' | 'monthly' | undefined>();
  const [compression, setCompression] = useState(false);
  const [splitBySheets, setSplitBySheets] = useState(false);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [customNaming, setCustomNaming] = useState('{{workbook}}_{{date}}_{{format}}');

  const handleFormatToggle = (formatId: string) => {
    setSelectedFormats(prev => 
      prev.includes(formatId) 
        ? prev.filter(id => id !== formatId)
        : [...prev, formatId]
    );
  };

  const handleExport = () => {
    const options: BatchExportOptions = {
      formats: selectedFormats,
      schedule: scheduleType === 'scheduled' ? {
        type: 'scheduled',
        datetime: scheduledDate && scheduledTime ? new Date(`${scheduledDate}T${scheduledTime}`) : undefined,
        recurring,
      } : { type: 'immediate' },
      compression,
      splitBySheets,
      includeMetadata,
      customNaming,
    };

    onExport(options);
  };

  const isValid = selectedFormats.length > 0 && 
    (scheduleType === 'immediate' || (scheduledDate && scheduledTime));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Export em Lote - Configurações Avançadas
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="formats" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="formats">Formatos</TabsTrigger>
            <TabsTrigger value="schedule">Agendamento</TabsTrigger>
            <TabsTrigger value="options">Opções</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          {/* Aba de Formatos */}
          <TabsContent value="formats" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Selecionar Formatos de Export</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {EXPORT_FORMATS.map((format) => (
                    <div
                      key={format.id}
                      className={`flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedFormats.includes(format.id) 
                          ? 'border-primary bg-primary/5' 
                          : 'border-border hover:bg-muted/50'
                      }`}
                      onClick={() => handleFormatToggle(format.id)}
                    >
                      <Checkbox
                        checked={selectedFormats.includes(format.id)}
                        onCheckedChange={() => handleFormatToggle(format.id)}
                      />
                      <div className="flex items-center gap-2">
                        {format.icon}
                        <span className="font-medium">{format.name}</span>
                      </div>
                    </div>
                  ))}
                </div>
                
                {selectedFormats.length === 0 && (
                  <Alert className="mt-4">
                    <AlertDescription>
                      Selecione pelo menos um formato para continuar.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba de Agendamento */}
          <TabsContent value="schedule" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Configurar Agendamento</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Label>Tipo de Execução</Label>
                  <Select value={scheduleType} onValueChange={(value: 'immediate' | 'scheduled') => setScheduleType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">
                        <div className="flex items-center gap-2">
                          <Download className="h-4 w-4" />
                          Executar Agora
                        </div>
                      </SelectItem>
                      <SelectItem value="scheduled">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Agendar Execução
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {scheduleType === 'scheduled' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="date">Data</Label>
                        <Input
                          id="date"
                          type="date"
                          value={scheduledDate}
                          onChange={(e) => setScheduledDate(e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="time">Horário</Label>
                        <Input
                          id="time"
                          type="time"
                          value={scheduledTime}
                          onChange={(e) => setScheduledTime(e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Recorrência (Opcional)</Label>
                      <Select value={recurring || ''} onValueChange={(value) => setRecurring(value as any)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecionar recorrência" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Sem recorrência</SelectItem>
                          <SelectItem value="daily">Diário</SelectItem>
                          <SelectItem value="weekly">Semanal</SelectItem>
                          <SelectItem value="monthly">Mensal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba de Opções */}
          <TabsContent value="options" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Opções de Export</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="compression"
                      checked={compression}
                      onCheckedChange={(checked) => setCompression(checked === true)}
                    />
                    <Label htmlFor="compression">Compactar arquivos em ZIP</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="splitBySheets"
                      checked={splitBySheets}
                      onCheckedChange={(checked) => setSplitBySheets(checked === true)}
                    />
                    <Label htmlFor="splitBySheets">Separar por planilhas</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeMetadata"
                      checked={includeMetadata}
                      onCheckedChange={(checked) => setIncludeMetadata(checked === true)}
                    />
                    <Label htmlFor="includeMetadata">Incluir metadados</Label>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="naming">Padrão de Nomenclatura</Label>
                  <Input
                    id="naming"
                    value={customNaming}
                    onChange={(e) => setCustomNaming(e.target.value)}
                    placeholder="{{workbook}}_{{date}}_{{format}}"
                  />
                  <p className="text-xs text-muted-foreground">
                    Variáveis disponíveis: {'{workbook}'}, {'{date}'}, {'{time}'}, {'{format}'}, {'{sheet}'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba de Preview */}
          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Preview da Configuração</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">Formatos Selecionados:</Label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {selectedFormats.map(formatId => {
                        const format = EXPORT_FORMATS.find(f => f.id === formatId);
                        return (
                          <Badge key={formatId} variant="secondary">
                            {format?.name}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Agendamento:</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {scheduleType === 'immediate' ? 'Executar imediatamente' : 
                       `Agendado para ${scheduledDate} às ${scheduledTime}${recurring ? ` (${recurring})` : ''}`}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Opções:</Label>
                    <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                      {compression && <li>• Compactação habilitada</li>}
                      {splitBySheets && <li>• Separar por planilhas</li>}
                      {includeMetadata && <li>• Incluir metadados</li>}
                      <li>• Padrão de nome: {customNaming}</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleExport} disabled={!isValid}>
            {scheduleType === 'immediate' ? 'Exportar Agora' : 'Agendar Export'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
