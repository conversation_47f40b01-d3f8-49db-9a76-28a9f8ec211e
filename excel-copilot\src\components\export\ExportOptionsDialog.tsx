'use client';

import { useState } from 'react';
import { Settings, FileSpreadsheet, Filter, Palette, Lock } from 'lucide-react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';

interface ExportOptions {
  format: 'xlsx' | 'csv' | 'pdf';
  includeHeaders: boolean;
  includeFormulas: boolean;
  includeFormatting: boolean;
  password?: string;
  compression: number;
  pageOrientation?: 'portrait' | 'landscape';
  pageSize?: 'A4' | 'A3' | 'Letter';
  filters?: {
    columns: string[];
    rows: { start: number; end: number };
  };
  customStyles?: {
    headerColor: string;
    alternateRows: boolean;
    fontSize: number;
  };
}

interface ExportOptionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  workbookId: string;
  workbookName: string;
  sheets: { name: string; data: any }[];
  onExport: (options: ExportOptions) => void;
}

export function ExportOptionsDialog({
  open,
  onOpenChange,
  workbookId,
  workbookName,
  sheets,
  onExport
}: ExportOptionsDialogProps) {
  const [format, setFormat] = useState<'xlsx' | 'csv' | 'pdf'>('xlsx');
  const [includeHeaders, setIncludeHeaders] = useState(true);
  const [includeFormulas, setIncludeFormulas] = useState(true);
  const [includeFormatting, setIncludeFormatting] = useState(true);
  const [password, setPassword] = useState('');
  const [compression, setCompression] = useState([50]);
  const [pageOrientation, setPageOrientation] = useState<'portrait' | 'landscape'>('portrait');
  const [pageSize, setPageSize] = useState<'A4' | 'A3' | 'Letter'>('A4');
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [rowStart, setRowStart] = useState('1');
  const [rowEnd, setRowEnd] = useState('');
  const [headerColor, setHeaderColor] = useState('#3b82f6');
  const [alternateRows, setAlternateRows] = useState(true);
  const [fontSize, setFontSize] = useState([12]);

  // Extrair colunas disponíveis do primeiro sheet
  const availableColumns = sheets.length > 0 && Array.isArray(sheets[0].data) && sheets[0].data.length > 0
    ? Object.keys(sheets[0].data[0])
    : [];

  const handleColumnToggle = (column: string) => {
    setSelectedColumns(prev =>
      prev.includes(column)
        ? prev.filter(c => c !== column)
        : [...prev, column]
    );
  };

  const handleExport = () => {
    const options: ExportOptions = {
      format,
      includeHeaders,
      includeFormulas,
      includeFormatting,
      password: password || undefined,
      compression: compression[0],
      pageOrientation: format === 'pdf' ? pageOrientation : undefined,
      pageSize: format === 'pdf' ? pageSize : undefined,
      filters: selectedColumns.length > 0 || rowStart || rowEnd ? {
        columns: selectedColumns.length > 0 ? selectedColumns : availableColumns,
        rows: {
          start: parseInt(rowStart) || 1,
          end: parseInt(rowEnd) || 999999
        }
      } : undefined,
      customStyles: {
        headerColor,
        alternateRows,
        fontSize: fontSize[0]
      }
    };

    onExport(options);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Opções Avançadas de Export - {workbookName}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">Geral</TabsTrigger>
            <TabsTrigger value="filters">Filtros</TabsTrigger>
            <TabsTrigger value="styling">Estilo</TabsTrigger>
            <TabsTrigger value="security">Segurança</TabsTrigger>
          </TabsList>

          {/* Aba Geral */}
          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileSpreadsheet className="h-5 w-5" />
                  Configurações Gerais
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Formato de Export</Label>
                  <Select value={format} onValueChange={(value: 'xlsx' | 'csv' | 'pdf') => setFormat(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="xlsx">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                      <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="headers"
                      checked={includeHeaders}
                      onCheckedChange={setIncludeHeaders}
                    />
                    <Label htmlFor="headers">Incluir cabeçalhos</Label>
                  </div>

                  {format === 'xlsx' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="formulas"
                          checked={includeFormulas}
                          onCheckedChange={setIncludeFormulas}
                        />
                        <Label htmlFor="formulas">Incluir fórmulas</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="formatting"
                          checked={includeFormatting}
                          onCheckedChange={setIncludeFormatting}
                        />
                        <Label htmlFor="formatting">Incluir formatação</Label>
                      </div>
                    </>
                  )}
                </div>

                {format === 'pdf' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Orientação</Label>
                      <Select value={pageOrientation} onValueChange={(value: 'portrait' | 'landscape') => setPageOrientation(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="portrait">Retrato</SelectItem>
                          <SelectItem value="landscape">Paisagem</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Tamanho da Página</Label>
                      <Select value={pageSize} onValueChange={(value: 'A4' | 'A3' | 'Letter') => setPageSize(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="A4">A4</SelectItem>
                          <SelectItem value="A3">A3</SelectItem>
                          <SelectItem value="Letter">Letter</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Nível de Compressão: {compression[0]}%</Label>
                  <Slider
                    value={compression}
                    onValueChange={setCompression}
                    max={100}
                    min={0}
                    step={10}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba Filtros */}
          <TabsContent value="filters" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filtros de Dados
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Colunas a Exportar</Label>
                  <div className="grid grid-cols-3 gap-2 max-h-40 overflow-y-auto">
                    {availableColumns.map((column) => (
                      <div key={column} className="flex items-center space-x-2">
                        <Checkbox
                          id={`col-${column}`}
                          checked={selectedColumns.includes(column)}
                          onCheckedChange={() => handleColumnToggle(column)}
                        />
                        <Label htmlFor={`col-${column}`} className="text-sm">
                          {column}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {selectedColumns.length === 0 && (
                    <p className="text-sm text-muted-foreground">
                      Nenhuma coluna selecionada = todas as colunas
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="rowStart">Linha Inicial</Label>
                    <Input
                      id="rowStart"
                      type="number"
                      value={rowStart}
                      onChange={(e) => setRowStart(e.target.value)}
                      placeholder="1"
                      min="1"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rowEnd">Linha Final</Label>
                    <Input
                      id="rowEnd"
                      type="number"
                      value={rowEnd}
                      onChange={(e) => setRowEnd(e.target.value)}
                      placeholder="Todas"
                      min="1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba Estilo */}
          <TabsContent value="styling" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Personalização de Estilo
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="headerColor">Cor do Cabeçalho</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="headerColor"
                      type="color"
                      value={headerColor}
                      onChange={(e) => setHeaderColor(e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={headerColor}
                      onChange={(e) => setHeaderColor(e.target.value)}
                      placeholder="#3b82f6"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="alternateRows"
                    checked={alternateRows}
                    onCheckedChange={setAlternateRows}
                  />
                  <Label htmlFor="alternateRows">Linhas alternadas</Label>
                </div>

                <div className="space-y-2">
                  <Label>Tamanho da Fonte: {fontSize[0]}px</Label>
                  <Slider
                    value={fontSize}
                    onValueChange={setFontSize}
                    max={24}
                    min={8}
                    step={1}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba Segurança */}
          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Configurações de Segurança
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {format === 'xlsx' && (
                  <div className="space-y-2">
                    <Label htmlFor="password">Senha de Proteção (Opcional)</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Digite uma senha para proteger o arquivo"
                    />
                    <p className="text-sm text-muted-foreground">
                      Deixe em branco para não proteger o arquivo
                    </p>
                  </div>
                )}

                {format !== 'xlsx' && (
                  <p className="text-sm text-muted-foreground">
                    Proteção por senha disponível apenas para arquivos Excel (.xlsx)
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleExport}>
            Exportar com Opções
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
