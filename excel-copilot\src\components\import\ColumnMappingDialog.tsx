'use client';

import { useState, useEffect } from 'react';
import { ArrowRight, FileSpreadsheet, AlertCircle, CheckCircle, X } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { parseExcelFile } from '@/lib/excel';

interface ColumnMappingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  file: File;
  template?: string | null;
  onConfirm: (mapping: Record<string, string>) => void;
  onCancel: () => void;
}

interface ColumnInfo {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  sampleValues: any[];
  required?: boolean;
}

const TEMPLATE_COLUMNS: Record<string, ColumnInfo[]> = {
  'financial-expenses': [
    { name: 'Data', type: 'date', sampleValues: [], required: true },
    { name: 'Descrição', type: 'string', sampleValues: [], required: true },
    { name: 'Categoria', type: 'string', sampleValues: [] },
    { name: 'Valor', type: 'number', sampleValues: [], required: true },
    { name: 'Tipo', type: 'string', sampleValues: [], required: true },
  ],
  'sales-data': [
    { name: 'Data', type: 'date', sampleValues: [], required: true },
    { name: 'Produto', type: 'string', sampleValues: [], required: true },
    { name: 'Cliente', type: 'string', sampleValues: [] },
    { name: 'Quantidade', type: 'number', sampleValues: [], required: true },
    { name: 'Valor Unitário', type: 'number', sampleValues: [], required: true },
    { name: 'Total', type: 'number', sampleValues: [] },
  ],
  'inventory-control': [
    { name: 'Código', type: 'string', sampleValues: [], required: true },
    { name: 'Produto', type: 'string', sampleValues: [], required: true },
    { name: 'Categoria', type: 'string', sampleValues: [] },
    { name: 'Quantidade', type: 'number', sampleValues: [], required: true },
    { name: 'Preço', type: 'number', sampleValues: [], required: true },
    { name: 'Fornecedor', type: 'string', sampleValues: [] },
  ],
};

export function ColumnMappingDialog({ 
  open, 
  onOpenChange, 
  file, 
  template, 
  onConfirm, 
  onCancel 
}: ColumnMappingDialogProps) {
  const [sourceColumns, setSourceColumns] = useState<string[]>([]);
  const [targetColumns, setTargetColumns] = useState<ColumnInfo[]>([]);
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);

  useEffect(() => {
    if (open && file) {
      analyzeFile();
    }
  }, [open, file]);

  useEffect(() => {
    if (template && TEMPLATE_COLUMNS[template]) {
      setTargetColumns(TEMPLATE_COLUMNS[template]);
    } else {
      // Se não há template, usar colunas genéricas baseadas no arquivo
      setTargetColumns(sourceColumns.map(col => ({
        name: col,
        type: 'string',
        sampleValues: [],
      })));
    }
  }, [template, sourceColumns]);

  const analyzeFile = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const sheets = await parseExcelFile(file);
      
      if (!sheets || sheets.length === 0) {
        throw new Error('Arquivo não contém dados válidos');
      }

      const firstSheet = sheets[0];
      const data = Array.isArray(firstSheet.data) ? firstSheet.data : [];
      
      if (data.length === 0) {
        throw new Error('Planilha está vazia');
      }

      // Extrair colunas do primeiro registro
      const firstRow = data[0];
      const columns = Object.keys(firstRow);
      
      setSourceColumns(columns);
      setPreviewData(data.slice(0, 5)); // Primeiras 5 linhas para preview

      // Auto-mapping baseado em nomes similares
      if (template && TEMPLATE_COLUMNS[template]) {
        const autoMapping: Record<string, string> = {};
        TEMPLATE_COLUMNS[template].forEach(targetCol => {
          const similarColumn = columns.find(sourceCol => 
            sourceCol.toLowerCase().includes(targetCol.name.toLowerCase()) ||
            targetCol.name.toLowerCase().includes(sourceCol.toLowerCase())
          );
          if (similarColumn) {
            autoMapping[similarColumn] = targetCol.name;
          }
        });
        setMapping(autoMapping);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao analisar arquivo');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMappingChange = (sourceColumn: string, targetColumn: string) => {
    setMapping(prev => ({
      ...prev,
      [sourceColumn]: targetColumn
    }));
  };

  const removeMappingForTarget = (targetColumn: string) => {
    setMapping(prev => {
      const newMapping = { ...prev };
      Object.keys(newMapping).forEach(key => {
        if (newMapping[key] === targetColumn) {
          delete newMapping[key];
        }
      });
      return newMapping;
    });
  };

  const getMappedSourceColumn = (targetColumn: string) => {
    return Object.keys(mapping).find(key => mapping[key] === targetColumn);
  };

  const getValidationStatus = () => {
    const requiredColumns = targetColumns.filter(col => col.required);
    const mappedRequired = requiredColumns.filter(col => getMappedSourceColumn(col.name));
    
    return {
      isValid: mappedRequired.length === requiredColumns.length,
      missingRequired: requiredColumns.filter(col => !getMappedSourceColumn(col.name)),
      totalMapped: Object.keys(mapping).length,
    };
  };

  const validation = getValidationStatus();

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p>Analisando arquivo...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Mapeamento de Colunas - {file.name}
          </DialogTitle>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!error && (
          <div className="space-y-6">
            {/* Status de Validação */}
            <Alert variant={validation.isValid ? "default" : "destructive"}>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                {validation.isValid ? (
                  `Mapeamento válido! ${validation.totalMapped} colunas mapeadas.`
                ) : (
                  `Colunas obrigatórias não mapeadas: ${validation.missingRequired.map(col => col.name).join(', ')}`
                )}
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Colunas do Arquivo */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Colunas do Arquivo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {sourceColumns.map((column) => (
                    <div key={column} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{column}</p>
                        <p className="text-sm text-muted-foreground">
                          {previewData[0]?.[column] ? String(previewData[0][column]).substring(0, 30) + '...' : 'Vazio'}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {mapping[column] && (
                          <Badge variant="secondary">{mapping[column]}</Badge>
                        )}
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Colunas do Template */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {template ? 'Colunas do Template' : 'Colunas de Destino'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {targetColumns.map((column) => {
                    const mappedSource = getMappedSourceColumn(column.name);
                    return (
                      <div key={column.name} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{column.name}</p>
                            {column.required && (
                              <Badge variant="destructive" className="text-xs">Obrigatório</Badge>
                            )}
                            <Badge variant="outline" className="text-xs">{column.type}</Badge>
                          </div>
                          {mappedSource && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeMappingForTarget(column.name)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                        <Select
                          value={mappedSource || ''}
                          onValueChange={(value) => handleMappingChange(value, column.name)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecionar coluna do arquivo" />
                          </SelectTrigger>
                          <SelectContent>
                            {sourceColumns.map((sourceCol) => (
                              <SelectItem key={sourceCol} value={sourceCol}>
                                {sourceCol}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button 
            onClick={() => onConfirm(mapping)}
            disabled={!validation.isValid}
          >
            Confirmar Mapeamento
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
