'use client';

import { useState } from 'react';
import { FileSpreadsheet, Calculator, BarChart3, PiggyBank, Users, Package, Search, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ImportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'financial' | 'sales' | 'inventory' | 'hr' | 'general';
  icon: React.ReactNode;
  columns: string[];
  validationRules: Record<string, any>;
  isPopular?: boolean;
  isNew?: boolean;
}

interface ImportTemplateSelectorProps {
  onTemplateSelect: (templateId: string) => void;
  onSkip: () => void;
}

const IMPORT_TEMPLATES: ImportTemplate[] = [
  {
    id: 'financial-expenses',
    name: 'Controle de Despesas',
    description: 'Template para importar dados de despesas e receitas',
    category: 'financial',
    icon: <PiggyBank className="h-6 w-6" />,
    columns: ['Data', 'Descrição', 'Categoria', 'Valor', 'Tipo'],
    validationRules: {
      'Data': { required: true, type: 'date' },
      'Valor': { required: true, type: 'number' },
      'Tipo': { required: true, enum: ['Receita', 'Despesa'] }
    },
    isPopular: true,
  },
  {
    id: 'sales-data',
    name: 'Dados de Vendas',
    description: 'Template para importar relatórios de vendas',
    category: 'sales',
    icon: <BarChart3 className="h-6 w-6" />,
    columns: ['Data', 'Produto', 'Cliente', 'Quantidade', 'Valor Unitário', 'Total'],
    validationRules: {
      'Data': { required: true, type: 'date' },
      'Quantidade': { required: true, type: 'number', min: 0 },
      'Valor Unitário': { required: true, type: 'number', min: 0 }
    },
    isPopular: true,
  },
  {
    id: 'inventory-control',
    name: 'Controle de Estoque',
    description: 'Template para importar dados de inventário',
    category: 'inventory',
    icon: <Package className="h-6 w-6" />,
    columns: ['Código', 'Produto', 'Categoria', 'Quantidade', 'Preço', 'Fornecedor'],
    validationRules: {
      'Código': { required: true, type: 'string' },
      'Quantidade': { required: true, type: 'number', min: 0 },
      'Preço': { required: true, type: 'number', min: 0 }
    },
  },
  {
    id: 'employee-data',
    name: 'Dados de Funcionários',
    description: 'Template para importar informações de RH',
    category: 'hr',
    icon: <Users className="h-6 w-6" />,
    columns: ['Nome', 'Email', 'Cargo', 'Departamento', 'Salário', 'Data Admissão'],
    validationRules: {
      'Nome': { required: true, type: 'string' },
      'Email': { required: true, type: 'email' },
      'Data Admissão': { required: true, type: 'date' }
    },
  },
  {
    id: 'budget-planning',
    name: 'Planejamento Orçamentário',
    description: 'Template para importar dados de orçamento',
    category: 'financial',
    icon: <Calculator className="h-6 w-6" />,
    columns: ['Categoria', 'Subcategoria', 'Orçado', 'Realizado', 'Variação'],
    validationRules: {
      'Categoria': { required: true, type: 'string' },
      'Orçado': { required: true, type: 'number' },
      'Realizado': { required: true, type: 'number' }
    },
    isNew: true,
  },
  {
    id: 'custom-generic',
    name: 'Template Genérico',
    description: 'Template flexível para qualquer tipo de dados',
    category: 'general',
    icon: <FileSpreadsheet className="h-6 w-6" />,
    columns: [],
    validationRules: {},
  },
];

const CATEGORY_LABELS = {
  financial: 'Financeiro',
  sales: 'Vendas',
  inventory: 'Estoque',
  hr: 'Recursos Humanos',
  general: 'Geral',
};

export function ImportTemplateSelector({ onTemplateSelect, onSkip }: ImportTemplateSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredTemplates = IMPORT_TEMPLATES.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const popularTemplates = IMPORT_TEMPLATES.filter(t => t.isPopular);

  return (
    <div className="space-y-6">
      {/* Busca */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Buscar templates..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all">Todos</TabsTrigger>
          <TabsTrigger value="financial">Financeiro</TabsTrigger>
          <TabsTrigger value="sales">Vendas</TabsTrigger>
          <TabsTrigger value="inventory">Estoque</TabsTrigger>
          <TabsTrigger value="hr">RH</TabsTrigger>
          <TabsTrigger value="general">Geral</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedCategory} className="space-y-4">
          {/* Templates Populares */}
          {selectedCategory === 'all' && popularTemplates.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500" />
                Templates Populares
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {popularTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onSelect={() => onTemplateSelect(template.id)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Todos os Templates */}
          <div>
            <h3 className="text-lg font-semibold mb-3">
              {selectedCategory === 'all' ? 'Todos os Templates' : CATEGORY_LABELS[selectedCategory as keyof typeof CATEGORY_LABELS]}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  onSelect={() => onTemplateSelect(template.id)}
                />
              ))}
            </div>
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <FileSpreadsheet className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum template encontrado</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Ações */}
      <div className="flex justify-between pt-4 border-t">
        <Button variant="outline" onClick={onSkip}>
          Pular Template
        </Button>
        <p className="text-sm text-muted-foreground self-center">
          Selecione um template ou pule para importação manual
        </p>
      </div>
    </div>
  );
}

function TemplateCard({ template, onSelect }: { template: ImportTemplate; onSelect: () => void }) {
  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onSelect}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              {template.icon}
            </div>
            <div>
              <CardTitle className="text-base flex items-center gap-2">
                {template.name}
                {template.isPopular && <Badge variant="secondary" className="text-xs">Popular</Badge>}
                {template.isNew && <Badge variant="default" className="text-xs">Novo</Badge>}
              </CardTitle>
              <CardDescription className="text-sm">
                {template.description}
              </CardDescription>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <p className="text-xs text-muted-foreground">Colunas esperadas:</p>
          <div className="flex flex-wrap gap-1">
            {template.columns.length > 0 ? (
              template.columns.slice(0, 4).map((column, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {column}
                </Badge>
              ))
            ) : (
              <Badge variant="outline" className="text-xs">Flexível</Badge>
            )}
            {template.columns.length > 4 && (
              <Badge variant="outline" className="text-xs">
                +{template.columns.length - 4} mais
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
