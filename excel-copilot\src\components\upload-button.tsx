'use client';

import { Upload, Settings, FileSpreadsheet } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { ChangeEvent, useRef, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useExcelFile } from '@/hooks/useExcelFile';
import { storageService } from '@/lib/supabase/storage';
import { ImportTemplateSelector } from './import/ImportTemplateSelector';
import { ColumnMappingDialog } from './import/ColumnMappingDialog';
import { ProgressTracker, useProgressTracker } from './import/ProgressTracker';

interface UploadButtonProps {
  onUpload: (data: any) => void;
  workbookId?: string; // Para salvar no Supabase Storage
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  maxSize?: number; // em bytes, padrão 10MB
  saveToSupabase?: boolean; // Se deve salvar no Supabase Storage
  enableAdvancedImport?: boolean; // Habilitar funcionalidades avançadas
  allowTemplates?: boolean; // Permitir uso de templates
}

/**
 * Botão para carregar arquivo Excel com funcionalidades avançadas
 */
export function UploadButton({
  onUpload,
  workbookId,
  variant = 'default',
  size = 'sm',
  maxSize,
  saveToSupabase = false,
  enableAdvancedImport = false,
  allowTemplates = false,
}: UploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { importExcel, isLoading } = useExcelFile();
  const { data: session } = useSession();

  // Estados para funcionalidades avançadas
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [showColumnMapping, setShowColumnMapping] = useState(false);
  const [showProgress, setShowProgress] = useState(false);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // Hook de progresso
  const progressTracker = useProgressTracker();

  const handleClick = () => {
    if (enableAdvancedImport && allowTemplates) {
      setShowTemplateSelector(true);
    } else {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (enableAdvancedImport) {
      // Para importação avançada, mostrar mapeamento de colunas
      setPendingFile(file);
      setShowColumnMapping(true);
    } else {
      // Importação simples
      await processFile(file);
    }

    // Resetar o input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const processFile = async (file: File, columnMapping?: Record<string, string>) => {
    if (enableAdvancedImport) {
      // Inicializar tracking de progresso
      progressTracker.initializeSteps([
        { id: 'file-analysis', name: 'Análise do Arquivo', description: 'Analisando estrutura e conteúdo' },
        { id: 'schema-validation', name: 'Validação de Schema', description: 'Validando dados conforme template' },
        { id: 'data-transformation', name: 'Transformação', description: 'Aplicando transformações nos dados' },
        { id: 'data-processing', name: 'Processamento', description: 'Processando e organizando dados' },
        { id: 'finalization', name: 'Finalização', description: 'Concluindo importação' },
      ]);
      setShowProgress(true);
    }

    try {
      // Salvar no Supabase Storage se solicitado
      if (saveToSupabase && workbookId && session?.user) {
        if (enableAdvancedImport) {
          progressTracker.startStep('data-processing', 'Salvando no Supabase Storage...');
        }

        toast.loading('Salvando arquivo no Supabase...', { id: 'supabase-upload' });

        const uploadResult = await storageService.uploadExcelFile(
          file,
          (session.user as any).id || session.user.email || 'unknown',
          workbookId,
          {
            fileName: file.name,
            upsert: true,
          }
        );

        toast.success('Arquivo salvo no Supabase!', {
          id: 'supabase-upload',
          description: `Tamanho: ${Math.round(uploadResult.size / 1024)}KB`,
        });

        if (enableAdvancedImport) {
          progressTracker.updateStepProgress('data-processing', 30, 'Arquivo salvo no storage');
        }
      }

      // Usar o hook para importar o arquivo
      await importExcel(file, {
        onSuccess: (data) => {
          // Aplicar mapeamento de colunas se fornecido
          if (columnMapping && data.sheets) {
            data.sheets = data.sheets.map(sheet => ({
              ...sheet,
              data: applyColumnMapping(sheet.data, columnMapping)
            }));
          }

          if (enableAdvancedImport) {
            progressTracker.completeStep('finalization', 'Importação concluída com sucesso');
            setTimeout(() => setShowProgress(false), 2000);
          }

          onUpload(data);
        },
        ...(maxSize ? { maxSize } : {}),
        trackAnalytics: true,
        template: selectedTemplate,
        validateSchema: enableAdvancedImport,
        transformData: enableAdvancedImport,
        onProgress: enableAdvancedImport ? (progress) => {
          if (progress <= 10) {
            progressTracker.startStep('file-analysis', 'Carregando arquivo...');
          } else if (progress <= 50) {
            progressTracker.updateStepProgress('file-analysis', (progress - 10) * 2.5, 'Analisando dados...');
          } else if (progress <= 70) {
            progressTracker.completeStep('file-analysis');
            progressTracker.startStep('schema-validation', 'Validando schema...');
            progressTracker.updateStepProgress('schema-validation', (progress - 50) * 5, 'Verificando estrutura...');
          } else if (progress <= 90) {
            progressTracker.completeStep('schema-validation');
            progressTracker.startStep('data-transformation', 'Transformando dados...');
            progressTracker.updateStepProgress('data-transformation', (progress - 70) * 5, 'Aplicando transformações...');
          } else {
            progressTracker.completeStep('data-transformation');
            progressTracker.startStep('finalization', 'Finalizando...');
          }
        } : undefined,
      });
    } catch (error) {
      console.error('Erro no upload:', error);

      if (enableAdvancedImport) {
        const currentStep = progressTracker.currentStep || 'file-analysis';
        progressTracker.errorStep(currentStep, error instanceof Error ? error.message : 'Erro desconhecido');
      }

      toast.error('Erro ao processar arquivo', {
        description: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  };

  const applyColumnMapping = (data: any, mapping: Record<string, string>) => {
    // Implementar lógica de mapeamento de colunas
    if (!data || !Array.isArray(data)) return data;

    return data.map((row: any) => {
      const mappedRow: any = {};
      Object.entries(mapping).forEach(([sourceCol, targetCol]) => {
        if (row[sourceCol] !== undefined) {
          mappedRow[targetCol] = row[sourceCol];
        }
      });
      return { ...row, ...mappedRow };
    });
  };

  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".xlsx,.xls,.csv"
        style={{ display: 'none' }}
      />

      <Button
        variant={variant}
        size={size}
        onClick={handleClick}
        disabled={isLoading}
        className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
      >
        {isLoading ? (
          <>
            <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            Carregando...
          </>
        ) : (
          <>
            {enableAdvancedImport ? (
              <Settings className="h-4 w-4 mr-2" />
            ) : (
              <Upload className="h-4 w-4 mr-2" />
            )}
            {enableAdvancedImport ? 'Importação Avançada' : 'Importar Excel'}
          </>
        )}
      </Button>

      {/* Dialog para seleção de template */}
      {allowTemplates && (
        <Dialog open={showTemplateSelector} onOpenChange={setShowTemplateSelector}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                Selecionar Template de Importação
              </DialogTitle>
            </DialogHeader>
            <ImportTemplateSelector
              onTemplateSelect={(templateId) => {
                setSelectedTemplate(templateId);
                setShowTemplateSelector(false);
                fileInputRef.current?.click();
              }}
              onSkip={() => {
                setSelectedTemplate(null);
                setShowTemplateSelector(false);
                fileInputRef.current?.click();
              }}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Dialog para mapeamento de colunas */}
      {enableAdvancedImport && pendingFile && (
        <ColumnMappingDialog
          open={showColumnMapping}
          onOpenChange={setShowColumnMapping}
          file={pendingFile}
          template={selectedTemplate}
          onConfirm={(mapping) => {
            setShowColumnMapping(false);
            processFile(pendingFile, mapping);
            setPendingFile(null);
          }}
          onCancel={() => {
            setShowColumnMapping(false);
            setPendingFile(null);
          }}
        />
      )}

      {/* Dialog de Progresso */}
      {enableAdvancedImport && (
        <Dialog open={showProgress} onOpenChange={setShowProgress}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Progresso da Importação</DialogTitle>
            </DialogHeader>
            <ProgressTracker
              steps={progressTracker.steps}
              currentStep={progressTracker.currentStep}
              overallProgress={progressTracker.overallProgress}
              isComplete={progressTracker.isComplete}
              hasError={progressTracker.hasError}
              onCancel={() => {
                setShowProgress(false);
                // TODO: Implementar cancelamento da importação
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
