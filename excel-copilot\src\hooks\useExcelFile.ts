import { useState } from 'react';
import { toast } from 'sonner';

// Importar implementações reais de Excel
import {
  createExcelFile,
  downloadExcelFile,
  parseExcelFile,
  isValidExcelFile,
  exportToCSV
} from '@/lib/excel';

interface ExcelSheet {
  name: string;
  data: unknown;
}

type ExportFormat = 'xlsx' | 'csv';

/**
 * Hook para facilitar operações com arquivos Excel
 */
export function useExcelFile() {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Importa um arquivo Excel com funcionalidades avançadas
   * @param file Arquivo a ser importado
   * @param options Opções de importação
   * @returns Promise com os dados das planilhas ou null em caso de erro
   */
  const importExcel = async (
    file: File,
    options: {
      onSuccess?: (data: { fileName: string; sheets: ExcelSheet[] }) => void;
      maxSize?: number; // em bytes
      trackAnalytics?: boolean;
      template?: string | null; // Template de importação
      validateSchema?: boolean; // Validar schema dos dados
      transformData?: boolean; // Aplicar transformações
      onProgress?: (progress: number) => void; // Callback de progresso
    } = {}
  ): Promise<{ fileName: string; sheets: ExcelSheet[] } | null> => {
    if (!file) return null;

    const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB default

    setIsLoading(true);
    const toastId = toast.loading(`Processando arquivo ${file.name}...`);

    try {
      // Verificações básicas
      if (!isValidExcelFile(file)) {
        throw new Error('Formato inválido: envie um arquivo Excel (.xlsx ou .xls)');
      }

      if (file.size > maxSize) {
        throw new Error(
          `Arquivo muito grande: o tamanho máximo é ${(maxSize / (1024 * 1024)).toFixed(0)}MB`
        );
      }

      // Callback de progresso inicial
      options.onProgress?.(10);

      // Processar o arquivo Excel
      const sheets = await parseExcelFile(file);
      options.onProgress?.(50);

      // Verificar se obteve dados
      if (!sheets || sheets.length === 0) {
        throw new Error('O arquivo não contém dados válidos');
      }

      // Aplicar validação de schema se solicitado
      if (options.validateSchema && options.template) {
        await validateDataSchema(sheets, options.template);
        options.onProgress?.(70);
      }

      // Aplicar transformações se solicitado
      if (options.transformData && options.template) {
        await transformDataByTemplate(sheets, options.template);
        options.onProgress?.(90);
      }

      // Sucesso
      toast.success(`${file.name} carregado com sucesso!`, {
        id: toastId,
        duration: 3000,
      });

      // Progresso final
      options.onProgress?.(100);

      // Registrar evento de importação (analytics)
      if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
        const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
        const gtag = windowWithGtag.gtag;
        if (typeof gtag === 'function') {
          gtag('event', 'import_excel_advanced', {
            file_size: file.size,
            file_type: file.type,
            sheet_count: sheets.length,
            template_used: options.template || 'none',
            validation_enabled: options.validateSchema || false,
            transformation_enabled: options.transformData || false,
          });
        }
      }

      const result = {
        fileName: file.name,
        sheets: sheets,
      };

      // Callback de sucesso se fornecido
      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error) {
      console.error('Erro ao processar arquivo Excel:', error);
      toast.error('Erro ao importar arquivo', {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : 'Não foi possível processar o arquivo. Tente novamente.',
        duration: 4000,
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Exporta dados para um arquivo Excel ou CSV
   * @param sheets Array de planilhas com dados
   * @param fileName Nome do arquivo sem extensão
   * @param format Formato de exportação (xlsx ou csv)
   * @param options Opções adicionais
   * @returns Promise<boolean> indicando sucesso ou falha
   */
  const exportExcel = async (
    sheets: ExcelSheet[],
    fileName: string,
    format: ExportFormat = 'xlsx',
    options: {
      trackAnalytics?: boolean;
      workbookId?: string;
    } = {}
  ): Promise<boolean> => {
    setIsLoading(true);

    // Remover caracteres especiais e espaços do nome do arquivo
    const safeFileName = fileName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const timestamp = Date.now();
    const fullFileName = `${safeFileName}_${timestamp}`;

    const toastId = toast.loading(`Preparando exportação ${format.toUpperCase()}...`);

    try {
      // Verificar se temos pelo menos uma planilha com dados
      if (!sheets || sheets.length === 0) {
        throw new Error('Não há dados para exportar');
      }

      // Exportar com base no formato
      if (format === 'xlsx') {
        // Gerar o arquivo Excel
        const blob = await createExcelFile(sheets, fileName);

        // Fazer download do arquivo
        downloadExcelFile(blob, `${fullFileName}.xlsx`);

        // Registrar evento de exportação (analytics)
        if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
          const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
          const gtag = windowWithGtag.gtag;
          if (typeof gtag === 'function') {
            gtag('event', 'export_excel', {
              workbook_id: options.workbookId,
              sheet_count: sheets.length,
              format: 'xlsx',
            });
          }
        }
      } else if (format === 'csv') {
        // Exportar como CSV
        exportToCSV(sheets, fullFileName);

        // Registrar evento de exportação (analytics)
        if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
          const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
          const gtag = windowWithGtag.gtag;
          if (typeof gtag === 'function') {
            gtag('event', 'export_csv', {
              workbook_id: options.workbookId,
              sheet_count: sheets.length,
              format: 'csv',
            });
          }
        }
      }

      // Mostrar mensagem de sucesso
      toast.success(`Exportação ${format.toUpperCase()} concluída`, {
        id: toastId,
        description: `Arquivo "${fullFileName}.${format}" baixado com sucesso!`,
        duration: 3000,
      });

      return true;
    } catch (error) {
      console.error(`Erro ao exportar ${format}:`, error);
      toast.error(`Erro na exportação ${format.toUpperCase()}`, {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : `Não foi possível exportar para ${format.toUpperCase()}. Tente novamente.`,
        duration: 4000,
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    importExcel,
    exportExcel,
  };
}

// Funções auxiliares para funcionalidades avançadas

/**
 * Valida schema dos dados baseado no template
 */
async function validateDataSchema(sheets: ExcelSheet[], template: string): Promise<void> {
  const templateRules = getTemplateValidationRules(template);

  for (const sheet of sheets) {
    if (!Array.isArray(sheet.data)) continue;

    for (const row of sheet.data) {
      for (const [column, rules] of Object.entries(templateRules)) {
        if (rules.required && (row[column] === undefined || row[column] === null || row[column] === '')) {
          throw new Error(`Campo obrigatório '${column}' está vazio`);
        }

        if (row[column] !== undefined && row[column] !== null && row[column] !== '') {
          if (rules.type === 'number' && isNaN(Number(row[column]))) {
            throw new Error(`Campo '${column}' deve ser um número`);
          }

          if (rules.type === 'email' && !isValidEmail(String(row[column]))) {
            throw new Error(`Campo '${column}' deve ser um email válido`);
          }

          if (rules.enum && !rules.enum.includes(row[column])) {
            throw new Error(`Campo '${column}' deve ser um dos valores: ${rules.enum.join(', ')}`);
          }
        }
      }
    }
  }
}

/**
 * Aplica transformações nos dados baseado no template
 */
async function transformDataByTemplate(sheets: ExcelSheet[], template: string): Promise<void> {
  const transformations = getTemplateTransformations(template);

  for (const sheet of sheets) {
    if (!Array.isArray(sheet.data)) continue;

    sheet.data = sheet.data.map((row: any) => {
      const transformedRow = { ...row };

      for (const [column, transformation] of Object.entries(transformations)) {
        if (transformedRow[column] !== undefined) {
          switch (transformation.type) {
            case 'currency':
              transformedRow[column] = formatCurrency(transformedRow[column]);
              break;
            case 'date':
              transformedRow[column] = formatDate(transformedRow[column]);
              break;
            case 'uppercase':
              transformedRow[column] = String(transformedRow[column]).toUpperCase();
              break;
            case 'lowercase':
              transformedRow[column] = String(transformedRow[column]).toLowerCase();
              break;
          }
        }
      }

      return transformedRow;
    });
  }
}

/**
 * Obtém regras de validação para um template
 */
function getTemplateValidationRules(template: string): Record<string, any> {
  const rules: Record<string, Record<string, any>> = {
    'financial-expenses': {
      'Data': { required: true, type: 'date' },
      'Valor': { required: true, type: 'number' },
      'Tipo': { required: true, enum: ['Receita', 'Despesa'] }
    },
    'sales-data': {
      'Data': { required: true, type: 'date' },
      'Quantidade': { required: true, type: 'number' },
      'Valor Unitário': { required: true, type: 'number' }
    },
    'employee-data': {
      'Nome': { required: true, type: 'string' },
      'Email': { required: true, type: 'email' },
      'Data Admissão': { required: true, type: 'date' }
    }
  };

  return rules[template] || {};
}

/**
 * Obtém transformações para um template
 */
function getTemplateTransformations(template: string): Record<string, any> {
  const transformations: Record<string, Record<string, any>> = {
    'financial-expenses': {
      'Valor': { type: 'currency' },
      'Data': { type: 'date' }
    },
    'sales-data': {
      'Valor Unitário': { type: 'currency' },
      'Total': { type: 'currency' },
      'Data': { type: 'date' }
    },
    'employee-data': {
      'Nome': { type: 'uppercase' },
      'Email': { type: 'lowercase' },
      'Data Admissão': { type: 'date' }
    }
  };

  return transformations[template] || {};
}

/**
 * Valida se é um email válido
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Formata valor como moeda
 */
function formatCurrency(value: any): string {
  const num = Number(value);
  if (isNaN(num)) return String(value);
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(num);
}

/**
 * Formata data
 */
function formatDate(value: any): string {
  try {
    const date = new Date(value);
    if (isNaN(date.getTime())) return String(value);
    return date.toLocaleDateString('pt-BR');
  } catch {
    return String(value);
  }
}
