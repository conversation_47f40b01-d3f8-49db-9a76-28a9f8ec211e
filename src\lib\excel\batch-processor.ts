import Bull, { Queue, Job, JobOptions } from 'bull';
import { ImportTemplate, TemplateApplicationResult } from './import-templates';
import { TemplateProcessor } from './template-processor';

/**
 * Sistema de Filas para Processamento em Lote de Arquivos Excel
 * Utiliza Bull para gerenciar filas de processamento assíncrono
 */

// Configuração do Redis para Bull (usando variáveis de ambiente)
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
};

// Tipos para jobs de processamento
export interface ExcelProcessingJobData {
  jobId: string;
  userId: string;
  workbookId: string;
  fileName: string;
  templateId: string;
  excelData: { name: string; data: any[][] }[];
  sheetIndex: number;
  options: {
    priority: 'low' | 'normal' | 'high' | 'critical';
    retryAttempts: number;
    timeout: number; // em milissegundos
    notifyOnComplete: boolean;
    notifyOnError: boolean;
  };
  metadata: {
    fileSize: number;
    rowCount: number;
    columnCount: number;
    createdAt: Date;
  };
}

export interface ExcelProcessingJobResult {
  jobId: string;
  success: boolean;
  result?: TemplateApplicationResult;
  error?: string;
  processingTime: number;
  completedAt: Date;
}

export interface BatchProcessingStats {
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  activeJobs: number;
  waitingJobs: number;
  delayedJobs: number;
  averageProcessingTime: number;
  successRate: number;
}

/**
 * Classe principal para gerenciar processamento em lote
 */
export class ExcelBatchProcessor {
  private queue: Queue<ExcelProcessingJobData>;
  private templateProcessor: TemplateProcessor;
  private isInitialized = false;

  constructor() {
    this.templateProcessor = new TemplateProcessor();
    
    // Inicializar fila apenas se Redis estiver disponível
    try {
      this.queue = new Bull('excel-processing', {
        redis: REDIS_CONFIG,
        defaultJobOptions: {
          removeOnComplete: 100, // Manter apenas os últimos 100 jobs completos
          removeOnFail: 50, // Manter apenas os últimos 50 jobs falhados
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });
      
      this.setupJobProcessors();
      this.setupEventHandlers();
      this.isInitialized = true;
    } catch (error) {
      console.warn('Redis não disponível, processamento será síncrono:', error);
      this.isInitialized = false;
    }
  }

  /**
   * Verificar se o processador está inicializado
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Adicionar job de processamento à fila
   */
  async addProcessingJob(
    jobData: ExcelProcessingJobData,
    priority: number = 0
  ): Promise<string> {
    if (!this.isInitialized) {
      // Fallback para processamento síncrono
      return this.processSynchronously(jobData);
    }

    try {
      const jobOptions: JobOptions = {
        priority,
        attempts: jobData.options.retryAttempts,
        timeout: jobData.options.timeout,
        delay: 0,
      };

      const job = await this.queue.add(jobData, jobOptions);
      return job.id?.toString() || jobData.jobId;
    } catch (error) {
      console.error('Erro ao adicionar job à fila:', error);
      // Fallback para processamento síncrono
      return this.processSynchronously(jobData);
    }
  }

  /**
   * Processar job de forma síncrona (fallback)
   */
  private async processSynchronously(jobData: ExcelProcessingJobData): Promise<string> {
    try {
      const startTime = Date.now();
      
      // Simular processamento assíncrono
      const result = await this.processExcelJob(jobData);
      
      const processingTime = Date.now() - startTime;
      
      // Notificar resultado (implementar webhook ou callback)
      if (jobData.options.notifyOnComplete) {
        await this.notifyJobCompletion({
          jobId: jobData.jobId,
          success: true,
          result,
          processingTime,
          completedAt: new Date(),
        });
      }
      
      return jobData.jobId;
    } catch (error) {
      if (jobData.options.notifyOnError) {
        await this.notifyJobCompletion({
          jobId: jobData.jobId,
          success: false,
          error: error instanceof Error ? error.message : String(error),
          processingTime: 0,
          completedAt: new Date(),
        });
      }
      throw error;
    }
  }

  /**
   * Configurar processadores de jobs
   */
  private setupJobProcessors(): void {
    if (!this.queue) return;

    // Processador principal
    this.queue.process('*', async (job: Job<ExcelProcessingJobData>) => {
      const startTime = Date.now();
      
      try {
        const result = await this.processExcelJob(job.data);
        
        const processingTime = Date.now() - startTime;
        
        return {
          jobId: job.data.jobId,
          success: true,
          result,
          processingTime,
          completedAt: new Date(),
        } as ExcelProcessingJobResult;
      } catch (error) {
        const processingTime = Date.now() - startTime;
        
        throw {
          jobId: job.data.jobId,
          success: false,
          error: error instanceof Error ? error.message : String(error),
          processingTime,
          completedAt: new Date(),
        } as ExcelProcessingJobResult;
      }
    });
  }

  /**
   * Configurar handlers de eventos
   */
  private setupEventHandlers(): void {
    if (!this.queue) return;

    this.queue.on('completed', async (job: Job, result: ExcelProcessingJobResult) => {
      console.log(`Job ${job.id} completado em ${result.processingTime}ms`);
      
      if (job.data.options.notifyOnComplete) {
        await this.notifyJobCompletion(result);
      }
    });

    this.queue.on('failed', async (job: Job, error: Error) => {
      console.error(`Job ${job.id} falhou:`, error);
      
      if (job.data.options.notifyOnError) {
        await this.notifyJobCompletion({
          jobId: job.data.jobId,
          success: false,
          error: error.message,
          processingTime: 0,
          completedAt: new Date(),
        });
      }
    });

    this.queue.on('stalled', (job: Job) => {
      console.warn(`Job ${job.id} travado, será reprocessado`);
    });
  }

  /**
   * Processar job de Excel
   */
  private async processExcelJob(jobData: ExcelProcessingJobData): Promise<TemplateApplicationResult> {
    // Simular template (em produção, buscar do banco de dados)
    const template: ImportTemplate = {
      id: jobData.templateId,
      name: 'Template Dinâmico',
      version: '1.0.0',
      createdAt: new Date(),
      updatedAt: new Date(),
      settings: {
        skipRows: 0,
        headerRow: 1,
        allowEmptyRows: false,
        stopOnError: false,
        batchSize: 1000,
      },
      columnMappings: [],
      globalValidations: [],
      postProcessing: [],
    };

    return await this.templateProcessor.applyTemplate(
      template,
      jobData.excelData,
      jobData.sheetIndex
    );
  }

  /**
   * Notificar conclusão de job
   */
  private async notifyJobCompletion(result: ExcelProcessingJobResult): Promise<void> {
    try {
      // Implementar notificação via webhook, email, ou callback
      console.log('Notificação de job:', result);
      
      // Exemplo: enviar para webhook
      if (process.env.WEBHOOK_URL) {
        const response = await fetch(process.env.WEBHOOK_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(result),
        });
        
        if (!response.ok) {
          console.error('Erro ao enviar webhook:', response.statusText);
        }
      }
    } catch (error) {
      console.error('Erro ao notificar conclusão de job:', error);
    }
  }

  /**
   * Obter status de um job
   */
  async getJobStatus(jobId: string): Promise<{
    status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'unknown';
    progress?: number;
    result?: any;
    error?: string;
  }> {
    if (!this.isInitialized || !this.queue) {
      return { status: 'unknown' };
    }

    try {
      const job = await this.queue.getJob(jobId);
      if (!job) {
        return { status: 'unknown' };
      }

      const state = await job.getState();
      const progress = job.progress();

      return {
        status: state as any,
        progress: typeof progress === 'number' ? progress : undefined,
        result: job.returnvalue,
        error: job.failedReason,
      };
    } catch (error) {
      console.error('Erro ao obter status do job:', error);
      return { status: 'unknown' };
    }
  }

  /**
   * Cancelar job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    if (!this.isInitialized || !this.queue) {
      return false;
    }

    try {
      const job = await this.queue.getJob(jobId);
      if (job) {
        await job.remove();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao cancelar job:', error);
      return false;
    }
  }

  /**
   * Obter estatísticas da fila
   */
  async getQueueStats(): Promise<BatchProcessingStats> {
    if (!this.isInitialized || !this.queue) {
      return {
        totalJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        activeJobs: 0,
        waitingJobs: 0,
        delayedJobs: 0,
        averageProcessingTime: 0,
        successRate: 0,
      };
    }

    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
      ]);

      const totalJobs = waiting.length + active.length + completed.length + failed.length + delayed.length;
      const successRate = totalJobs > 0 ? (completed.length / totalJobs) * 100 : 0;

      // Calcular tempo médio de processamento
      const completedWithTimes = completed.filter(job => job.returnvalue?.processingTime);
      const averageProcessingTime = completedWithTimes.length > 0
        ? completedWithTimes.reduce((sum, job) => sum + job.returnvalue.processingTime, 0) / completedWithTimes.length
        : 0;

      return {
        totalJobs,
        completedJobs: completed.length,
        failedJobs: failed.length,
        activeJobs: active.length,
        waitingJobs: waiting.length,
        delayedJobs: delayed.length,
        averageProcessingTime,
        successRate,
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas da fila:', error);
      return {
        totalJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        activeJobs: 0,
        waitingJobs: 0,
        delayedJobs: 0,
        averageProcessingTime: 0,
        successRate: 0,
      };
    }
  }

  /**
   * Limpar fila
   */
  async clearQueue(): Promise<void> {
    if (!this.isInitialized || !this.queue) {
      return;
    }

    try {
      await this.queue.empty();
      console.log('Fila limpa com sucesso');
    } catch (error) {
      console.error('Erro ao limpar fila:', error);
    }
  }

  /**
   * Fechar conexões
   */
  async close(): Promise<void> {
    if (this.queue) {
      await this.queue.close();
    }
  }
}

// Instância global do processador em lote
export const batchProcessor = new ExcelBatchProcessor();
