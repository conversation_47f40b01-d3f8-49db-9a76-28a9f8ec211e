import { z } from 'zod';

/**
 * Sistema de Templates de Importação Avançado
 * Permite definir templates para mapear e validar dados de arquivos Excel
 */

// Schema de validação para colunas
export const ColumnMappingSchema = z.object({
  sourceColumn: z.string().min(1, 'Nome da coluna de origem é obrigatório'),
  targetField: z.string().min(1, 'Campo de destino é obrigatório'),
  dataType: z.enum(['string', 'number', 'date', 'boolean', 'email', 'phone', 'currency']),
  required: z.boolean().default(false),
  defaultValue: z.any().optional(),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
    customValidator: z.string().optional(), // Nome da função de validação customizada
  }).optional(),
  transformation: z.object({
    type: z.enum(['uppercase', 'lowercase', 'trim', 'format_date', 'format_currency', 'custom']),
    customFunction: z.string().optional(), // Nome da função de transformação customizada
  }).optional(),
});

export const ImportTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Nome do template é obrigatório'),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  
  // Configurações de importação
  settings: z.object({
    skipRows: z.number().default(0), // Pular linhas no início
    headerRow: z.number().default(1), // Linha que contém os cabeçalhos
    maxRows: z.number().optional(), // Máximo de linhas para processar
    allowEmptyRows: z.boolean().default(false),
    stopOnError: z.boolean().default(true),
    batchSize: z.number().default(1000), // Tamanho do lote para processamento
  }),
  
  // Mapeamento de colunas
  columnMappings: z.array(ColumnMappingSchema),
  
  // Validações globais
  globalValidations: z.array(z.object({
    name: z.string(),
    description: z.string(),
    validatorFunction: z.string(), // Nome da função de validação
  })).default([]),
  
  // Transformações pós-importação
  postProcessing: z.array(z.object({
    name: z.string(),
    description: z.string(),
    processorFunction: z.string(), // Nome da função de processamento
  })).default([]),
});

export type ColumnMapping = z.infer<typeof ColumnMappingSchema>;
export type ImportTemplate = z.infer<typeof ImportTemplateSchema>;

/**
 * Resultado da aplicação de um template
 */
export interface TemplateApplicationResult {
  success: boolean;
  processedRows: number;
  validRows: number;
  errorRows: number;
  warnings: string[];
  errors: string[];
  data: Record<string, any>[];
  skippedRows: number[];
  errorDetails: Array<{
    row: number;
    column?: string;
    error: string;
    value?: any;
  }>;
}

/**
 * Templates pré-definidos para casos comuns
 */
export const PREDEFINED_TEMPLATES: ImportTemplate[] = [
  {
    id: 'financial-data',
    name: 'Dados Financeiros',
    description: 'Template para importação de dados financeiros com receitas, despesas e categorias',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
    settings: {
      skipRows: 0,
      headerRow: 1,
      allowEmptyRows: false,
      stopOnError: false,
      batchSize: 500,
    },
    columnMappings: [
      {
        sourceColumn: 'Data',
        targetField: 'date',
        dataType: 'date',
        required: true,
        validation: {
          pattern: '^\\d{2}/\\d{2}/\\d{4}$',
        },
        transformation: {
          type: 'format_date',
        },
      },
      {
        sourceColumn: 'Descrição',
        targetField: 'description',
        dataType: 'string',
        required: true,
        transformation: {
          type: 'trim',
        },
      },
      {
        sourceColumn: 'Valor',
        targetField: 'amount',
        dataType: 'currency',
        required: true,
        validation: {
          min: 0,
        },
        transformation: {
          type: 'format_currency',
        },
      },
      {
        sourceColumn: 'Categoria',
        targetField: 'category',
        dataType: 'string',
        required: true,
        transformation: {
          type: 'trim',
        },
      },
      {
        sourceColumn: 'Tipo',
        targetField: 'type',
        dataType: 'string',
        required: true,
        defaultValue: 'receita',
      },
    ],
    globalValidations: [
      {
        name: 'validate_balance',
        description: 'Validar se o balanço está correto',
        validatorFunction: 'validateFinancialBalance',
      },
    ],
    postProcessing: [
      {
        name: 'calculate_totals',
        description: 'Calcular totais por categoria',
        processorFunction: 'calculateCategoryTotals',
      },
    ],
  },
  {
    id: 'customer-data',
    name: 'Dados de Clientes',
    description: 'Template para importação de dados de clientes com validação de email e telefone',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
    settings: {
      skipRows: 0,
      headerRow: 1,
      allowEmptyRows: false,
      stopOnError: false,
      batchSize: 1000,
    },
    columnMappings: [
      {
        sourceColumn: 'Nome',
        targetField: 'name',
        dataType: 'string',
        required: true,
        transformation: {
          type: 'trim',
        },
      },
      {
        sourceColumn: 'Email',
        targetField: 'email',
        dataType: 'email',
        required: true,
        validation: {
          pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
        },
        transformation: {
          type: 'lowercase',
        },
      },
      {
        sourceColumn: 'Telefone',
        targetField: 'phone',
        dataType: 'phone',
        required: false,
        validation: {
          pattern: '^\\(?\\d{2}\\)?\\s?\\d{4,5}-?\\d{4}$',
        },
      },
      {
        sourceColumn: 'Empresa',
        targetField: 'company',
        dataType: 'string',
        required: false,
        transformation: {
          type: 'trim',
        },
      },
      {
        sourceColumn: 'Data Cadastro',
        targetField: 'registrationDate',
        dataType: 'date',
        required: false,
        defaultValue: new Date().toISOString(),
      },
    ],
    globalValidations: [
      {
        name: 'validate_unique_emails',
        description: 'Validar se não há emails duplicados',
        validatorFunction: 'validateUniqueEmails',
      },
    ],
    postProcessing: [],
  },
  {
    id: 'inventory-data',
    name: 'Dados de Estoque',
    description: 'Template para importação de dados de estoque com validação de quantidades',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
    settings: {
      skipRows: 0,
      headerRow: 1,
      allowEmptyRows: false,
      stopOnError: false,
      batchSize: 2000,
    },
    columnMappings: [
      {
        sourceColumn: 'Código',
        targetField: 'code',
        dataType: 'string',
        required: true,
        transformation: {
          type: 'uppercase',
        },
      },
      {
        sourceColumn: 'Produto',
        targetField: 'product',
        dataType: 'string',
        required: true,
        transformation: {
          type: 'trim',
        },
      },
      {
        sourceColumn: 'Quantidade',
        targetField: 'quantity',
        dataType: 'number',
        required: true,
        validation: {
          min: 0,
        },
      },
      {
        sourceColumn: 'Preço Unitário',
        targetField: 'unitPrice',
        dataType: 'currency',
        required: true,
        validation: {
          min: 0,
        },
        transformation: {
          type: 'format_currency',
        },
      },
      {
        sourceColumn: 'Categoria',
        targetField: 'category',
        dataType: 'string',
        required: true,
        transformation: {
          type: 'trim',
        },
      },
    ],
    globalValidations: [
      {
        name: 'validate_unique_codes',
        description: 'Validar se não há códigos duplicados',
        validatorFunction: 'validateUniqueCodes',
      },
    ],
    postProcessing: [
      {
        name: 'calculate_total_value',
        description: 'Calcular valor total do estoque',
        processorFunction: 'calculateInventoryValue',
      },
    ],
  },
];

/**
 * Classe para gerenciar templates de importação
 */
export class ImportTemplateManager {
  private templates: Map<string, ImportTemplate> = new Map();

  constructor() {
    // Carregar templates pré-definidos
    PREDEFINED_TEMPLATES.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Adicionar um novo template
   */
  addTemplate(template: ImportTemplate): void {
    const validatedTemplate = ImportTemplateSchema.parse(template);
    this.templates.set(validatedTemplate.id, validatedTemplate);
  }

  /**
   * Obter um template por ID
   */
  getTemplate(id: string): ImportTemplate | undefined {
    return this.templates.get(id);
  }

  /**
   * Listar todos os templates
   */
  listTemplates(): ImportTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Remover um template
   */
  removeTemplate(id: string): boolean {
    return this.templates.delete(id);
  }

  /**
   * Atualizar um template existente
   */
  updateTemplate(id: string, updates: Partial<ImportTemplate>): boolean {
    const existing = this.templates.get(id);
    if (!existing) return false;

    const updated = { ...existing, ...updates, updatedAt: new Date() };
    const validatedTemplate = ImportTemplateSchema.parse(updated);
    this.templates.set(id, validatedTemplate);
    return true;
  }

  /**
   * Buscar templates por nome ou descrição
   */
  searchTemplates(query: string): ImportTemplate[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.templates.values()).filter(
      template =>
        template.name.toLowerCase().includes(lowerQuery) ||
        template.description?.toLowerCase().includes(lowerQuery)
    );
  }
}

// Instância global do gerenciador de templates
export const templateManager = new ImportTemplateManager();
