import { ImportTemplate, ColumnMapping, TemplateApplicationResult } from './import-templates';

/**
 * Processador de Templates de Importação
 * Aplica templates para validar e transformar dados de Excel
 */

/**
 * Funções de validação customizadas
 */
const VALIDATION_FUNCTIONS: Record<string, (value: any, data: any[]) => boolean> = {
  validateFinancialBalance: (value: any, data: any[]) => {
    // Validar se o balanço financeiro está correto
    const receitas = data.filter(row => row.type === 'receita').reduce((sum, row) => sum + (row.amount || 0), 0);
    const despesas = data.filter(row => row.type === 'despesa').reduce((sum, row) => sum + (row.amount || 0), 0);
    return receitas >= despesas; // Receitas devem ser >= despesas
  },
  
  validateUniqueEmails: (value: any, data: any[]) => {
    // Validar se não há emails duplicados
    const emails = data.map(row => row.email).filter(Boolean);
    const uniqueEmails = new Set(emails);
    return emails.length === uniqueEmails.size;
  },
  
  validateUniqueCodes: (value: any, data: any[]) => {
    // Validar se não há códigos duplicados
    const codes = data.map(row => row.code).filter(Boolean);
    const uniqueCodes = new Set(codes);
    return codes.length === uniqueCodes.size;
  },
};

/**
 * Funções de transformação customizadas
 */
const TRANSFORMATION_FUNCTIONS: Record<string, (value: any) => any> = {
  uppercase: (value: any) => String(value || '').toUpperCase(),
  lowercase: (value: any) => String(value || '').toLowerCase(),
  trim: (value: any) => String(value || '').trim(),
  
  format_date: (value: any) => {
    if (!value) return null;
    try {
      // Tentar diferentes formatos de data
      const dateStr = String(value);
      
      // Formato DD/MM/YYYY
      if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
        const [day, month, year] = dateStr.split('/');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day)).toISOString();
      }
      
      // Formato ISO
      const date = new Date(value);
      return isNaN(date.getTime()) ? null : date.toISOString();
    } catch {
      return null;
    }
  },
  
  format_currency: (value: any) => {
    if (!value) return 0;
    try {
      // Remover símbolos de moeda e converter para número
      const numStr = String(value).replace(/[R$\s.,]/g, '');
      const num = parseFloat(numStr) / 100; // Assumir que está em centavos
      return isNaN(num) ? 0 : num;
    } catch {
      return 0;
    }
  },
};

/**
 * Funções de pós-processamento
 */
const POST_PROCESSING_FUNCTIONS: Record<string, (data: any[]) => any[]> = {
  calculateCategoryTotals: (data: any[]) => {
    // Calcular totais por categoria
    const categoryTotals: Record<string, number> = {};
    
    data.forEach(row => {
      if (row.category && row.amount) {
        categoryTotals[row.category] = (categoryTotals[row.category] || 0) + row.amount;
      }
    });
    
    // Adicionar totais aos dados
    return data.map(row => ({
      ...row,
      categoryTotal: categoryTotals[row.category] || 0,
    }));
  },
  
  calculateInventoryValue: (data: any[]) => {
    // Calcular valor total do estoque
    return data.map(row => ({
      ...row,
      totalValue: (row.quantity || 0) * (row.unitPrice || 0),
    }));
  },
};

/**
 * Classe principal para processar templates
 */
export class TemplateProcessor {
  /**
   * Aplicar template aos dados de Excel
   */
  async applyTemplate(
    template: ImportTemplate,
    excelData: { name: string; data: any[][] }[],
    sheetIndex: number = 0
  ): Promise<TemplateApplicationResult> {
    const result: TemplateApplicationResult = {
      success: false,
      processedRows: 0,
      validRows: 0,
      errorRows: 0,
      warnings: [],
      errors: [],
      data: [],
      skippedRows: [],
      errorDetails: [],
    };

    try {
      // Verificar se há dados
      if (!excelData || excelData.length === 0) {
        result.errors.push('Nenhum dado encontrado no arquivo Excel');
        return result;
      }

      const sheet = excelData[sheetIndex];
      if (!sheet || !sheet.data || sheet.data.length === 0) {
        result.errors.push(`Planilha ${sheetIndex} não encontrada ou vazia`);
        return result;
      }

      const rawData = sheet.data;
      
      // Aplicar configurações do template
      const { settings } = template;
      
      // Pular linhas iniciais
      const dataStartIndex = settings.skipRows;
      
      // Obter cabeçalhos
      const headerRowIndex = settings.headerRow - 1; // Converter para índice baseado em 0
      if (headerRowIndex >= rawData.length) {
        result.errors.push('Linha de cabeçalho não encontrada');
        return result;
      }
      
      const headers = rawData[headerRowIndex].map(h => String(h || '').trim());
      
      // Processar dados linha por linha
      const dataRows = rawData.slice(Math.max(dataStartIndex, headerRowIndex + 1));
      
      // Limitar número de linhas se especificado
      const rowsToProcess = settings.maxRows 
        ? dataRows.slice(0, settings.maxRows)
        : dataRows;

      for (let i = 0; i < rowsToProcess.length; i++) {
        const rowIndex = i + Math.max(dataStartIndex, headerRowIndex + 1) + 1; // +1 para linha baseada em 1
        const row = rowsToProcess[i];
        
        result.processedRows++;
        
        // Verificar se a linha está vazia
        const isEmptyRow = row.every(cell => !cell || String(cell).trim() === '');
        if (isEmptyRow) {
          if (!settings.allowEmptyRows) {
            result.skippedRows.push(rowIndex);
            continue;
          }
        }
        
        // Aplicar mapeamento de colunas
        const mappedRow = await this.mapRowData(row, headers, template.columnMappings, rowIndex, result);
        
        if (mappedRow) {
          result.data.push(mappedRow);
          result.validRows++;
        } else {
          result.errorRows++;
          if (settings.stopOnError) {
            break;
          }
        }
      }
      
      // Aplicar validações globais
      await this.applyGlobalValidations(result.data, template, result);
      
      // Aplicar pós-processamento
      result.data = await this.applyPostProcessing(result.data, template);
      
      result.success = result.errorRows === 0 || !settings.stopOnError;
      
    } catch (error) {
      result.errors.push(`Erro durante o processamento: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    return result;
  }

  /**
   * Mapear dados de uma linha usando as configurações de coluna
   */
  private async mapRowData(
    row: any[],
    headers: string[],
    columnMappings: ColumnMapping[],
    rowIndex: number,
    result: TemplateApplicationResult
  ): Promise<Record<string, any> | null> {
    const mappedRow: Record<string, any> = {};
    let hasErrors = false;

    for (const mapping of columnMappings) {
      try {
        // Encontrar índice da coluna
        const columnIndex = headers.findIndex(h => h === mapping.sourceColumn);
        
        if (columnIndex === -1) {
          if (mapping.required) {
            result.errorDetails.push({
              row: rowIndex,
              column: mapping.sourceColumn,
              error: `Coluna obrigatória '${mapping.sourceColumn}' não encontrada`,
            });
            hasErrors = true;
            continue;
          } else {
            // Usar valor padrão se disponível
            mappedRow[mapping.targetField] = mapping.defaultValue;
            continue;
          }
        }
        
        // Obter valor da célula
        let cellValue = row[columnIndex];
        
        // Verificar se é obrigatório
        if (mapping.required && (cellValue === null || cellValue === undefined || String(cellValue).trim() === '')) {
          result.errorDetails.push({
            row: rowIndex,
            column: mapping.sourceColumn,
            error: `Valor obrigatório não fornecido`,
            value: cellValue,
          });
          hasErrors = true;
          continue;
        }
        
        // Aplicar valor padrão se vazio
        if ((cellValue === null || cellValue === undefined || String(cellValue).trim() === '') && mapping.defaultValue !== undefined) {
          cellValue = mapping.defaultValue;
        }
        
        // Aplicar transformações
        if (mapping.transformation && cellValue !== null && cellValue !== undefined) {
          cellValue = this.applyTransformation(cellValue, mapping.transformation);
        }
        
        // Aplicar validações
        if (mapping.validation && cellValue !== null && cellValue !== undefined) {
          const validationResult = this.validateValue(cellValue, mapping.validation, mapping.dataType);
          if (!validationResult.valid) {
            result.errorDetails.push({
              row: rowIndex,
              column: mapping.sourceColumn,
              error: validationResult.error || 'Valor inválido',
              value: cellValue,
            });
            hasErrors = true;
            continue;
          }
        }
        
        // Converter tipo de dados
        const convertedValue = this.convertDataType(cellValue, mapping.dataType);
        mappedRow[mapping.targetField] = convertedValue;
        
      } catch (error) {
        result.errorDetails.push({
          row: rowIndex,
          column: mapping.sourceColumn,
          error: `Erro no processamento: ${error instanceof Error ? error.message : String(error)}`,
          value: row[headers.findIndex(h => h === mapping.sourceColumn)],
        });
        hasErrors = true;
      }
    }
    
    return hasErrors ? null : mappedRow;
  }

  /**
   * Aplicar transformação a um valor
   */
  private applyTransformation(value: any, transformation: ColumnMapping['transformation']): any {
    if (!transformation) return value;
    
    const transformFunction = TRANSFORMATION_FUNCTIONS[transformation.type];
    if (transformFunction) {
      return transformFunction(value);
    }
    
    // Transformação customizada
    if (transformation.type === 'custom' && transformation.customFunction) {
      const customFunction = TRANSFORMATION_FUNCTIONS[transformation.customFunction];
      if (customFunction) {
        return customFunction(value);
      }
    }
    
    return value;
  }

  /**
   * Validar um valor
   */
  private validateValue(
    value: any, 
    validation: NonNullable<ColumnMapping['validation']>, 
    dataType: ColumnMapping['dataType']
  ): { valid: boolean; error?: string } {
    try {
      // Validação de tipo de dados
      if (dataType === 'number') {
        const num = Number(value);
        if (isNaN(num)) {
          return { valid: false, error: 'Valor deve ser um número' };
        }
        
        if (validation.min !== undefined && num < validation.min) {
          return { valid: false, error: `Valor deve ser maior ou igual a ${validation.min}` };
        }
        
        if (validation.max !== undefined && num > validation.max) {
          return { valid: false, error: `Valor deve ser menor ou igual a ${validation.max}` };
        }
      }
      
      // Validação de padrão
      if (validation.pattern) {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(String(value))) {
          return { valid: false, error: 'Valor não atende ao padrão exigido' };
        }
      }
      
      // Validação customizada
      if (validation.customValidator) {
        const customValidator = VALIDATION_FUNCTIONS[validation.customValidator];
        if (customValidator && !customValidator(value, [])) {
          return { valid: false, error: 'Falha na validação customizada' };
        }
      }
      
      return { valid: true };
    } catch (error) {
      return { valid: false, error: `Erro na validação: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * Converter tipo de dados
   */
  private convertDataType(value: any, dataType: ColumnMapping['dataType']): any {
    if (value === null || value === undefined) return value;
    
    switch (dataType) {
      case 'string':
        return String(value);
      case 'number':
        const num = Number(value);
        return isNaN(num) ? 0 : num;
      case 'boolean':
        return Boolean(value);
      case 'date':
        try {
          return new Date(value);
        } catch {
          return null;
        }
      case 'email':
        return String(value).toLowerCase().trim();
      case 'phone':
        return String(value).replace(/\D/g, ''); // Remover caracteres não numéricos
      case 'currency':
        const currencyNum = Number(String(value).replace(/[^\d.,]/g, '').replace(',', '.'));
        return isNaN(currencyNum) ? 0 : currencyNum;
      default:
        return value;
    }
  }

  /**
   * Aplicar validações globais
   */
  private async applyGlobalValidations(
    data: any[], 
    template: ImportTemplate, 
    result: TemplateApplicationResult
  ): Promise<void> {
    for (const validation of template.globalValidations) {
      try {
        const validationFunction = VALIDATION_FUNCTIONS[validation.validatorFunction];
        if (validationFunction) {
          const isValid = validationFunction(null, data);
          if (!isValid) {
            result.warnings.push(`Falha na validação global: ${validation.description}`);
          }
        }
      } catch (error) {
        result.warnings.push(`Erro na validação global '${validation.name}': ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  /**
   * Aplicar pós-processamento
   */
  private async applyPostProcessing(data: any[], template: ImportTemplate): Promise<any[]> {
    let processedData = [...data];
    
    for (const processor of template.postProcessing) {
      try {
        const processingFunction = POST_PROCESSING_FUNCTIONS[processor.processorFunction];
        if (processingFunction) {
          processedData = processingFunction(processedData);
        }
      } catch (error) {
        console.warn(`Erro no pós-processamento '${processor.name}':`, error);
      }
    }
    
    return processedData;
  }
}

// Instância global do processador
export const templateProcessor = new TemplateProcessor();
